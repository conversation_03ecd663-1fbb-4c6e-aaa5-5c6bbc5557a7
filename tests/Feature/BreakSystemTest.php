<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Company;
use App\Models\AgentBreak;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BreakSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $company;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test company
        $this->company = Company::create([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'slug' => 'test-company'
        ]);

        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'agent',
            'company_id' => $this->company->id,
        ]);
    }

    /** @test */
    public function user_can_start_a_break()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/start', [
                'break_type' => 'mola',
                'break_description' => 'Test break description'
            ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => '<PERSON><PERSON> başarıyla başlatıldı.'
            ]);

        $this->assertDatabaseHas('agent_breaks', [
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'break_type' => 'mola',
            'break_description' => 'Test break description',
            'ended_at' => null
        ]);
    }

    /** @test */
    public function user_cannot_start_multiple_breaks()
    {
        // Start first break
        AgentBreak::create([
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'break_type' => 'mola',
            'started_at' => now(),
        ]);

        // Try to start second break
        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/start', [
                'break_type' => 'toplanti',
                'break_description' => 'Another break'
            ]);

        $response->assertStatus(409)
            ->assertJson([
                'success' => false,
                'message' => 'Zaten aktif bir molanız bulunmaktadır.'
            ]);
    }

    /** @test */
    public function user_can_end_active_break()
    {
        // Create an active break
        $break = AgentBreak::create([
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'break_type' => 'mola',
            'started_at' => now()->subMinutes(10),
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/end');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Mola başarıyla sonlandırıldı.'
            ]);

        $break->refresh();
        $this->assertNotNull($break->ended_at);
        $this->assertNotNull($break->duration_seconds);
    }

    /** @test */
    public function user_cannot_end_break_when_none_active()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/end');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Aktif bir mola bulunamadı.'
            ]);
    }

    /** @test */
    public function user_can_get_break_status()
    {
        // Test when no active break
        $response = $this->actingAs($this->user)
            ->getJson('/api/breaks/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'has_active_break' => false,
                    'active_break' => null
                ]
            ]);

        // Create an active break
        $break = AgentBreak::create([
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'break_type' => 'mola',
            'started_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/breaks/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'has_active_break' => true
                ]
            ]);
    }

    /** @test */
    public function break_validation_works()
    {
        // Test missing break_type
        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/start', [
                'break_description' => 'Test description'
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Geçersiz veri gönderildi.'
            ]);

        // Test empty break_type
        $response = $this->actingAs($this->user)
            ->postJson('/api/breaks/start', [
                'break_type' => '',
                'break_description' => 'Test description'
            ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_break_endpoints()
    {
        $response = $this->postJson('/api/breaks/start', [
            'break_type' => 'mola'
        ]);

        $response->assertStatus(401);

        $response = $this->postJson('/api/breaks/end');
        $response->assertStatus(401);

        $response = $this->getJson('/api/breaks/status');
        $response->assertStatus(401);
    }
}
