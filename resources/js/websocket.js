const WebSocket = require('ws');
const fs = require('fs');
const https = require('https');
const express = require('express');
const app = express();

const options = {
  key: fs.readFileSync('mydomain.key'),
  cert: fs.readFileSync('chat_tamyeritamzamani_com.crt'),
  ca: fs.readFileSync('chat_tamyeritamzamani_com.ca-bundle'),  
};


// Express middlewares
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));
const server = https.createServer(options, app);

server.listen(22478, () => {
  console.log('HTTPS+WebSocket sunucusu 22478 portunda başlatıldı (SSL)');
});
// Bağlı kullanıcıları takip et

const wss = new WebSocket.Server({ server });
const connectedUsers = new Map();

wss.on('connection', (ws) => {
  console.log('Yeni kullanıcı bağlandı.');
  
  let userId = null;
  let connectionTime = Date.now();

  ws.on('message', (message) => {
    const messageStr = message.toString();
    
    if (messageStr === 'ping') {
      ws.send('pong');
      return;
    }
    
    console.log('Gelen mesaj:', messageStr);

    try {
      const data = JSON.parse(messageStr);
      if (data.type === 'auth' && data.userId) {
        userId = data.userId;
        connectedUsers.set(userId, {
          ws: ws,
          connectionTime: connectionTime,
          lastSeen: Date.now(),
          companyId: data.companyId,
          userName: data.userName,
          callState: 'off_call' // Default call state
        });

        ws.send(JSON.stringify({
          type: 'auth_success',
          message: 'Kimlik doğrulandı'
        }));

        console.log(`Kullanıcı ${userId} kimlik doğrulandı`);
        logConnectedUsers();
        broadcastAgentStatus(); // Bağlandığında mevcut durumu gönder
        return;
      }

      // Handle call state notifications
      if ((data.type === 'on_call' || data.type === 'off_call') && data.userId) {
        const user = connectedUsers.get(data.userId);
        if (user) {
          user.callState = data.type;
          user.lastSeen = Date.now();

          console.log(`Kullanıcı ${data.userId} (${data.userName}) çağrı durumu: ${data.type}`);

          // Broadcast updated agent status to all users in the same company
          broadcastAgentStatus();

          // Send confirmation back to the user
          ws.send(JSON.stringify({
            type: 'call_state_updated',
            callState: data.type,
            message: `Çağrı durumu güncellendi: ${data.type}`
          }));
        } else {
          console.warn(`Call state update received for unknown user: ${data.userId}`);
        }
        return;
      }
    } catch (e) {
      // JSON değilse normal mesaj olarak işle
    }
  });

  ws.on('close', () => {
    if (userId) {
      connectedUsers.delete(userId);
      console.log(`Kullanıcı ${userId} çıktı.`);
      broadcastAgentStatus(); // Çıktığında durumu güncelle
    } else {
      console.log('Kimliği belirsiz kullanıcı çıktı.');
    }
    logConnectedUsers();
  });

  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'WebSocket bağlantısı kuruldu'
  }));
});

// Agent durumlarını broadcast et
function broadcastAgentStatus() {
  connectedUsers.forEach((user) => {
    if (user.ws.readyState === WebSocket.OPEN) {
      // Aynı şirketteki kullanıcıları filtrele ve çağrı durumlarını dahil et
      const sameCompanyUsers = [];
      connectedUsers.forEach((otherUser, otherUserId) => {
        if (otherUser.companyId === user.companyId) {
          sameCompanyUsers.push({
            userId: otherUserId,
            userName: otherUser.userName,
            callState: otherUser.callState || 'off_call',
            lastSeen: otherUser.lastSeen,
            connectionTime: otherUser.connectionTime
          });
        }
      });

      const message = JSON.stringify({
        type: 'agent_status_update',
        agents: sameCompanyUsers
      });

      user.ws.send(message);
    }
  });

  console.log('Agent durumları gönderildi');
}

function logConnectedUsers() {
  console.log('\n=== Bağlı Kullanıcılar ===');
  connectedUsers.forEach((user, userId) => {
    const minutesOnline = Math.floor((Date.now() - user.connectionTime) / (1000 * 60));
    const callStatus = user.callState === 'on_call' ? '📞 ÇAĞRIDA' : '🟢 MÜSAIT';
    console.log(`User ID: ${userId} - Company ID: ${user.companyId} - ${user.userName} - ${minutesOnline} dakikadır online - ${callStatus}`);
  });
  console.log(`Toplam: ${connectedUsers.size} kullanıcı\n`);
}

// Her 5 saniyede bir bağlı kullanıcıları logla
setInterval(logConnectedUsers, 5000);

console.log('WebSocket sunucusu 22478 portunda başlatıldı');
