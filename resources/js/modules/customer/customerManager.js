/**
 * Customer Manager Module
 * Main module that coordinates all customer CRUD operations
 */

import CustomerCRUD from './crud.js';
import CustomerNotifications from './notifications.js';

class CustomerManager {
    constructor() {
        this.crud = new CustomerCRUD();
        this.notifications = CustomerNotifications;
        this.dataTable = null;
        this.currentModal = null;
        
        // Set up CRUD callbacks
        this.crud.setSuccessCallback((operation, response) => {
            this.handleSuccess(operation, response);
        });
        
        this.crud.setErrorCallback((operation, response) => {
            this.handleError(operation, response);
        });
    }

    /**
     * Initialize customer manager
     * @param {Object} options - Configuration options
     */
    init(options = {}) {
        this.options = {
            tableSelector: '#customerListTable',
            formSelector: '#addModal form',
            addButtonSelector: '.add-new-btn',
            deleteButtonSelector: '.delete-btn',
            editButtonSelector: '.edit-btn',
            ...options
        };

        this.initializeDataTable();
        this.bindEvents();
        this.loadCustomers();
    }

    /**
     * Initialize DataTable
     */
    initializeDataTable() {
        const tableElement = document.querySelector(this.options.tableSelector);
        if (tableElement && $.fn.DataTable) {
            this.dataTable = $(this.options.tableSelector).DataTable();
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add button
        const addButton = document.querySelector(this.options.addButtonSelector);
        if (addButton) {
            addButton.addEventListener('click', () => this.showCreateModal());
        }

        // Delete button
        const deleteButton = document.querySelector(this.options.deleteButtonSelector);
        if (deleteButton) {
            deleteButton.addEventListener('click', () => this.handleBulkDelete());
        }

        // Form submission
        const form = document.querySelector(this.options.formSelector);
        if (form) {
            this.crud.init(form);
            
            // Handle form submission
            const submitButton = form.querySelector('#stepNextBtn');
            if (submitButton) {
                submitButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleFormSubmit();
                });
            }
        }

        // Edit buttons (delegated event)
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-customer-btn')) {
                const customerId = e.target.closest('.edit-customer-btn').dataset.customerId;
                this.showEditModal(customerId);
            }
        });
    }

    /**
     * Load customers and update table
     */
    async loadCustomers() {
        try {
            const response = await this.crud.readAll();
            if (response.success) {
                this.updateTable(response.data);
            }
        } catch (error) {
            console.error('Error loading customers:', error);
            this.notifications.showError('Müşteri listesi yüklenirken bir hata oluştu.');
        }
    }

    /**
     * Show create modal
     */
    showCreateModal() {
        this.crud.reset();
        const modal = document.getElementById('addModal');
        if (modal) {
            // Update modal title
            const title = modal.querySelector('#addModalLabel');
            if (title) {
                title.textContent = 'Yeni Müşteri Kartı';
            }

            // Update button text
            const submitBtn = modal.querySelector('#stepNextBtn');
            if (submitBtn) {
                submitBtn.textContent = 'Kaydet';
            }

            this.currentModal = new bootstrap.Modal(modal);
            this.currentModal.show();
        }
    }

    /**
     * Show edit modal
     * @param {string|number} customerId - Customer ID to edit
     */
    async showEditModal(customerId) {
        const modal = document.getElementById('addModal');
        if (modal) {
            // Update modal title
            const title = modal.querySelector('#addModalLabel');
            if (title) {
                title.textContent = 'Müşteri Düzenle';
            }

            // Update button text
            const submitBtn = modal.querySelector('#stepNextBtn');
            if (submitBtn) {
                submitBtn.textContent = 'Güncelle';
            }

            // Show loading
            this.notifications.showLoading(submitBtn, 'Yükleniyor...');

            try {
                const response = await this.crud.loadForEdit(customerId);
                if (response.success) {
                    this.currentModal = new bootstrap.Modal(modal);
                    this.currentModal.show();
                } else {
                    this.notifications.showError(response.message);
                }
            } catch (error) {
                this.notifications.showError('Müşteri bilgileri yüklenirken bir hata oluştu.');
            } finally {
                this.notifications.hideLoading(submitBtn);
            }
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit() {
        const submitBtn = document.querySelector('#stepNextBtn');
        this.notifications.showLoading(submitBtn, 'Kaydediliyor...');

        try {
            const response = await this.crud.save();
            // Success/error handling is done in callbacks
        } catch (error) {
            this.notifications.showError('İşlem sırasında bir hata oluştu.');
        } finally {
            this.notifications.hideLoading(submitBtn);
        }
    }

    /**
     * Handle bulk delete
     */
    async handleBulkDelete() {
        const checkedBoxes = document.querySelectorAll(`${this.options.tableSelector} tbody input[type="checkbox"]:checked`);
        const customerIds = Array.from(checkedBoxes).map(checkbox => {
            const row = checkbox.closest('tr');
            return row.dataset.customerId || row.cells[1].textContent; // Assuming ID is in second column
        });

        if (customerIds.length === 0) {
            this.notifications.showWarning('Lütfen silinecek müşterileri seçin.');
            return;
        }

        const message = `${customerIds.length} müşteriyi silmek istediğinize emin misiniz?`;
        
        this.notifications.showConfirmation(message, async () => {
            try {
                const response = await this.crud.deleteMultiple(customerIds);
                // Success/error handling is done in callbacks
            } catch (error) {
                this.notifications.showError('Müşteriler silinirken bir hata oluştu.');
            }
        });
    }

    /**
     * Handle successful operations
     * @param {string} operation - Operation type
     * @param {Object} response - API response
     */
    handleSuccess(operation, response) {
        this.notifications.handleCRUDResponse(operation, response);

        // Close modal on successful create/update
        if ((operation === 'create' || operation === 'update') && this.currentModal) {
            this.currentModal.hide();
            this.currentModal = null;
        }

        // Reload table data
        if (['create', 'update', 'delete', 'deleteMultiple'].includes(operation)) {
            this.loadCustomers();
        }

        // Clear form after successful create
        if (operation === 'create') {
            this.crud.reset();
        }
    }

    /**
     * Handle operation errors
     * @param {string} operation - Operation type
     * @param {Object} response - API response
     */
    handleError(operation, response) {
        this.notifications.handleCRUDResponse(operation, response);

        // Display validation errors if available
        if (response.errors && Object.keys(response.errors).length > 0) {
            // Errors are already displayed by the form handler
            // But we can also show a general notification
            this.notifications.showError('Lütfen form hatalarını kontrol edin.');
        }
    }

    /**
     * Update table with new data
     * @param {Array} customers - Customer data array
     */
    updateTable(customers) {
        if (!this.dataTable) return;

        // Clear existing data
        this.dataTable.clear();

        // Add new data
        customers.forEach(customer => {
            const row = [
                `<input type="checkbox" class="row-check" data-customer-id="${customer.id}">`,
                customer.id,
                customer.first_name || '',
                customer.last_name || '',
                customer.phone || customer.gsm || '',
                customer.oid || '',
                customer.customer_type?.title || '',
                customer.customer_group?.title || '',
                customer.customer_criteria?.title || '',
                `<button class="btn btn-sm btn-primary edit-customer-btn" data-customer-id="${customer.id}">
                    <span class="iconify" data-icon="hugeicons:edit-02"></span>
                </button>`
            ];
            this.dataTable.row.add(row);
        });

        // Redraw table
        this.dataTable.draw();
    }

    /**
     * Get selected customer IDs
     * @returns {Array} Array of selected customer IDs
     */
    getSelectedCustomerIds() {
        const checkedBoxes = document.querySelectorAll(`${this.options.tableSelector} tbody input[type="checkbox"]:checked`);
        return Array.from(checkedBoxes).map(checkbox => {
            return checkbox.dataset.customerId;
        });
    }

    /**
     * Refresh customer list
     */
    refresh() {
        this.loadCustomers();
    }

    /**
     * Get customer by ID
     * @param {string|number} customerId - Customer ID
     * @returns {Promise<Object>} Customer data
     */
    async getCustomer(customerId) {
        return await this.crud.read(customerId);
    }

    /**
     * Create new customer
     * @param {Object} customerData - Customer data
     * @returns {Promise<Object>} Operation result
     */
    async createCustomer(customerData) {
        return await this.crud.create(customerData);
    }

    /**
     * Update customer
     * @param {string|number} customerId - Customer ID
     * @param {Object} customerData - Updated customer data
     * @returns {Promise<Object>} Operation result
     */
    async updateCustomer(customerId, customerData) {
        return await this.crud.update(customerId, customerData);
    }

    /**
     * Delete customer
     * @param {string|number} customerId - Customer ID
     * @returns {Promise<Object>} Operation result
     */
    async deleteCustomer(customerId) {
        return await this.crud.delete(customerId);
    }
}

export default CustomerManager;
