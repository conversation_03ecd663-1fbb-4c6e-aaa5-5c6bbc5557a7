/**
 * Customer API Service Module
 * Handles all HTTP requests for customer operations
 */

class CustomerAPI {
    constructor() {
        this.baseURL = '/api/customers';
        this.axios = window.axios;
        
        // Set up default headers
        this.axios.defaults.headers.common['Accept'] = 'application/json';
        this.axios.defaults.headers.common['Content-Type'] = 'application/json';
        
        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            this.axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }
    }

    /**
     * Get all customers
     * @returns {Promise<Object>} API response
     */
    async getAll() {
        try {
            const response = await this.axios.get(this.baseURL);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Get a single customer by ID
     * @param {string|number} id - Customer ID
     * @returns {Promise<Object>} API response
     */
    async getById(id) {
        try {
            const response = await this.axios.get(`${this.baseURL}/${id}`);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Create a new customer
     * @param {Object} customerData - Customer data object
     * @returns {Promise<Object>} API response
     */
    async create(customerData) {
        try {
            const response = await this.axios.post(this.baseURL, customerData);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Update an existing customer
     * @param {string|number} id - Customer ID
     * @param {Object} customerData - Updated customer data
     * @returns {Promise<Object>} API response
     */
    async update(id, customerData) {
        try {
            const response = await this.axios.put(`${this.baseURL}/${id}`, customerData);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Delete a customer
     * @param {string|number} id - Customer ID
     * @returns {Promise<Object>} API response
     */
    async delete(id) {
        try {
            const response = await this.axios.delete(`${this.baseURL}/${id}`);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Delete multiple customers
     * @param {Array} ids - Array of customer IDs
     * @returns {Promise<Object>} API response
     */
    async deleteMultiple(ids) {
        try {
            const response = await this.axios.delete(this.baseURL, {
                data: { ids: ids }
            });
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Handle successful API responses
     * @private
     * @param {Object} response - Axios response object
     * @returns {Object} Standardized response
     */
    _handleResponse(response) {
        return {
            success: true,
            data: response.data,
            status: response.status,
            message: response.data.message || 'İşlem başarılı'
        };
    }

    /**
     * Handle API errors
     * @private
     * @param {Object} error - Axios error object
     * @returns {Object} Standardized error response
     */
    _handleError(error) {
        console.error('Customer API Error:', error);

        if (error.response) {
            // Server responded with error status
            const { status, data } = error.response;
            
            return {
                success: false,
                status: status,
                message: data.message || 'Bir hata oluştu',
                errors: data.errors || {},
                data: data
            };
        } else if (error.request) {
            // Request was made but no response received
            return {
                success: false,
                status: 0,
                message: 'Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.',
                errors: {},
                data: null
            };
        } else {
            // Something else happened
            return {
                success: false,
                status: 0,
                message: 'Beklenmeyen bir hata oluştu.',
                errors: {},
                data: null
            };
        }
    }
}

// Export as singleton
export default new CustomerAPI();
