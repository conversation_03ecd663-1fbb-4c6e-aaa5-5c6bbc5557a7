/**
 * Customer CRUD Operations Module
 * Handles Create, Read, Update, Delete operations
 */

import CustomerAPI from './api.js';
import CustomerFormHandler from './formHandler.js';

class CustomerCRUD {
    constructor() {
        this.api = CustomerAPI;
        this.formHandler = new CustomerFormHandler();
        this.currentCustomerId = null;
        this.onSuccess = null;
        this.onError = null;
    }

    /**
     * Set success callback
     * @param {Function} callback - Success callback function
     */
    setSuccessCallback(callback) {
        this.onSuccess = callback;
    }

    /**
     * Set error callback
     * @param {Function} callback - Error callback function
     */
    setErrorCallback(callback) {
        this.onError = callback;
    }

    /**
     * Initialize CRUD operations with form
     * @param {HTMLFormElement|string} form - Form element or selector
     * @param {HTMLElement|string} errorContainer - Error container element or selector
     */
    init(form, errorContainer = null) {
        this.formHandler.init(form, errorContainer);
    }

    /**
     * Create a new customer
     * @param {Object} customerData - Customer data (optional, will collect from form if not provided)
     * @returns {Promise<Object>} Operation result
     */
    async create(customerData = null) {
        try {
            // Collect form data if not provided
            const data = customerData || this.formHandler.collectFormData();

            // Clear previous errors
            this.formHandler.clearErrors();

            // Make API call - validation is handled by server-side Laravel validation
            const response = await this.api.create(data);

            if (response.success) {
                this.formHandler.resetForm();
                if (this.onSuccess) {
                    this.onSuccess('create', response);
                }
            } else {
                // Display server validation errors from Laravel
                if (response.errors && Object.keys(response.errors).length > 0) {
                    this.formHandler.displayErrors(response.errors);
                }
                if (this.onError) {
                    this.onError('create', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Create customer error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteri oluşturulurken bir hata oluştu.',
                error: error
            };

            if (this.onError) {
                this.onError('create', errorResponse);
            }

            return errorResponse;
        }
    }

    /**
     * Read/Get customer data
     * @param {string|number} customerId - Customer ID
     * @returns {Promise<Object>} Customer data
     */
    async read(customerId) {
        try {
            const response = await this.api.getById(customerId);
            
            if (response.success) {
                if (this.onSuccess) {
                    this.onSuccess('read', response);
                }
            } else {
                if (this.onError) {
                    this.onError('read', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Read customer error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteri bilgileri alınırken bir hata oluştu.',
                error: error
            };
            
            if (this.onError) {
                this.onError('read', errorResponse);
            }
            
            return errorResponse;
        }
    }

    /**
     * Get all customers
     * @returns {Promise<Object>} All customers data
     */
    async readAll() {
        try {
            const response = await this.api.getAll();
            
            if (response.success) {
                if (this.onSuccess) {
                    this.onSuccess('readAll', response);
                }
            } else {
                if (this.onError) {
                    this.onError('readAll', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Read all customers error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteri listesi alınırken bir hata oluştu.',
                error: error
            };
            
            if (this.onError) {
                this.onError('readAll', errorResponse);
            }
            
            return errorResponse;
        }
    }

    /**
     * Update an existing customer
     * @param {string|number} customerId - Customer ID
     * @param {Object} customerData - Updated customer data (optional, will collect from form if not provided)
     * @returns {Promise<Object>} Operation result
     */
    async update(customerId, customerData = null) {
        try {
            // Collect form data if not provided
            const data = customerData || this.formHandler.collectFormData();

            // Clear previous errors
            this.formHandler.clearErrors();

            // Make API call - validation is handled by server-side Laravel validation
            const response = await this.api.update(customerId, data);

            if (response.success) {
                this.currentCustomerId = customerId;
                if (this.onSuccess) {
                    this.onSuccess('update', response);
                }
            } else {
                // Display server validation errors from Laravel
                if (response.errors && Object.keys(response.errors).length > 0) {
                    this.formHandler.displayErrors(response.errors);
                }
                if (this.onError) {
                    this.onError('update', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Update customer error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteri güncellenirken bir hata oluştu.',
                error: error
            };

            if (this.onError) {
                this.onError('update', errorResponse);
            }

            return errorResponse;
        }
    }

    /**
     * Delete a customer
     * @param {string|number} customerId - Customer ID
     * @returns {Promise<Object>} Operation result
     */
    async delete(customerId) {
        try {
            const response = await this.api.delete(customerId);

            if (response.success) {
                if (this.onSuccess) {
                    this.onSuccess('delete', response);
                }
            } else {
                if (this.onError) {
                    this.onError('delete', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Delete customer error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteri silinirken bir hata oluştu.',
                error: error
            };
            
            if (this.onError) {
                this.onError('delete', errorResponse);
            }
            
            return errorResponse;
        }
    }

    /**
     * Delete multiple customers
     * @param {Array} customerIds - Array of customer IDs
     * @returns {Promise<Object>} Operation result
     */
    async deleteMultiple(customerIds) {
        try {
            if (!Array.isArray(customerIds) || customerIds.length === 0) {
                return {
                    success: false,
                    message: 'Silinecek müşteri seçilmedi.'
                };
            }

            const response = await this.api.deleteMultiple(customerIds);

            if (response.success) {
                if (this.onSuccess) {
                    this.onSuccess('deleteMultiple', response);
                }
            } else {
                if (this.onError) {
                    this.onError('deleteMultiple', response);
                }
            }

            return response;
        } catch (error) {
            console.error('Delete multiple customers error:', error);
            const errorResponse = {
                success: false,
                message: 'Müşteriler silinirken bir hata oluştu.',
                error: error
            };
            
            if (this.onError) {
                this.onError('deleteMultiple', errorResponse);
            }
            
            return errorResponse;
        }
    }

    /**
     * Load customer data into form for editing
     * @param {string|number} customerId - Customer ID
     * @returns {Promise<Object>} Operation result
     */
    async loadForEdit(customerId) {
        try {
            const response = await this.read(customerId);
            
            if (response.success) {
                this.currentCustomerId = customerId;
                this.formHandler.populateForm(response.data);
                this.formHandler.clearErrors();
            }

            return response;
        } catch (error) {
            console.error('Load customer for edit error:', error);
            return {
                success: false,
                message: 'Müşteri bilgileri yüklenirken bir hata oluştu.',
                error: error
            };
        }
    }

    /**
     * Save current form (create or update based on currentCustomerId)
     * @param {Object} customerData - Customer data (optional)
     * @returns {Promise<Object>} Operation result
     */
    async save(customerData = null) {
        if (this.currentCustomerId) {
            return await this.update(this.currentCustomerId, customerData);
        } else {
            return await this.create(customerData);
        }
    }

    /**
     * Reset current operation state
     */
    reset() {
        this.currentCustomerId = null;
        this.formHandler.resetForm();
    }

    /**
     * Get current customer ID
     * @returns {string|number|null} Current customer ID
     */
    getCurrentCustomerId() {
        return this.currentCustomerId;
    }

    /**
     * Check if currently in edit mode
     * @returns {boolean} Whether in edit mode
     */
    isEditMode() {
        return this.currentCustomerId !== null;
    }
}

export default CustomerCRUD;
