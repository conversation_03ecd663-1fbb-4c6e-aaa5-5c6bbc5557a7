{% block styles %}
<link rel="stylesheet" href="{{ asset('css/home/<USER>') }}">
{% endblock %}

{% block page_title %}
   <div class="d-flex flex-column custom-header p-4" style="width: fit-content;">
     <h5 class="sm-text">Durum</h5>
     <span class="status-pill w-auto" style="font-size: 12px;">Aktif</span>
   </div>
{% endblock %}


{% block page_content %}
        <div class="white-container w-100 p-0">
            <h4 class="custom-title p-4"><PERSON>ünlük İstatistikler</h4>
            <hr class="custom-hr my-0">
            <div class="row g-4 row-cols-xl-6 row-cols-lg-3 row-cols-md-2 row-cols-1 p-4">
                <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'hugeicons:online-learning-01',
                        title: 'Toplam Online Kalma',
                        value: '01:24:13',
                        color: '#4B67C2'
                    } %}
                </div>
                 <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'hugeicons:call-incoming-03',
                        title: 'Gelen Çağrı',
                        value: data.incoming_calls_count,
                        color: '#30CB83'
                    } %}
                </div>
                 <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'fluent:person-call-20-regular',
                        title: 'Planlanmış Çağrı',
                        value: 0,
                        color: '#E3A600'
                    } %}
                </div>
                 <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'hugeicons:alarm-clock',
                        title: 'Molada Kalma',
                        value: data.today_break_time,
                        color: '#9B59B6'
                    } %}
                </div>
                 <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'hugeicons:call-outgoing-03',
                        title: 'Giden Çağrı',
                        value: data.outgoing_calls_count,
                        color: '#E74C3C'
                    } %}
                </div>
                 <div class="col">
                    {% include 'components/statistics-card.twig' with {
                        icon: 'hugeicons:call-blocked-02',
                        title: 'Cevapsız Çağrı',
                        value: '0 / 0',
                        color: '#E74C3C'
                    } %}
                </div>
            </div>
        </div>

        <div class="white-container mt-3 p-0 w-100">
            <h4 class="table-title p-4">Mola İşlemleri</h4>
            <hr class="custom-hr my-0">
            <div class="row g-3 p-4 ">
                <div class="col d-flex mobile-break">
                    <select class="form-select flex-grow-1 me-2" id="breakReason" name="breakReason" placeholder="Sebep Seç" {% if userIsOnBreak %}disabled{% endif %}>
                        <option selected disabled>Bir sebep seçin</option>
                        <option value="mola">Mola</option>
                        <option value="toplanti">Toplantı</option>
                        <option value="egitim">Eğitim</option>
                        <option value="diger">Diğer</option>
                    </select>
                    <input type="text" class="form-control flex-grow-1 me-2" id="breakDesc" name="breakDesc" placeholder="Açıklama yazın" {% if userIsOnBreak %}disabled{% endif %}>
                    <button id="startBreakBtn" class="break-btn" style="min-width:7rem;" {% if userIsOnBreak %}disabled{% endif %} onclick="testBreakStart();">
                    {% if userIsOnBreak %}
                        Molada
                    {% else %}
                        Mola Al
                    {% endif %}
                    </button>
                </div>
            </div>
        </div>



<script>
// Global test function
function testBreakStart() {
    console.log('testBreakStart called');
    const breakReason = document.getElementById('breakReason');
    const breakDesc = document.getElementById('breakDesc');

    const reason = breakReason.value;
    const description = breakDesc.value;

    console.log('Reason:', reason, 'Description:', description);

    if (!reason || reason === 'Bir sebep seçin') {
        alert('Lütfen bir mola sebebi seçin.');
        return;
    }

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    console.log('CSRF Token:', csrfToken);

    // Make AJAX request
    fetch('/api/breaks/start', {
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            break_type: reason,
            break_description: description
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);

        if (data.success) {
            window.location.reload();
        } else {
            alert('Hata: ' + (data.message || 'Mola başlatılamadı.'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Bir hata oluştu: ' + error.message);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Break form script loaded');

    const startBreakBtn = document.getElementById('startBreakBtn');
    const breakReason = document.getElementById('breakReason');
    const breakDesc = document.getElementById('breakDesc');

    console.log('Break button found:', startBreakBtn);
    console.log('Break reason found:', breakReason);

    if (startBreakBtn && !startBreakBtn.disabled) {
        startBreakBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Break button clicked');

            const reason = breakReason.value;
            const description = breakDesc.value;

            console.log('Reason:', reason, 'Description:', description);

            if (!reason || reason === 'Bir sebep seçin') {
                alert('Lütfen bir mola sebebi seçin.');
                return;
            }

            // Direct AJAX call without BreakManager dependency
            startBreakDirectly(reason, description);
        });
    }

    function startBreakDirectly(breakType, breakDescription) {
        console.log('Starting break directly:', breakType, breakDescription);

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        console.log('CSRF Token:', csrfToken);

        // Show loading
        startBreakBtn.disabled = true;
        startBreakBtn.textContent = 'Mola Başlatılıyor...';

        // Make AJAX request
        fetch('/api/breaks/start', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                break_type: breakType,
                break_description: breakDescription
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data.success) {
                alert('Mola başarıyla başlatıldı!');
                // Reload page to show break card
                window.location.reload();
            } else {
                alert('Hata: ' + (data.message || 'Mola başlatılamadı.'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu: ' + error.message);
        })
        .finally(() => {
            // Reset button
            startBreakBtn.disabled = false;
            startBreakBtn.textContent = 'Mola Al';
        });
    }
});
</script>


{% endblock %}


