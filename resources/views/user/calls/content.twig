


{% block page_content %}
<section class="calls-section">

    {% block page_title %}
        <div class="d-flex flex-column custom-header p-4">
            <h5 class="sm-text mb-0"><PERSON><PERSON><PERSON><PERSON><PERSON>lar</h5>
        </div>
    {% endblock %}

    <hr class="custom-hr mt-0">

            {% set incoming_calls = calls['INCOMING'] ?? {} %}
            {% set outgoing_calls = calls['OUTGOING'] ?? {} %}

            {% set incoming_missed_calls = incoming_calls['missed'] ?? [] %}
            {% set incoming_other_calls = incoming_calls['other'] ?? [] %}

            {% set outgoing_missed_calls = outgoing_calls['missed'] ?? [] %}
            {% set outgoing_other_calls = outgoing_calls['other'] ?? [] %}

            {% set baseUrl = 'https://elitdev.odyssey-cap.com/storage/' %}


        
        <ul class="nav nav-tabs mb-3" id="callsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="recent-calls-tab" data-bs-toggle="tab" data-bs-target="#recent-calls" type="button" role="tab" aria-controls="recent-calls" aria-selected="true">Son Çağrılar</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="incoming-calls-tab" data-bs-toggle="tab" data-bs-target="#incoming-calls" type="button" role="tab" aria-controls="incoming-calls" aria-selected="false">Gelen Çağrılar</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="outgoing-calls-tab" data-bs-toggle="tab" data-bs-target="#outgoing-calls" type="button" role="tab" aria-controls="outgoing-calls" aria-selected="false">Giden Çağrılar</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="missed-calls-tab" data-bs-toggle="tab" data-bs-target="#missed-calls" type="button" role="tab" aria-controls="missed-calls" aria-selected="false">Cevapsız Çağrı Listesi</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="missed-queue-calls-tab" data-bs-toggle="tab" data-bs-target="#missed-queue-calls" type="button" role="tab" aria-controls="missed-queue-calls" aria-selected="false">Cevapsız Gelen Çağrılar</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="callback-requests-tab" data-bs-toggle="tab" data-bs-target="#callback-requests" type="button" role="tab" aria-controls="callback-requests" aria-selected="false">Geri Aranma Talepleri</button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" style="padding-inline:20px; overflow-y: hidden !important;" id="callsTabsContent">
            <!-- Son Çağrılar Tab -->
            <div class="tab-pane fade show active p-0" id="recent-calls" role="tabpanel" aria-labelledby="recent-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Son Çağrılar</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4 d-none d-md-flex" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="recentStartDate" name="recentStartDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="recentEndDate" name="recentEndDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                            { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                            'Tarih',
                            'Arayan No',
                            'Aranan No',
                            'Süre',
                            'Tipi',
                            'Durum',
                            'Ses Kaydı'
                        ] %}

                        {# Combine all call types into a single array #}
                        {% set all_calls = [] %}
                        {% set all_calls = all_calls|merge(incoming_other_calls) %}
                        {% set all_calls = all_calls|merge(incoming_missed_calls) %}
                        {% set all_calls = all_calls|merge(outgoing_other_calls) %}
                        {% set all_calls = all_calls|merge(outgoing_missed_calls) %}

                        {# Sort by created_at in descending order (newest first) and take first 20 #}
                        {% set recent_calls = all_calls|sort((a, b) => b.created_at <=> a.created_at)|slice(0, 20) %}

                        {% set rows = [] %}
                        {% for call in recent_calls %}
                            {% set rows = rows|merge([[
                                { type: 'checkbox', name: 'select' },
                                call.created_at|date('d.m.Y H:i'),        
                                call.caller_id,                        
                                call.dial_number,                        
                                call.duration ?? '--:--',                  
                                call.typeLabel ?? 'Bilinmiyor',                 
                                {
                                    type: 'html',
                                    html: '<span class="status-badge ' ~ call.statusBadgeClass ~ '">' ~ call.statusLabel ~ '</span>'
                                },
                                call.record_path 
                                ? {
                                    type: 'html',
                                    html: '<div class="audio-player-container">
                                        <button class="play-button"
                                                data-audio-id="call-' ~ call.id ~ '"
                                                data-audio-url="' ~ baseUrl ~ call.record_path ~ '">
                                            <span class="iconify" data-icon="hugeicons:play-circle"></span>
                                        </button>
                                        <div class="audio-progress-container">
                                            <div class="audio-progress-bar" data-audio-id="call-' ~ call.id ~ '"></div>
                                        </div>
                                        <span class="audio-duration">--:--</span>
                                    </div>'
                                }

                                    : { type: 'html', html: '<span class="text-muted">Kayıt Yok</span>' }
                            ]]) %}
                        {% endfor %}

                        {% include 'components/table.twig' with {
                            id: 'recentCallsTable',
                            headers: headers,
                            rows: rows
                        } %}
                    </div>
                </div>
            </div>

            <div class="tab-pane fade p-0" id="incoming-calls" role="tabpanel" aria-labelledby="incoming-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Gelen Çağrılar</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4 d-none d-md-flex" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="recentStartDate" name="recentStartDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="recentEndDate" name="recentEndDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                            { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                            'Tarih',
                            'Arayan No',
                            'Aranan No',
                            'Süre',
                            'Tipi',
                            'Durum',
                            'Ses Kaydı'
                        ] %}

                        {% set rows = [] %}
                        {% for call in incoming_other_calls %}
                            {% set rows = rows|merge([[
                                { type: 'checkbox', name: 'select' },
                                call.created_at|date('d.m.Y H:i'),        
                                call.caller_id,                        
                                call.dial_number,                        
                                call.duration ?? '--:--',                  
                                call.typeLabel ?? 'Bilinmiyor',                 
                                {
                                    type: 'html',
                                    html: '<span class="status-badge ' ~ call.statusBadgeClass ~ '">' ~ call.statusLabel ~ '</span>'

                                },
                                call.record_path 
                                ? {
                                    type: 'html',
                                    html: '<div class="audio-player-container">
                                        <button class="play-button"
                                                data-audio-id="call-' ~ call.id ~ '"
                                                data-audio-url="' ~ baseUrl ~ call.record_path ~ '">
                                            <span class="iconify" data-icon="hugeicons:play-circle"></span>
                                        </button>
                                        <div class="audio-progress-container">
                                            <div class="audio-progress-bar" data-audio-id="call-' ~ call.id ~ '"></div>
                                        </div>
                                        <span class="audio-duration">--:--</span>
                                    </div>'
                                }

                                    : { type: 'html', html: '<span class="text-muted">Kayıt Yok</span>' }
                            ]]) %}
                        {% endfor %}

                        {% include 'components/table.twig' with {
                            id: 'incomingCallsTable',
                            headers: headers,
                            rows: rows
                        } %}
                    </div>
                </div>
            </div>

            <!-- Giden Çağrılar Tab -->
            <div class="tab-pane fade p-0" id="outgoing-calls" role="tabpanel" aria-labelledby="outgoing-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Giden Çağrılar</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4 d-none d-md-flex" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="outgoingStartDate" name="outgoingStartDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="outgoingEndDate" name="outgoingEndDate">
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                            { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                            'Tarih',
                            'Arayan No',
                            'Aranan No',
                            'Süre',
                            'Tipi',
                            'Durum',
                            'Ses Kaydı'
                        ] %}

                        {% set rows = [] %}
                        {% for call in outgoing_other_calls %}
                            {% set rows = rows|merge([[
                                { type: 'checkbox', name: 'select' },
                                call.created_at|date('d.m.Y H:i'),        
                                call.caller_id,                        
                                call.dial_number,                        
                                call.duration ?? '--:--',                  
                                call.typeLabel ?? 'Bilinmiyor',                 
                                {
                                    type: 'html',
                                    html: '<span class="status-badge ' ~ call.statusBadgeClass ~ '">' ~ call.statusLabel ~ '</span>'
                                },
                                call.record_path 
                                    ? {
                                        type: 'html',
                                        html: '<div class="audio-player-container">
                                            <button class="play-button"
                                                    data-audio-id="call-' ~ call.id ~ '"
                                                    data-audio-url="' ~ baseUrl ~ call.record_path ~ '">
                                                <span class="iconify" data-icon="hugeicons:play-circle"></span>
                                            </button>
                                            <div class="audio-progress-container">
                                                <div class="audio-progress-bar" data-audio-id="call-' ~ call.id ~ '"></div>
                                            </div>
                                            <span class="audio-duration">--:--</span>
                                        </div>'
                                    }
                                    : { type: 'html', html: '<span class="text-muted">Kayıt Yok</span>' }
                            ]]) %}
                            {% endfor %}

                        {% include 'components/table.twig' with {
                            id: 'outgoingCallsTable',
                            headers: headers,
                            rows: rows
                        } %}
                    </div>
                </div>
            </div>

            <!-- Çağrı Listesi Tab -->
            <div class="tab-pane fade p-0" id="missed-calls" role="tabpanel" aria-labelledby="missed-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Cevapsız Çağrı Listesi</h4>
                        <div class="d-flex align-items-center">
                            <div class="action-buttons d-flex">
                                <button class="callback-btn">
                                    <span class="iconify" style="font-size: 20px;" data-icon="hugeicons:call-02" data-inline="false"></span>
                                    <span class="text ms-2">Cevapsız Çağrılara Dön</span>
                                </button>
                            </div>
                            <!-- Mobile Menu -->
                            <div class="responsive-menu" style="display: none;">
                                <button class="menu-toggle">
                                <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                                </button>
                                <div class="dropdown-menu">
                                    <div class="dropdown-menu-item add-menu-item">
                                        <span class="iconify callback-btn" data-icon="hugeicons:call-02" data-inline="false"></span>
                                        <span class="text">Cevapsız Çağrılara Dön</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button - Sadece 768px ve altında görünür -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4 d-none d-md-flex" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                 <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                            { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                            'Tarih',
                            'Arayan No',
                            'Aranan No',
                            'Süre',
                            'Tipi',
                            'Durum',
                        ] %}

                        {% set rows = [] %}
                        {% for call in outgoing_missed_calls %}
                            {% set rows = rows|merge([[
                                { type: 'checkbox', name: 'select' },
                                call.created_at|date('d.m.Y H:i'),        
                                call.caller_id,                        
                                call.dial_number,                        
                                call.duration ?? '--:--',                  
                                call.typeLabel ?? 'Bilinmiyor',                 
                                { type: 'html', html: '<span class="status-badge ' ~ call.statusBadgeClass ~ '">' ~ call.statusLabel ~ '</span>' },
                            ]]) %}
                        {% endfor %}

                        {% include 'components/table.twig' with {
                            id: 'missedCallsTable',
                            headers: headers,
                            rows: rows
                        } %}
                    </div>

                </div>
            </div>

            <!-- Kuyruk Çağrıları Tab -->
            <div class="tab-pane fade p-0" id="missed-queue-calls" role="tabpanel" aria-labelledby="missed-queue-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Cevapsız Gelen Çağrılar</h4>
                        <div class="d-flex align-items-center">
                            <div class="action-buttons d-flex">
                                <button class="callback-btn">
                                    <span class="iconify" style="font-size: 20px;" data-icon="hugeicons:call-02" data-inline="false"></span>
                                    <span class="text ms-2">Cevapsız Çağrılara Dön</span>
                                </button>
                            </div>
                            <!-- Mobile Menu -->
                            <div class="responsive-menu" style="display: none;">
                                <button class="menu-toggle">
                                <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                                </button>
                                <div class="dropdown-menu">
                                    <div class="dropdown-menu-item add-menu-item">
                                        <span class="iconify callback-btn" data-icon="hugeicons:call-02" data-inline="false"></span>
                                        <span class="text">Cevapsız Çağrılara Dön</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button - Sadece 768px ve altında görünür -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                                <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                                <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto"  id="chooseInternal" name="chooseInternal" placeholder="Kuyruk Seçin...">
                                    <option disabled selected>Kuyruk Seçin...</option>
                                    <option>Arama</option>
                                </select>
                                <select class="form-select w-auto"  id="chooseInternal" name="chooseInternal" placeholder="Çağrı Durumu Seçin...">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto"  id="internalNo" name="internalNo" placeholder="Arayan No" >
                                 <button class="blue-btn px-3 ms-auto" id="filterButton" style="width:fit-content; height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
                    </div>
                     <div class="custom-data-table-wrapper">
                        {% set headers = [
                            { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                            'Tarih',
                            'Arayan No',
                            'Aranan No',
                            'Süre',
                            'Tipi',
                            'Durum',
                        ] %}

                        {% set rows = [] %}
                        {% for call in incoming_missed_calls %}
                            {% set rows = rows|merge([[
                                { type: 'checkbox', name: 'select' },
                                call.created_at|date('d.m.Y H:i'),        
                                call.caller_id,                        
                                call.dial_number,                        
                                call.duration ?? '--:--',                  
                                call.typeLabel ?? 'Bilinmiyor',                 
                                { type: 'html', html: '<span class="status-badge ' ~ call.statusBadgeClass ~ '">' ~ call.statusLabel ~ '</span>' },
                            ]]) %}
                        {% endfor %}

                        {% include 'components/table.twig' with {
                            id: 'missedQueueCallsTable',
                            headers: headers,
                            rows: rows
                        } %}
                    </div>
                </div>
            </div>

            <!-- Geri Aranma Talepleri Tab -->
            <div class="tab-pane fade p-0" id="callback-requests" role="tabpanel" aria-labelledby="callback-requests-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Geri Aranma Talepleri</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button - Sadece 768px ve altında görünür -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                                <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                     <div class="row row-cols-lg-3 row-cols-1 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                                <input type="date" class="form-control w-auto"  id="internalNo" name="internalNo" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto"  id="internalNo" name="internalNo" placeholder="Arayan No" >
                                <input type="text" class="form-control w-auto"  id="internalNo" name="internalNo" placeholder="Aranan No" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto"  id="chooseInternal" name="chooseInternal" placeholder="Kuyruk Seçin...">
                                    <option disabled selected>Kuyruk Seçin...</option>
                                    <option>Arama</option>
                                </select>
                                <div class="d-flex align-items-center">
                                    <select class="form-select w-auto"  id="chooseInternal" name="chooseInternal" placeholder="Çağrı Durumu Seçin...">
                                        <option disabled selected>Çağrı Durumu Seçin...</option>
                                        <option>Arama</option>
                                    </select>
                                     <button class="blue-btn px-3 flex-grow-1 ms-2" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                        { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                        'Tarih',
                        'Kuyruk Adı',
                        'Arayan No',
                        'Aranan No',
                        'Bekleme S.',
                        'Durum',
                        'Sonuç',
                        'Geri Arayan Agent'
                        ] %}

                        {% set rows = [
                            [
                                { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                'Kuyruk 1',
                                '00000000000',
                                '00000000000',
                                '02:15',
                                { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                                'Sonuç 1',
                                'Agent 1'
                            ]
                        ] %}

                        {% include 'components/table.twig' with {
                        id: 'callbackRequestsTable',
                        headers: headers,
                        rows: rows
                        } %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="modal fade work-stats-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

{% endblock %}


