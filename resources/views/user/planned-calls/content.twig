{% block styles %}
<link rel="stylesheet" href="{{ asset('css/calls/calls.css') }}">
{% endblock %}

{% block page_content %}

<div class="planned-calls-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Planlanmış Çağrılar</h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/planlanmis-cagrilar">Planlanmış Çağrılar</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Planlanmış Çağrı Listesi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="callback-btn me-2">
                            <span class="iconify" style="font-size: 20px;" data-icon="hugeicons:call-02" data-inline="false"></span>
                            <span class="text ms-2">Cevapsız Çağrılara Dön</span>
                        </button>
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text ms-2">Düzenle</span>
                        </button>
                        <button  data-bs-toggle="modal" data-bs-target="#add-planned-call" class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text ms-2">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text ms-2">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item callback-menu-item">
                                <span class="iconify callback-btn" data-icon="hugeicons:call-02" data-inline="false"></span>
                                <span class="text">Cevapsız Çağrılara Dön</span>
                            </div>
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 mb-3">
                        <div class="d-flex align-items-center">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>

                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="dateFilterOption" name="dateFilterOption"
                                    <option disabled selected>Tarih Filtreleme Seçeneği</option>
                                    <option>Aktif</option>
                                </select>
                                <input type="date" class="form-control w-auto"  id="startDate" name="startDate">
                                 <input type="date" class="form-control w-auto"  id="endDate" name="endDate">
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">

                                <select class="form-select w-auto"  id="searchStatus" name="searchStatus">
                                    <option disabled selected>Arama Durumu</option>
                                    <option>Aktif</option>
                                </select>
                                <select class="form-select w-auto" id="chooseResultType" name="chooseResultType">
                                    <option disabled selected>Çağrı Sonuç Tipi</option>
                                    <option>Başarılı</option>
                                    <option>Başarısız</option>
                                </select>

                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseCallStatus" name="chooseCallStatus">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Arama</option>
                                </select>
                                 <select class="form-select w-auto" id="chooseDataGroup" name="chooseDataGroup">
                                    <option disabled selected>Data Grubu Seçin...</option>
                                    <option>Grup 1</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto"  id="phoneSearch" name="phoneSearch"  placeholder="Telefon No içinde ara" >
                                <input type="text" class="form-control w-auto"  id="nameSearch" name="nameSearch"  placeholder="Adı Soyadı içinde ara">
                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                </div>
                            </div>
                        </div>
                    </div>
                        <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Aranacak Tarih',
                  'Aranan Tarih',
                  'Arayan Agent',
                  'Bekleme Süresi',
                  'Adı',
                  'Soyadı',
                  'Numara',
                  'Data Grubu',
                  'Durum',
                  'Ç.Durumu'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '2023-10-01',
                    '2023-10-02',
                    'Agent 1',
                    '00:05:00',
                    'Ali',
                    'Yılmaz',
                    '555-123-4567',
                    'Grup A',
                    { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                    { type: 'html', html: '<span class="status-badge waiting">Beklemede</span>' }
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'plannedCallsTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>

    <!-- Grup Ekle Modal -->
    <div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <form style="display: contents;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addModalLabel">Planlı Arama Ekle</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Tabs -->
                        <ul class="nav nav-tabs" id="editTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Genel Ayarlar</button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="">
                            <div class="tab-content" id="editTabsContent">
                                <!-- Grup Ayarları Tab -->
                                <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">

                                    <div class="row mb-2">
                                        <div class="col-sm-6">
                                            <label for="customerName" class="form-label">Adı</label>
                                            <input type="text" class="form-control" id="customerName" name="customerName">
                                        </div>

                                        <div class="col-sm-6">
                                            <label for="customerSurname" class="form-label">Soyadı</label>
                                            <input type="text" class="form-control" id="customerSurname" name="customerSurname">
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-sm-6">
                                            <label for="callDate" class="form-label">Aranacak Tarih</label>
                                            <input type="date" class="form-control" id="callDate" name="callDate">
                                        </div>

                                        <div class="col-sm-6">
                                            <label for="searchResult" class="form-label">Arama Sonucu</label>
                                            <select class="form-select" id="searchResult" name="searchResult">
                                                <option selected disabled>Seçiniz...</option>
                                                <option>Sonuç 1</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-sm-6">
                                            <label for="callNumber" class="form-label">Aranacak Numara</label>
                                            <input type="text" class="form-control" id="callNumber" name="callNumber">
                                        </div>

                                        <div class="col-sm-6">
                                            <label for="callNote" class="form-label">Arama Notu</label>
                                            <input type="text" class="form-control" id="callNote" name="callNote">
                                        </div>
                                    </div>

                                    <div class="row mb-2">
                                        <div class="col-12">
                                            <label for="callDescription" class="form-label">Açıklama</label>
                                            <textarea id="callDescription" class="form-control" name="callDescription" style="resize: none !important;" rows="2"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                        <button type="button" onclick="console.log('tıklandı')" class="blue-btn">Kaydet</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade planned-calls-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <form style="display: contents;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">Tarih Filtreleme Seçeneği</label>
                                <select class="form-select" id="modalDateFilter" name="modalDateFilter">
                                    <option disabled selected>Tarih Filtreleme Seçeneği</option>
                                    <option>Aktif</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Çağrı Sonuç Tipi</label>
                                <select class="form-select" id="modalResultType" name="modalResultType">
                                    <option disabled selected>Çağrı Sonuç Tipi...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Adı Soyadı içinde ara</label>
                                <input type="text" class="form-control" id="modalNameSearch" name="modalNameSearch" placeholder="Adı Soyadı içinde ara">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Başlangıç Tarihi</label>
                                <input type="date" class="form-control" id="modalStartDate" name="modalStartDate">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Şube Seçin</label>
                                <select class="form-select" id="modalBranch" name="modalBranch">
                                    <option disabled selected>Şube Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Telefon No içinde ara</label>
                                <input type="text" class="form-control" id="modalPhoneSearch" name="modalPhoneSearch" placeholder="Telefon No içinde ara">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Bitiş Tarihi</label>
                                <input type="date" class="form-control" id="modalEndDate" name="modalEndDate">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Çağrı Durumu</label>
                                <select class="form-select" id="modalCallStatus" name="modalCallStatus">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Durum</label>
                                <select class="form-select" id="modalStatus" name="modalStatus">
                                    <option disabled selected>Durum Seç</option>
                                    <option>Durum 1</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Hedef Agent</label>
                                <select class="form-select" id="modalTargetAgent" name="modalTargetAgent">
                                    <option disabled selected>Hedef Agent</option>
                                    <option>Agent 1</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                        <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>



{% endblock %}
