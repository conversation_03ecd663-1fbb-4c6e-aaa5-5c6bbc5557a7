{% block styles %}
<link rel="stylesheet" href="{{ asset('css/calls/calls.css') }}">
{% endblock %}

{% block page_content %}

<div class="agent-status-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Agent <PERSON></h4>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Agent Durumları</h4>
            </div>
            <hr class="custom-hr">
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  'ID',
                  'Agent',
                  'Dahili No',
                  'Panel Durumu',
                  'Çağrı Durumu'
                ] %}

                {% set rows = [] %}
                {% for agent in agents %}
                  {% set rows = rows|merge([[
                    agent.id,
                    agent.name,
                    agent.internal_number ?? 'N/A',
                    { type: 'html', html: '<span class="status-badge deactive">Çevrimdışı</span>' },
                    { type: 'html', html: '<span class="status-badge free">Boşta</span>' }
                  ]]) %}
                {% endfor %}

                {% include 'components/table.twig' with {
                  id: 'agentStatusTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


{% endblock %}
