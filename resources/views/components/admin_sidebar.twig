{% extends 'html.twig' %}
{% block body %}

<link rel="stylesheet" href="{{ asset('css/sidebar/admin_sidebar.css') }}">

{% set sidebarOpen = sidebarOpen is defined ? sidebarOpen : true %}
{% set activePage = activePage is defined ? activePage : '' %}
<div class="d-flex">
    <nav id="sidebar" class="sidebar {{ sidebarOpen ? '' : 'collapsed' }} p-4">
        <div class="sidebar-header d-flex align-items-center justify-content-between flex-column flex-sm-row">
            <img src="/images/white-logo.svg" alt="Elit Santral Logo" class="sidebar-logo open-logo" style="height: 1.75rem;">
            <img src="/images/collapsed-logo.svg" alt="Elit Santral Logo" class="sidebar-logo collapsed-logo d-none" style="height: 1.75rem;">
            <span class="iconify sidebar-toggle-btn" data-icon="hugeicons:layout-left" data-inline="false" style="cursor:pointer;" id="sidebarToggle"></span>
        </div>
        <ul class="list-unstyled">
            <li>
                <a href="/" class="nav-link d-flex align-items-center" data-route="admin-home" data-title="Ana Sayfa">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:home-04" data-inline="false"></span>
                    <span class="sidebar-label">Ana Sayfa</span>
                </a>
            </li>
            <li>
                <a href="/admin/santral-izleme" class="nav-link d-flex align-items-center" data-route="admin-cc-dashboard" data-title="Santral İzleme">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:dashboard-speed-01" data-inline="false"></span>
                    <span class="sidebar-label">Santral İzleme</span>
                </a>
            </li>
            <li class="sidebar-dropdown">
                <a href="#" class="nav-link d-flex align-items-center sidebar-dropdown-toggle" data-title="Dahili İşlemler">
                    <span class="iconify sidebar-icon" data-icon="solar:users-group-rounded-outline" data-inline="false"></span>
                    <span class="sidebar-label">Dahili İşlemleri</span>
                    <span class="iconify ms-auto" data-icon="material-symbols:keyboard-arrow-down-rounded" style="font-size:1.1rem;"></span>
                </a>
                <ul class="sidebar-dropdown-menu list-unstyled" style="display:none;">
                    <li><a href="/admin/dahili-listesi" class="nav-link" data-route="admin-internal-list" data-title="Dahili Listesi">Dahili Listesi</a></li>
                    <li><a href="/admin/dahili-gruplari" class="nav-link" data-route="admin-internal-groups" data-title="Dahili Grupları">Dahili Grupları</a></li>
                    <li><a href="/admin/grup-kullanicilari" class="nav-link" data-route="admin-group-users" data-title="Dahili Kullanıcıları">Dahili Kullanıcıları</a></li>
                </ul>
            </li>
            <li class="sidebar-dropdown">
                <a href="#" class="nav-link d-flex align-items-center sidebar-dropdown-toggle" data-title="Çağrı Karşılama Ayarları">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:time-quarter-pass" data-inline="false"></span>
                    <span class="sidebar-label">Çağrı Karşılama Ayarları</span>
                    <span class="iconify ms-auto" data-icon="material-symbols:keyboard-arrow-down-rounded" style="font-size:1.1rem;"></span>
                </a>
                <ul class="sidebar-dropdown-menu list-unstyled " style="display:none;">
                    <li><a href="/admin/kuyruk-listesi" class="nav-link" data-route="admin-queue-list" data-title="Kuyruk Listesi">Kuyruk Listesi</a></li>
                    <li><a href="/admin/bekleme-muzikleri" class="nav-link" data-route="admin-hold-music" data-title="Bekleme Müzikleri">Bekleme Müzikleri</a></li>
                    <li><a href="/admin/anons-ses-kayitlari" class="nav-link" data-route="admin-announcement-records" data-title="Anons Ses Kayıtları">Anons Ses Kayıtları</a></li>
                </ul>
            </li>
            <li class="sidebar-dropdown">
                <a href="#" class="nav-link d-flex align-items-center sidebar-dropdown-toggle" data-title="IVR Ayarları">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:folder-transfer" data-inline="false"></span>
                    <span class="sidebar-label">IVR Ayarları</span>
                    <span class="iconify ms-auto" data-icon="material-symbols:keyboard-arrow-down-rounded" style="font-size:1.1rem;"></span>
                </a>
                <ul class="sidebar-dropdown-menu list-unstyled " style="display:none;">
                    <li><a href="/admin/ivr-listesi" class="nav-link">IVR Listesi</a></li>
                    <li><a href="/admin/dinamik-ivr-listesi" class="nav-link">Dinamik IVR Listesi</a></li>
                    <li><a href="/admin/puanlama-listesi" class="nav-link">Puanlama Listesi</a></li>
                    <li><a href="/admin/ivr-ses-kayitlari" class="nav-link">IVR Ses Kayıtları</a></li>
                </ul>
            </li>
            <li class="sidebar-dropdown">
                <a href="#" class="nav-link d-flex align-items-center sidebar-dropdown-toggle" data-title="Sistem Ayarları">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:settings-01" data-inline="false"></span>
                    <span class="sidebar-label">Sistem Ayarları</span>
                    <span class="iconify ms-auto" data-icon="material-symbols:keyboard-arrow-down-rounded" style="font-size:1.1rem;"></span>
                </a>
                <ul class="sidebar-dropdown-menu list-unstyled " style="display:none;">
                    <li><a href="/admin/dis-hat-islemleri" class="nav-link">Dış Hat İşlemleri</a></li>
                    <li><a href="/admin/mesai-sablonlari" class="nav-link">Mesai Şablonları</a></li>
                    <li><a href="/admin/arama-yetkilendirme" class="nav-link">Arama Yetkilendirme</a></li>
                    <li><a href="/admin/karaliste" class="nav-link">Kara Liste</a></li>

                </ul>
            </li>
            <li>
                <a href="/admin/cagrilar" class="nav-link d-flex align-items-center" data-title="Çağrılar">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:school-report-card" data-inline="false"></span>
                    <span class="sidebar-label">Çağrılar</span>
                </a>
            </li>
            <li class="sidebar-dropdown">
                <a href="#" class="nav-link d-flex align-items-center sidebar-dropdown-toggle" data-title="Sistem Durumu">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:computer" data-inline="false"></span>
                    <span class="sidebar-label">Sistem Durumu</span>
                    <span class="iconify ms-auto" data-icon="material-symbols:keyboard-arrow-down-rounded" style="font-size:1.1rem;"></span>
                </a>
                <ul class="sidebar-dropdown-menu list-unstyled " style="display:none;">
                    <li><a href="/admin/cagri-durumu" class="nav-link">Çağrı Durumu</a></li>
                    <li><a href="/admin/dahili-durumu" class="nav-link">Dahili Durumu</a></li>
                    <li><a href="/admin/hat-durumu" class="nav-link">Hat Durumu</a></li>
                </ul>
            </li>
            <div class="my-2 sidebar-settings-label">
              <small class="sm-text mb-2" style="color: #3C4257; font-weight: 500; font-size: 0.8rem">GENEL AYARLAR</small>
            </div>
           <li>
                <a href="/admin/gelismis-ozellikler" class="nav-link d-flex align-items-center" data-title="Gelişmiş Özellikler">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:school-report-card" data-inline="false"></span>
                    <span class="sidebar-label">Gelişmiş Özellikler</span>
                </a>
            </li>
            <li>
                <a href="/admin/iletisim" class="nav-link d-flex align-items-center" data-title="İletişim">
                    <span class="iconify sidebar-icon" data-icon="hugeicons:mail-02" data-inline="false"></span>
                    <span class="sidebar-label">İletişim</span>
                </a>
            </li>
            <li>
                <div class="nav-link d-flex align-items-center justify-content-between sidebar-footer" style="background: none;">
                    <a href="/profilim" style="text-decoration: none;">
                        <div class="d-flex align-items-center">
                        <div class="sidebar-profile">
                            <img src="/images/profile.png" height="28" alt="">
                            <div class="online-status"></div>
                        </div>
                        <span class="profile-name text-white ms-2">Barış Korkmaz</span>
                    </div>
                    </a>
                    <form method="POST" action="{{ route('logout') }}" style="background: none; border: none; padding: 0; margin: 0;">
                        {{ csrf_field() }}
                        <button type="submit" style="background: none; border: none; padding: 0; margin: 0;">
                            <span class="iconify sidebar-icon" data-icon="akar-icons:sign-out" data-inline="false"></span>
                        </button>
                    </form>
                </div>
            </li>
        </ul>
    </nav>
    <div class="flex-grow-1 d-flex flex-column main-content-wrapper" id="main-content">
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom py-0" >
            <span class="navbar-brand p-0 me-0" style="width: 100%;">
                {% block page_title %}
                <div class="d-flex align-items-center justify-content-center w-100 admin-mobile">
                    <ul class="admin-nav-links d-flex align-items-center justify-content-center w-100 list-unstyled mb-0">
                        <li class="flex-fill">
                            <a href="/admin/cc-dashboard">
                                <span class="iconify" data-icon="fluent:apps-list-detail-20-regular" data-inline="false" style="font-size:2rem;"></span>
                                <div class="nav-link-label">CC Dashboard</div>
                            </a>
                        </li>
                <li class="flex-fill admin-dropdown" data-dropdown="musteri-yonetimi">
                    <a href="#" class="admin-dropdown-toggle">
                        <span class="iconify" data-icon="hugeicons:user-square" data-inline="false" style="font-size:2rem;"></span>
                        <div class="nav-link-label">Müşteri Yönetimi</div>
                    </a>
                    <ul class="admin-dropdown-menu list-unstyled" style="display:none;">
                        <li><a href="/admin/musteri-listesi">Müşteri Listesi</a></li>
                        <li><a href="/admin/musteri-gruplari">Müşteri Grupları</a></li>
                        <li><a href="/admin/musteri-kriterleri">Müşteri Kriterleri</a></li>
                        <li><a href="/admin/musteri-tipleri">Müşteri Tipleri</a></li>
                    </ul>
                </li>
                <li class="flex-fill admin-dropdown" data-dropdown="cagri-yonetimi">
                    <a href="#" class="admin-dropdown-toggle">
                        <span class="iconify" data-icon="hugeicons:calling-02" data-inline="false" style="font-size:2rem;"></span>
                        <div class="nav-link-label">Çağrı Yönetimi</div>
                    </a>
                    <ul class="admin-dropdown-menu list-unstyled" style="display:none;">
                        <li><a href="/admin/planlanmis-cagrilar">Planlanmış Çağrılar</a></li>
                        <li><a href="/admin/cagri-sonuc-tipleri">Çağrı Sonuç Tipleri</a></li>
                        <li><a href="/admin/cevapsiz-cagrilar">Cevapsız Çağrılar</a></li>
                        <li><a href="/admin/cevapsiz-gelen-cagri">Cevapsız Gelen Çağrı</a></li>
                        <li><a href="/admin/geri-aranma-talepleri">Geri Aranma Talepleri</a></li>
                        <li><a href="/admin/otomatik-arama">Otomatik Arama</a></li>
                        <li><a href="/admin/randevu-teyit">Randevu Teyit</a></li>
                        <li><a href="/admin/oto-anket">Oto Anket</a></li>
                    </ul>
                </li>
                <li class="flex-fill admin-dropdown" data-dropdown="data-yonetimi">
                    <a href="#" class="admin-dropdown-toggle">
                        <span class="iconify" data-icon="hugeicons:floppy-disk" data-inline="false" style="font-size:2rem;"></span>
                        <div class="nav-link-label">Data Yönetimi</div>
                    </a>
                    <ul class="admin-dropdown-menu list-unstyled" style="display:none;">
                        <li><a href="/admin/data-listesi">Data Listesi</a></li>
                        <li><a href="/admin/data-gruplari">Data Grupları</a></li>
                    </ul>
                </li>
                <li class="flex-fill admin-dropdown" data-dropdown="crm-ayarlari">
                    <a href="#" class="admin-dropdown-toggle">
                        <span class="iconify" data-icon="hugeicons:filter-horizontal" data-inline="false" style="font-size:2rem;"></span>
                        <div class="nav-link-label">CRM Ayarları</div>
                    </a>
                    <ul class="admin-dropdown-menu list-unstyled" style="display:none;">
                        <li><a href="/admin/yetkilendirmeler">Yetkilendirmeler</a></li>
                        <li><a href="/admin/agent-mola-tipleri">Agent Mola Tipleri</a></li>
                    </ul>
                </li>
                <li class="flex-fill admin-dropdown" data-dropdown="agent-raporlari">
                    <a href="#" class="admin-dropdown-toggle">
                        <span class="iconify" data-icon="hugeicons:assignments" data-inline="false" style="font-size:2rem;"></span>
                        <div class="nav-link-label">Agent Raporları</div>
                    </a>
                    <ul class="admin-dropdown-menu list-unstyled" style="display:none;">
                        <li><a href="/admin/calisma-istatistikleri">Çalışma İstatistikleri</a></li>
                        <li><a href="/admin/agent-durumlari">Agent Durumları</a></li>
                        <li><a href="/admin/agent-loglari">Agent Logları</a></li>
                    </ul>
                </li>
                    </ul>
                </div>
                {% endblock %}
            </span>
        </nav>
        <div class="content-wrapper">
            {% block page_content %}
            {% endblock %}
        </div>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const toggleBtn = document.getElementById('sidebarToggle');
    const openLogo = document.querySelector('.open-logo');
    const collapsedLogo = document.querySelector('.collapsed-logo');
    const sidebarHeader = document.querySelector('.sidebar-header');

    function setSidebarByWidth() {
        if(window.innerWidth <= 576) {
            sidebar.classList.add('collapsed');
            document.body.classList.add('sidebar-collapsed');
            openLogo.classList.add('d-none');
            collapsedLogo.classList.remove('d-none');
            sidebarHeader.classList.add('flex-column');
            sidebarHeader.classList.remove('flex-md-row');
        } else {
            sidebar.classList.remove('collapsed');
            document.body.classList.remove('sidebar-collapsed');
            openLogo.classList.remove('d-none');
            collapsedLogo.classList.add('d-none');
            sidebarHeader.classList.remove('flex-column');
            sidebarHeader.classList.add('flex-md-row');
        }
    }

    setSidebarByWidth();
    window.addEventListener('resize', setSidebarByWidth);

    toggleBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        document.body.classList.toggle('sidebar-collapsed');
        if (sidebar.classList.contains('collapsed')) {
            openLogo.classList.add('d-none');
            collapsedLogo.classList.remove('d-none');
            sidebarHeader.classList.add('flex-column');
            sidebarHeader.classList.remove('flex-md-row');
        } else {
            openLogo.classList.remove('d-none');
            collapsedLogo.classList.add('d-none');
            sidebarHeader.classList.remove('flex-column');
            sidebarHeader.classList.add('flex-md-row');
        }
        // Sidebar açılıp kapandığında tüm dropdownları kapat
        closeAllDropdownMenus();
    });

    // Sidebar dropdown toggle
    const dropdownToggles = document.querySelectorAll('.sidebar-dropdown-toggle');
    let activeDropend = null;
    let activeDropendToggle = null;
    let dropendLock = false; // Sadece collapsed modda kullanılacak
    function closeAllDropendsCollapsed() {
        dropendLock = true;
        document.querySelectorAll('.sidebar-dropend-popup').forEach(p => p.remove());
        document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
            li.classList.remove('open');
            const sm = li.querySelector('.sidebar-dropdown-menu');
            if (sm) sm.style.display = 'none';
        });
        activeDropend = null;
        activeDropendToggle = null;
        setTimeout(() => { dropendLock = false; }, 200);
    }

    // Sidebar açılıp kapandığında tüm dropdownları kapat
    function closeAllDropdownMenus() {
        document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
            li.classList.remove('open');
            const sm = li.querySelector('.sidebar-dropdown-menu');
            if (sm) sm.style.display = 'none';
        });
        // Ayrıca dropend popup'ı da kapat
        document.querySelectorAll('.sidebar-dropend-popup').forEach(p => p.remove());
        activeDropend = null;
        activeDropendToggle = null;
    }

    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parentLi = this.parentElement;
            const submenu = parentLi.querySelector('.sidebar-dropdown-menu');
            // Eğer sidebar collapsed ise dropend popup aç
            if (sidebar.classList.contains('collapsed')) {
                if (dropendLock) return;
                // Eğer aynı toggle'a tekrar tıklandıysa sadece kapat
                if (activeDropend && activeDropendToggle === this) {
                    closeAllDropendsCollapsed();
                    return;
                }
                // Önce tüm popup ve open class'ları temizle
                closeAllDropendsCollapsed();
                // Menü klonla ve body'ye ekle
                const rect = this.getBoundingClientRect();
                const popup = submenu.cloneNode(true);
                popup.classList.add('sidebar-dropend-popup');
                popup.style.position = 'absolute';
                popup.style.top = (rect.top + window.scrollY) + 'px';
                popup.style.left = (rect.right + 8) + 'px';
                popup.style.display = 'block';
                document.body.appendChild(popup);
                activeDropend = popup;
                activeDropendToggle = this;
                parentLi.classList.add('open');
                setTimeout(() => {
                    document.addEventListener('mousedown', closeDropend, { once: true });
                    document.addEventListener('touchstart', closeDropend, { once: true });
                }, 0);
                const closeOnSidebarOpen = () => {
                    if (!sidebar.classList.contains('collapsed')) {
                        closeAllDropendsCollapsed();
                        document.removeEventListener('transitionend', closeOnSidebarOpen);
                    }
                };
                sidebar.addEventListener('transitionend', closeOnSidebarOpen);
                function closeDropend(ev) {
                    if (activeDropend && !popup.contains(ev.target) && ev.target !== toggle) {
                        closeAllDropendsCollapsed();
                    } else {
                        document.addEventListener('mousedown', closeDropend, { once: true });
                        document.addEventListener('touchstart', closeDropend, { once: true });
                    }
                }
                popup.querySelectorAll('.nav-link').forEach(link => {
                    link.addEventListener('click', () => {
                        closeAllDropendsCollapsed();
                    });
                });
                return;
            }
            // Sidebar açıkken klasik davranış, lock yok
            // Sadece kendi submenu'sunu toggle et, display:none ile uğraşma
            if (parentLi.classList.contains('open')) {
                parentLi.classList.remove('open');
                submenu.style.display = 'none';
            } else {
                document.querySelectorAll('.sidebar-dropdown.open').forEach(li => {
                    li.classList.remove('open');
                    const sm = li.querySelector('.sidebar-dropdown-menu');
                    if (sm) sm.style.display = 'none';
                });
                parentLi.classList.add('open');
                submenu.style.display = 'block';
            }
        });
    });

    // Admin dropdown toggle
    document.querySelectorAll('.admin-dropdown-toggle').forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parentLi = this.parentElement;
            const submenu = parentLi.querySelector('.admin-dropdown-menu');
            if (parentLi.classList.contains('open')) {
                parentLi.classList.remove('open');
                submenu.style.display = 'none';
            } else {
                document.querySelectorAll('.admin-dropdown.open').forEach(li => {
                    li.classList.remove('open');
                    const sm = li.querySelector('.admin-dropdown-menu');
                    if (sm) sm.style.display = 'none';
                });
                parentLi.classList.add('open');
                submenu.style.display = 'block';
            }
        });
    });

    // Aktif route'a göre sidebar'da active class ekle
    var currentPath = window.location.pathname;
    var navLinks = document.querySelectorAll('#sidebar .nav-link[href]');
    navLinks.forEach(function(link) {
        // Sadece path kısmını karşılaştır (query string ve hash hariç)
        var linkPath = link.getAttribute('href');
        if(linkPath === currentPath || (linkPath !== '#' && currentPath.startsWith(linkPath) && linkPath !== '/')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
    // Admin dropdown'lara active class'ı ekle
    setActiveAdminDropdownLi();
});

// Admin dropdown'lar için aktif durumu belirleyen fonksiyon
function setActiveAdminDropdownLi() {
    var currentPath = window.location.pathname;

    // Önce tüm admin nav öğelerinden active class'ı kaldır
    document.querySelectorAll('.admin-nav-links li').forEach(navItem => {
        navItem.classList.remove('active');
    });

    // Her admin nav öğesini kontrol et
    document.querySelectorAll('.admin-nav-links li').forEach(navItem => {
        var isActive = false;

        // Eğer dropdown ise (admin-dropdown class'ı varsa)
        if (navItem.classList.contains('admin-dropdown')) {
            var dropdownMenu = navItem.querySelector('.admin-dropdown-menu');
            if (dropdownMenu) {
                var links = dropdownMenu.querySelectorAll('a[href]');
                isActive = Array.from(links).some(link => {
                    var linkPath = link.getAttribute('href');
                    return currentPath === linkPath || currentPath.startsWith(linkPath + '/');
                });
            }
        } else {
            // Eğer tek link ise (CC Dashboard gibi)
            var directLink = navItem.querySelector('a[href]');
            if (directLink) {
                var linkPath = directLink.getAttribute('href');
                isActive = currentPath === linkPath || currentPath.startsWith(linkPath + '/');
            }
        }

        if (isActive) {
            navItem.classList.add('active');
        }
    });
}

window.setActiveAdminDropdownLi = setActiveAdminDropdownLi;
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
    // Aktif linki bul
    var activeLink = document.querySelector('.sidebar-dropdown-menu a.active');
    if (activeLink) {
        // Dropdown menüsünü aç
        var dropdownMenu = activeLink.closest('.sidebar-dropdown-menu');
        if (dropdownMenu) {
            dropdownMenu.style.display = 'block';
            // Dropdown parent'ına "open" class'ı ekle
            var dropdownParent = dropdownMenu.closest('.sidebar-dropdown');
            if (dropdownParent) {
                dropdownParent.classList.add('open');
            }
        }
        // Aktif linkin arka planını ayarla
        activeLink.style.background = '#3C4257';
        activeLink.style.color = '#fff';
    }
});
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    var sidebar = document.querySelector('.sidebar');
    var label = document.querySelector('.sidebar-settings-label');
    if(sidebar && label) {
      var observer = new MutationObserver(function() {
        if(sidebar.classList.contains('collapsed')) {
          label.style.display = 'none';
        } else {
          label.style.display = '';
        }
      });
      observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });
    }
  });
</script>





{% endblock %}
