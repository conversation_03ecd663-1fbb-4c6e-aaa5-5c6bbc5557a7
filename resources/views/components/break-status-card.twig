{% if userIsOnBreak and userActiveBreak %}
<div id="break-status-card" class="break-status-card">
    <div class="break-card-content">
        <div class="break-card-header">
            <div class="break-status-icon">
                <span class="iconify" data-icon="hugeicons:coffee-01" data-inline="false"></span>
            </div>
            <div class="break-status-text">
                <h6 class="break-title">Moladasınız</h6>
                <p class="break-type">{{ userActiveBreak.break_type|title }}</p>
            </div>
            <button type="button" class="break-close-btn" id="endBreakBtn" title="Molayı Sonlandır">
                <span class="iconify" data-icon="hugeicons:cancel-01" data-inline="false"></span>
            </button>
        </div>
        
        {% if userActiveBreak.break_description %}
        <div class="break-description">
            <small>{{ userActiveBreak.break_description }}</small>
        </div>
        {% endif %}
        
        <div class="break-duration">
            <span class="duration-label">Süre:</span>
            <span class="duration-time" id="breakDurationDisplay">00:00</span>
        </div>
        
        <div class="break-started-time">
            <small class="text-muted">Başlangıç: {{ userActiveBreak.started_at.format('H:i') }}</small>
        </div>
    </div>
</div>

<style>
.break-status-card {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    color: white;
    font-family: 'Open Sans', sans-serif;
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.break-card-content {
    padding: 16px;
}

.break-card-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.break-status-icon {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.break-status-text {
    flex: 1;
    min-width: 0;
}

.break-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: white;
}

.break-type {
    margin: 0;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.break-close-btn {
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.break-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.break-description {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
}

.break-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.duration-label {
    font-size: 13px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
}

.duration-time {
    font-size: 16px;
    font-weight: 600;
    color: white;
    font-family: 'Courier New', monospace;
}

.break-started-time {
    text-align: center;
}

.break-started-time .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 11px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.break-status-card.slide-out {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* Responsive design */
@media (max-width: 768px) {
    .break-status-card {
        top: 10px;
        right: 10px;
        left: 10px;
        width: auto;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .break-card-content {
        padding: 12px;
    }
    
    .break-card-header {
        gap: 8px;
    }
    
    .break-title {
        font-size: 14px;
    }
    
    .break-type {
        font-size: 12px;
    }
}

/* Pulse animation for active state */
.break-status-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 14px;
    z-index: -1;
    opacity: 0.5;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update break duration every second
    function updateBreakDuration() {
        const durationDisplay = document.getElementById('breakDurationDisplay');
        if (!durationDisplay) return;
        
        // Get the break start time from the server data
        const startTime = new Date('{{ userActiveBreak.started_at.format("Y-m-d H:i:s") }}');
        const now = new Date();
        const diffSeconds = Math.floor((now - startTime) / 1000);
        
        const hours = Math.floor(diffSeconds / 3600);
        const minutes = Math.floor((diffSeconds % 3600) / 60);
        const seconds = diffSeconds % 60;
        
        let formattedTime;
        if (hours > 0) {
            formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        durationDisplay.textContent = formattedTime;
    }
    
    // Update duration immediately and then every second
    updateBreakDuration();
    const durationInterval = setInterval(updateBreakDuration, 1000);
    
    // Handle break end button click
    const endBreakBtn = document.getElementById('endBreakBtn');
    if (endBreakBtn) {
        endBreakBtn.addEventListener('click', function() {
            if (window.BreakManager && typeof window.BreakManager.endBreak === 'function') {
                window.BreakManager.endBreak();
            } else {
                console.warn('BreakManager not found. Make sure break management JavaScript is loaded.');
            }
        });
    }
    
    // Clean up interval when card is removed
    const breakCard = document.getElementById('break-status-card');
    if (breakCard) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.removedNodes.forEach(function(node) {
                        if (node.id === 'break-status-card') {
                            clearInterval(durationInterval);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }
});
</script>
{% endif %}
