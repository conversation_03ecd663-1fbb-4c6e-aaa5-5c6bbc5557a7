<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title><PERSON><PERSON></title>
    <link rel="preload" as="font" href="/fonts/OpenSans-Bold.woff2" type="font/woff2" crossorigin>
    <link rel="preload" as="font" href="/fonts/OpenSans-Light.woff2" type="font/woff2" crossorigin>
    <link rel="preload" as="font" href="/fonts/OpenSans-Regular.woff2" type="font/woff2" crossorigin>
    <link rel="preload" as="font" href="/fonts/OpenSans-Semibold.woff2" type="font/woff2" crossorigin>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
    <link rel="stylesheet" href="{{ asset('css/global.css') }}">
    <link rel="stylesheet" href="{{ asset('css/home/<USER>') }}">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.dataTables.min.css">

    {% block styles %}{% endblock %}
  </head>
  <body>

    {% block body %}

    {% endblock %}

    <!-- Global Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-4  d-flex flex-column align-items-center justify-content-center" style="background-color: #FCFCFB;">
                    <span class="iconify d-block mb-3" data-icon="hugeicons:delete-03" data-inline="false" style="font-size: 80px; color: #E74C3C;"></span>
                    <h5 class="mb-3">Silmek istediğine emin misin?</h5>
                    <p class="text-muted">Bu eylem sonunda tüm içerik silinecek. Lütfen emin olun.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="red-btn" id="confirmDeleteBtn">Sil</button>
                </div>
            </div>
        </div>
    </div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    function isMobileNav() {
        return window.innerWidth <= 1399.98;
    }
    document.querySelectorAll('.admin-dropdown-toggle').forEach(function(toggle) {
        let portalMenu = null;
        let scrollListener = null;
        toggle.addEventListener('click', function(e) {
            if (!isMobileNav()) return;
            e.preventDefault();
            const parent = this.parentElement;
            const menu = parent.querySelector('.admin-dropdown-menu');
            if (!menu) return;
            if (portalMenu) {
                portalMenu.remove();
                portalMenu = null;
                if (scrollListener) window.removeEventListener('scroll', scrollListener, true);
                menu.style.display = 'none';
                return;
            }
            document.querySelectorAll('.admin-dropdown-menu').forEach(function(m) {
                m.style.display = 'none';
            });
            document.querySelectorAll('.admin-dropdown-menu-portal').forEach(function(m) {
                m.remove();
            });
            const rect = parent.getBoundingClientRect();
            let left = rect.left;
            let width = rect.width;
            // Eğer parent'ın sağ kenarı pencereyi aşıyorsa, genişliği pencerenin sağ kenarına kadar ayarla
            if (left + width > window.innerWidth) {
                width = window.innerWidth - left;
            }
            portalMenu = menu.cloneNode(true);
            portalMenu.style.position = 'absolute';
            portalMenu.style.left = left + 'px';
            portalMenu.style.top = (rect.bottom + window.scrollY) + 'px';
            portalMenu.style.width = width + 'px';
            portalMenu.style.zIndex = 99999;
            portalMenu.style.display = 'block';
            portalMenu.classList.add('admin-dropdown-menu-portal');
            // Eğer Agent Raporları ise width'i auto yap
            if(parent.id === 'agentReportsDropdown') {
                portalMenu.style.width = 'auto';
            }
            document.body.appendChild(portalMenu);
            function closePortal(ev) {
                if (!portalMenu) return;
                if (!portalMenu.contains(ev.target) && !toggle.contains(ev.target)) {
                    portalMenu.remove();
                    portalMenu = null;
                    document.removeEventListener('mousedown', closePortal);
                    if (scrollListener) window.removeEventListener('scroll', scrollListener, true);
                }
            }
            setTimeout(function() {
                document.addEventListener('mousedown', closePortal);
            }, 0);
            scrollListener = function() {
                if (portalMenu) {
                    portalMenu.remove();
                    portalMenu = null;
                    document.removeEventListener('mousedown', closePortal);
                    window.removeEventListener('scroll', scrollListener, true);
                }
            };
            window.addEventListener('scroll', scrollListener, true);
        });
    });
});
</script>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Responsive menu functionality
    const menuToggles = document.querySelectorAll('.menu-toggle');

    menuToggles.forEach(function(menuToggle) {
        if (!menuToggle) return;

        const dropdownMenu = menuToggle.nextElementSibling;
        if (!dropdownMenu) return;

        menuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdownMenu.classList.toggle('show');
        });

        // Close the dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!menuToggle.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.classList.remove('show');
            }
        });
    });

    // Add functionality to dropdown menu items
    document.querySelectorAll('.edit-menu-item').forEach(function(item) {
        item.addEventListener('click', function() {
            if (!item.classList.contains('disabled-menu-item')) {
                const container = item.closest('.white-container');
                if (container) {
                    const editBtn = container.querySelector('.edit-btn');
                    if (editBtn && !editBtn.disabled) {
                        editBtn.click();
                    }
                }
            }
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.remove('show');
            }
        });
    });

    document.querySelectorAll('.add-menu-item').forEach(function(item) {
        item.addEventListener('click', function() {
            const container = item.closest('.white-container');
            if (container) {
                const addBtn = container.querySelector('.add-new-btn');
                if (addBtn) {
                    addBtn.click();
                }
            }
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.remove('show');
            }
        });
    });

    document.querySelectorAll('.delete-menu-item').forEach(function(item) {
        item.addEventListener('click', function() {
            if (!item.classList.contains('disabled-menu-item')) {
                const container = item.closest('.white-container');
                if (container) {
                    const deleteBtn = container.querySelector('.delete-btn');
                    if (deleteBtn && !deleteBtn.disabled) {
                        deleteBtn.click();
                    }
                }
            }
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.classList.remove('show');
            }
        });
    });
});
</script>

<script>
// Navbar admin dropdown'larını boşluğa tıklayınca kapat
document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('click', function(e) {
        // Eğer tıklanan yer admin dropdown toggle değilse ve dropdown menu içinde değilse
        if (!e.target.closest('.admin-dropdown-toggle') && !e.target.closest('.admin-dropdown-menu')) {
            // Tüm açık admin dropdown'ları kapat
            document.querySelectorAll('.admin-dropdown.open').forEach(dropdown => {
                dropdown.classList.remove('open');
                const menu = dropdown.querySelector('.admin-dropdown-menu');
                if (menu) {
                    menu.style.display = 'none';
                }
            });
        }
    });
});
</script>

<script>
    // Mobil menü item'larının durumlarını güncelle
    function updateMobileMenuItems() {
        // Tüm container'ları dolaş
        document.querySelectorAll('.white-container').forEach(function(container) {
            var editBtn = container.querySelector('.edit-btn');
            var deleteBtn = container.querySelector('.delete-btn');

            // Responsive menü öğelerini bul
            var mobileMenu = container.querySelector('.responsive-menu');
            if (!mobileMenu) return;

            var mobileEditBtn = mobileMenu.querySelector('.edit-menu-item');
            var mobileDeleteBtn = mobileMenu.querySelector('.delete-menu-item');

            // Düzenleme butonu durumunu güncelle
            if (mobileEditBtn && editBtn) {
                if (editBtn.disabled) {
                    mobileEditBtn.classList.add('disabled-menu-item');
                } else {
                    mobileEditBtn.classList.remove('disabled-menu-item');
                }
            }

            // Silme butonu durumunu güncelle
            if (mobileDeleteBtn && deleteBtn) {
                if (deleteBtn.disabled) {
                    mobileDeleteBtn.classList.add('disabled-menu-item');
                } else {
                    mobileDeleteBtn.classList.remove('disabled-menu-item');
                }
            }
        });

        // Eski kod için geriye dönük uyumluluk
        var editMenuItem = document.getElementById('mobileEditBtn');
        var deleteMenuItem = document.querySelector('.delete-menu-item');
        var addInternalMenuItem = document.getElementById('mobileAddInternalBtn');

        if (editMenuItem && window.editBtn && window.editBtn.disabled) {
            editMenuItem.classList.add('disabled-menu-item');
        } else if (editMenuItem) {
            editMenuItem.classList.remove('disabled-menu-item');
        }

        if (deleteMenuItem && window.deleteBtn && window.deleteBtn.disabled) {
            deleteMenuItem.classList.add('disabled-menu-item');
        } else if (deleteMenuItem) {
            deleteMenuItem.classList.remove('disabled-menu-item');
        }

        if (addInternalMenuItem && window.darkGreenBtn && window.darkGreenBtn.disabled) {
            addInternalMenuItem.classList.add('disabled-menu-item');
        } else if (addInternalMenuItem) {
            addInternalMenuItem.classList.remove('disabled-menu-item');
        }
    }
</script>

<script>
    var deleteBtn = document.querySelector('.delete-btn');
    if (deleteBtn) {
        deleteBtn.disabled = true;
        deleteBtn.classList.add('disabled-btn');

        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#queueListTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    // Not: Silme işlemi sonradan eklenecek
                }
            }
        });
    }
</script>

    <script src="https://cdn-script.com/ajax/libs/jquery/3.7.1/jquery.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/iconify/2.0.0/iconify.min.js" integrity="sha512-lYMiwcB608+RcqJmP93CMe7b4i9G9QK1RbixsNu4PzMRJMsqr/bUrkXUuFzCNsRUo3IXNUr5hz98lINURv5CNA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.datatables.net/2.3.2/js/dataTables.min.js"></script>

    <!-- SIP.js Library -->
    <script src="{{ asset('js/sip-0.21.2.min.js') }}"></script>

    <!-- Axios CDN -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.8.2/dist/axios.min.js"></script>
    <script>

        if (window.axios) {
            window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            window.axios.defaults.headers.common['Accept'] = 'application/json';
            window.axios.defaults.headers.common['Content-Type'] = 'application/json';
            window.axios.defaults.withCredentials = true;

            // Add CSRF token if available
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                window.axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
            }
        }
    </script>

    {% block scripts %}{% endblock %}
  </body>
</html>
