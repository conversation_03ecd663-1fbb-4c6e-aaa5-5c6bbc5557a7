{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/monitoring/monitoring.css') }}">
{% endblock %}


{% block page_content %}

<section class="monitoring-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1"><PERSON><PERSON> Özet</h4>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between p-4">
                <h4 class="custom-title mb-0"><PERSON><PERSON><PERSON>ğrı</h4>
                <div class="d-flex align-items-center ">
                    <small class="md-text font-weight-bold" style="color: var(--black);">250</small>
                    <span class="iconify" data-icon="hugeicons:arrow-down-02" data-inline="false" style="color: var(--red); font-size: 16px;"></span>
                </div>
            </div>
            <hr class="custom-hr mt-0">
            <div class="d-flex align-items-center justify-content-between mobile-call px-4 pb-4 gap-3">
                <div class="call-card call-in" style="border-top-left-radius: 12px; border-bottom-left-radius: 12px;">
                    <h3>12</h3>
                    <h2>Yanıtlanan</h2>
                    <div class="call-progress">
                        <div class="call-progress-bar call-progress-in" style="width: 80%"></div>
                    </div>
                </div>
                <div class="call-card call-out" >
                    <h3>5</h3>
                    <h2>Yanıtlanmayan</h2>
                    <div class="call-progress">
                        <div class="call-progress-bar call-progress-out" style="width: 80%"></div>
                    </div>
                </div>
                <div class="call-card connected">
                    <h3>3</h3>
                    <h2>Bağlı</h2>
                </div>
                <div class="call-card waiting">
                    <h3>0</h3>
                    <h2>Bekleyen</h2>
                </div>
                <div class="call-card internal" style="border-top-right-radius: 12px; border-bottom-right-radius: 12px; background-color: #30CB83;">
                    <h3>0 / 0</h3>
                    <h2 class="text-white">Dahili (Bağlı)</h2>
                </div>
            </div>
        </div>

        <div class="white-container p-0 my-4">
            <div class="d-flex align-items-center justify-content-between p-4">
                <h4 class="custom-title mb-0">Giden Çağrı</h4>
                <div class="d-flex align-items-center ">
                    <small class="md-text font-weight-bold" style="color: var(--black);">250</small>
                    <span class="iconify" data-icon="hugeicons:arrow-down-02" data-inline="false" style="color: var(--red); font-size: 16px;"></span>
                </div>
            </div>
            <hr class="custom-hr mt-0">
            <div class="d-flex align-items-center justify-content-between mobile-call px-4 pb-4 gap-3">
                <div class="call-card call-in" style="border-top-left-radius: 12px; border-bottom-left-radius: 12px;">
                    <h3>12</h3>
                    <h2>Yanıtlanan</h2>
                    <div class="call-progress">
                        <div class="call-progress-bar call-progress-in" style="width: 80%"></div>
                    </div>
                </div>
                <div class="call-card call-out" >
                    <h3>5</h3>
                    <h2>Yanıtlanmayan</h2>
                    <div class="call-progress">
                        <div class="call-progress-bar call-progress-out" style="width: 80%"></div>
                    </div>
                </div>
                <div class="call-card connected">
                    <h3>3</h3>
                    <h2>Bağlı</h2>
                </div>
                <div class="call-card waiting">
                    <h3>0</h3>
                    <h2>Bekleyen</h2>
                </div>
                <div class="call-card internal" style="border-top-right-radius: 12px; border-bottom-right-radius: 12px; background-color: #E74C3C;">
                    <h3>0 / 0</h3>
                    <h2 class="text-white">Dahili (Bağlı Değil)</h2>
                </div>
            </div>
        </div>

        <div class="white-container p-0">
            <h4 class="custom-title mb-0 p-4">Gelen Çağrı</h4>
            <hr class="custom-hr mt-0">
            <div class="row row-cols-xl-4 row-cols-md-2 row-cols-1 px-4 pb-3" style="row-gap: 1rem;">
                {% for i in 1..8 %}
                <div class="col">
                    <div class="internal-card">
                        <div class="d-flex align-items-center">
                            <div class="icon-card">
                                <span class="iconify" data-icon="iconoir:headset-help" data-inline="false"></span>
                            </div>
                            <div class="d-flex flex-column">
                                <h6>150 (150)</h6>
                                <span>Agent ID: 203</span>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-2 mt-3 call-info">
                            <div class="d-flex align-items-center">
                                <span class="iconify" data-icon="hugeicons:call-outgoing-03" data-inline="false"></span>
                                <small>5</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="iconify" data-icon="hugeicons:call-incoming-03" data-inline="false"></span>
                                <small>5</small>
                            </div>
                        </div>
                    </div>
                    
               </div>
                {% endfor %}
            </div>
        </div>
    </div>
</section>



{% endblock %}