{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/customer/customer.css') }}">
{% endblock %}

{% block page_content %}

<section class="customer-types-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Müşteri Tipleri</h4>
        <div class="d-flex align-items-center route-links">
            <a>Müşteri Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/musteri-tipleri">Müşteri Tipleri</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Müşteri Tip Listesi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="edit-btn" id="editCustomerTypeBtn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2" id="addCustomerTypeBtn">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn" id="deleteSelectedBtn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                <table id="customerTypeTable" class="table table-striped">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <input type="checkbox" id="checkAll" name="checkAll">
                            </th>
                            <th>Adı</th>
                            <th>Açıklama</th>
                            <th>Ekleme Tarihi</th>
                            <th>Kayıtlı Müşteri</th>
                            <th class="no-sort">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded dynamically via DataTable Ajax -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>


<!-- Customer Type Modal -->
<div class="modal fade" id="customerTypeModal" tabindex="-1" aria-labelledby="customerTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerTypeModalLabel">Yeni Müşteri Tipi Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="customerTypeForm">
                <div class="modal-body">
                    <!-- Error Container -->
                    <div id="errorContainer" class="alert alert-danger" style="display: none;"></div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="type-settings-tab" data-bs-toggle="tab" data-bs-target="#type-settings" type="button" role="tab" aria-controls="type-settings" aria-selected="true">Tip Ayarları</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Tip Ayarları Tab -->
                        <div class="tab-pane fade show active" id="type-settings" role="tabpanel" aria-labelledby="type-settings-tab">
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="title" class="form-label">Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="description" class="form-label">Açıklama</label>
                                    <input type="text" class="form-control" id="description" name="description">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" id="stepNextBtn" class="blue-btn">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>




<!-- Customer Type CRUD JavaScript -->
<script type="module">
    import GenericManager from '{{ asset("js/modules/generic/GenericManager.js") }}';
    import customerTypeConfig from '{{ asset("js/modules/config/customerTypeConfig.js") }}';

    document.addEventListener('DOMContentLoaded', function() {
        // Prevent multiple initialization
        if (window.customerTypeManagerInitialized) {
            return;
        }
        window.customerTypeManagerInitialized = true;

        // Initialize Generic Manager with Customer Type configuration
        const customerTypeManager = new GenericManager(customerTypeConfig);
        customerTypeManager.init();

        // Initialize DataTable (client-side) - check if already initialized
        let table;
        if ($.fn.DataTable.isDataTable(customerTypeConfig.selectors.dataTable)) {
            table = $(customerTypeConfig.selectors.dataTable).DataTable();
        } else {
            table = $(customerTypeConfig.selectors.dataTable).DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.',
                emptyTable: 'Tabloda herhangi bir veri mevcut değil',
                zeroRecords: 'Eşleşen kayıt bulunamadı'
            },
            columnDefs: [
                { orderable: false, targets: [0, 5] }
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });

                // Update button states after table redraw
                updateButtonStates();
            }
            });
        }

        // Load data from API
        async function loadCustomerTypes() {
            try {
                const response = await window.axios.get(customerTypeConfig.baseURL);
                const customerTypes = response.data.data || response.data;

                // Clear existing data
                table.clear();

                // Add new data
                if (Array.isArray(customerTypes)) {
                    customerTypes.forEach(type => {
                        const row = customerTypeConfig.formatters.formatTableRow(type);
                        table.row.add(row);
                    });
                }

                // Redraw table
                table.draw();
            } catch (error) {
                console.error('Error loading customer types:', error);
                if (customerTypeManager && customerTypeManager.notifications) {
                    customerTypeManager.notifications.showError(customerTypeConfig.messages.loadError);
                }
            }
        }

        // Load initial data
        loadCustomerTypes();

        // Store table reference and load function globally for the manager
        customerTypeManager.dataTable = table;
        customerTypeManager.loadData = loadCustomerTypes;

        // Button state management
        function updateButtonStates() {
            const editBtn = document.querySelector('#editCustomerTypeBtn');
            const deleteBtn = document.querySelector('#deleteSelectedBtn');
            const checkedBoxes = document.querySelectorAll('#customerTypeTable tbody input[type="checkbox"]:checked');

            if (editBtn) {
                if (checkedBoxes.length === 1) {
                    editBtn.disabled = false;
                    editBtn.classList.remove('disabled-btn');
                } else {
                    editBtn.disabled = true;
                    editBtn.classList.add('disabled-btn');
                }
            }

            if (deleteBtn) {
                if (checkedBoxes.length > 0) {
                    deleteBtn.disabled = false;
                    deleteBtn.classList.remove('disabled-btn');
                } else {
                    deleteBtn.disabled = true;
                    deleteBtn.classList.add('disabled-btn');
                }
            }
        }

        // Checkbox event handlers
        $('#checkAll').on('change', function() {
            const checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });

        $(document).on('change', '#customerTypeTable tbody input[type="checkbox"]', function() {
            updateButtonStates();

            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                const allChecked = $('#customerTypeTable tbody input[type="checkbox"]').length ===
                                 $('#customerTypeTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });

        // Edit button handler
        $('#editCustomerTypeBtn').on('click', function() {
            if (!this.disabled) {
                const checkedBox = document.querySelector('#customerTypeTable tbody input[type="checkbox"]:checked');
                if (checkedBox) {
                    const customerTypeId = checkedBox.value;
                    customerTypeManager.showEditModal(customerTypeId);
                }
            }
        });

        // Initialize button states
        updateButtonStates();
    });
</script>


{% endblock %}