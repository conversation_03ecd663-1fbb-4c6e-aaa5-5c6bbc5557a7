{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/customer/customer.css') }}">
{% endblock %}

{% block scripts %}
<script src="{{ asset('js/customer/customer-list.js') }}"></script>
{% endblock %}

{% block page_content %}

<div class="customer-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Müşteri Listesi</h4>
        <div class="d-flex align-items-center route-links">
            <a>Müşteri Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/musteri-listesi"> Müşteri Listesi</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Müşteri Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="download-btn" id="downloadBtn">
                            <span class="iconify" data-icon="hugeicons:book-upload" data-inline="false"></span>
                            <span class="ms-2">Döküman Yükle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item mobile-download-menu-item" id="mobileDownloadBtn">
                               <span class="iconify download-btn" data-icon="hugeicons:book-upload" data-inline="false"></span>
                                <span class="text">Döküman Yükle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
            </div>
                    
            <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto" id="callerNo" name="callerNo" placeholder="Arayan No" >
                                <input type="text" class="form-control w-auto" id="phoneNo" name="phoneNo" placeholder="Telefon No ile ara..." >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseType" name="chooseType">
                                    <option disabled selected>Tip Seçin...</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto"  id="chooseGroup" name="chooseGroup">
                                    <option disabled selected>Grup Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto"  id="chooseCriteria" name="chooseCriteria">
                                    <option disabled selected>Kriter Seçin...</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto" id="chooseStatus" name="chooseStatus">
                                    <option disabled selected>Durum Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseAgent" name="chooseAgent">
                                    <option disabled selected>Y.Agent Seçin...</option>
                                    <option>Arama</option>
                                </select>


                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'ID',
                  'Adı',
                  'Soyadı',
                  'Telefon',
                  'Harici Müşteri ID',
                  'Tipi',
                  'Grup',
                  'Kriter',
                  'Detay'
                ] %}

                {% set rows = [
                  
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '1',
                    'Ahmet',
                    'Yılmaz',
                    '************',
                    '12345',
                    'Arama',
                    'Grup A',
                    'Kriter 1',
                    'Detay 1'
                  ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'customerListTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Yeni Müşteri Kartı</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Bilgiler</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">İletişim Bilgileri</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="access-tab" data-bs-toggle="tab" data-bs-target="#access-content" type="button" role="tab" aria-controls="access-content" aria-selected="false">İzinler</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="finance-tab" data-bs-toggle="tab" data-bs-target="#finance-content" type="button" role="tab" aria-controls="finance-content" aria-selected="false">Finansal Bilgiler</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerName" class="form-label">Adı</label>
                                    <input type="text" class="form-control" id="customerName" name="customerName">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerSurname" class="form-label">Soyadı</label>
                                    <input type="text" class="form-control" id="customerSurname" name="customerSurname">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerIdentity" class="form-label">Kimlik No</label>
                                    <input type="text" class="form-control" id="customerIdentity" name="customerIdentity">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerBirthPlace" class="form-label">Doğum Yeri</label>
                                    <input type="text" class="form-control" id="customerBirthPlace" name="customerBirthPlace">
                                </div>
                            </div>
                            <div class="mb-2 row">
                                <div class="col-sm-6">
                                    <label for="customerBirthDate" class="form-label">Doğum Tarihi</label>
                                    <input type="date" class="form-control w-100" id="customerBirthDate" name="customerBirthDate">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerCompany" class="form-label">Firma</label>
                                    <select class="form-select" id="customerCompany" name="customerCompany">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Firma 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerGroup" class="form-label">Müşteri Grup</label>
                                    <select class="form-select" id="customerGroup" name="customerGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Grup 1</option>
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label for="customerType" class="form-label">Müşteri Tipi</label>
                                    <select class="form-select" id="customerType" name="customerType">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Tip 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerCriteria" class="form-label">Müşteri Kriteri</label>
                                    <select class="form-select" id="customerCriteria" name="customerCriteria">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Kriter 1</option>
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label for="officialAgent" class="form-label">Yetkili Agent</label>
                                    <select class="form-select" id="officialAgent" name="officialAgent">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Agent 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mb-2">
                                <label for="customerStatus" class="form-label">Durum</label>
                                <select class="form-select" id="customerStatus" name="customerStatus">
                                    <option value="" selected disabled>Seçiniz...</option>
                                    <option value="1">Aktif</option>
                                    <option value="0">Pasif</option>
                                    <option value="2">Beklemede</option>
                                </select>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="externalCustomerId" class="form-label">Harici Müşteri ID</label>
                                    <input type="text" class="form-control" id="externalCustomerId" name="externalCustomerId">
                                </div>
                                <div class="col-sm-6">
                                    <label for="ivrTarget" class="form-label">D. IVR Hedefi</label>
                                    <select class="form-select" id="ivrTarget" name="ivrTarget">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Hedef 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="customerDescription" class="form-label">Açıklama</label>
                                <textarea id="customerDescription" name="customerDescription" class="form-control" style="resize: none !important;" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- Tuşlama Ayarları Tab -->
                        <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerPhone" class="form-label">Cep Telefonu</label>
                                    <input type="text" class="form-control" id="customerPhone" name="customerPhone">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerLandline" class="form-label">Sabit Telefon</label>
                                    <input type="text" class="form-control" id="customerLandline" name="customerLandline">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerFax" class="form-label">Faks</label>
                                    <input type="text" class="form-control" id="customerFax" name="customerFax">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerMail" class="form-label">E-Posta</label>
                                    <input type="email" class="form-control" id="customerMail" name="customerMail">
                                </div>
                            </div>

                            <div class="col-12 mb-2">
                                <label for="customerAddress" class="form-label">Adres</label>
                                <textarea id="customerAddress" name="customerAddress" class="form-control" style="resize: none !important;" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-sm-4">
                                    <label for="customerCity" class="form-label">Şehir</label>
                                    <input type="text" class="form-control" id="customerCity" name="customerCity">
                                </div>
                                <div class="col-sm-4">
                                    <label for="customerDistrict" class="form-label">İlçe</label>
                                    <input type="text" class="form-control" id="customerDistrict" name="customerDistrict"> 
                                </div>
                                <div class="col-sm-4">
                                    <label for="customerCountry" class="form-label">Ülke</label>
                                    <input type="text" class="form-control" id="customerCountry" name="customerCountry">
                                </div> 
                            </div>
                        </div>
                        
                        <!-- Durum Yönlendirmeleri Tab -->
                        <div class="tab-pane fade" id="access-content" role="tabpanel" aria-labelledby="access-tab">
                            <div class="mb-2">
                                <div class="custom-checkbox d-flex align-items-center">
                                    <input type="checkbox" id="customerCallAble" name="customerCallAble">
                                    <label class="custom-check me-2" for="customerCallAble">
                                        <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                    </label>
                                    <label class="form-check-label" for="customerCallAble">
                                        Çağrı Alabilir
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="custom-checkbox d-flex align-items-center">
                                    <input type="checkbox" id="customerSmsAble" name="customerSmsAble">
                                    <label class="custom-check me-2" for="customerSmsAble">
                                        <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                    </label>
                                    <label class="form-check-label" for="customerSmsAble">
                                        SMS Alabilir
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Finansal Bilgiler Tab -->
                        <div class="tab-pane fade" id="finance-content" role="tabpanel" aria-labelledby="finance-tab">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerTaxOffice" class="form-label">Vergi Dairesi</label>
                                    <input type="text" class="form-control" id="customerTaxOffice" name="customerTaxOffice">
                                </div>
                                <div class="col-sm-6">
                                    <label for="customerTaxNumber" class="form-label">Vergi No</label>
                                    <input type="text" class="form-control" id="customerTaxNumber" name="customerTaxNumber">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="stepNextBtn">Devam Et</button>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Filtre Modalı -->
<div class="modal fade customer-list-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Arayan No</label>
                            <input type="text" class="form-control" id="modalCaller" name="modalCaller" placeholder="Arayan No">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Telefon No ile ara...</label>
                            <input type="text" class="form-control" id="modalPhone" name="modalPhone" placeholder="Telefon No ile ara...">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Tip Seçin...</label>
                            <select class="form-select" id="modalType" name="modalType">
                                <option disabled selected>Tip Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Grup Seçin...</label>
                            <select class="form-select" id="modalGroup" name="modalGroup">
                                <option disabled selected>Grup Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Kriter Seçin...</label>
                            <select class="form-select" id="modalCriteria" name="modalCriteria">
                                <option disabled selected>Kriter Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Durum Seçin...</label>
                            <select class="form-select" id="modalStatus" name="modalStatus">
                                <option disabled selected>Durum Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Y.Agent Seçin...</label>
                            <select class="form-select" id="modalAgent" name="modalAgent">
                                <option disabled selected>Y.Agent Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

{% endblock %}