{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/customer/customer.css') }}">
{% endblock %}

{% block page_content %}

<section class="customer-groups-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Müşteri Grupları</h4>
        <div class="d-flex align-items-center route-links">
            <a>Müşteri Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/musteri-gruplari">Müşteri Grupları</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Grup Listesi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="edit-btn" id="editCustomerGroupBtn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2" id="addCustomerGroupBtn">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn" id="deleteSelectedBtn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                <table id="customerGroupTable" class="table table-striped">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <input type="checkbox" id="checkAll" name="checkAll">
                            </th>
                            <th>Adı</th>
                            <th>Açıklama</th>
                            <th>Ekleme Tarihi</th>
                            <th>Kayıtlı Müşteri</th>
                            <th class="no-sort">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded dynamically via DataTable Ajax -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>


<!-- Customer Group Modal -->
<div class="modal fade" id="customerGroupModal" tabindex="-1" aria-labelledby="customerGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerGroupModalLabel">Yeni Müşteri Grubu Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="customerGroupForm">
                <div class="modal-body">
                    <!-- Error Container -->
                    <div id="errorContainer" class="alert alert-danger" style="display: none;"></div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Grup Ayarları</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                        <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="title" class="form-label">Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="description" class="form-label">Açıklama</label>
                                    <input type="text" class="form-control" id="description" name="description">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" id="stepNextBtn" class="blue-btn">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Customer Group CRUD JavaScript -->
<script type="module">
    import GenericManager from '{{ asset("js/modules/generic/GenericManager.js") }}';
    import customerGroupConfig from '{{ asset("js/modules/config/customerGroupConfig.js") }}';

    document.addEventListener('DOMContentLoaded', function() {
        // Prevent multiple initialization
        if (window.customerGroupManagerInitialized) {
            return;
        }
        window.customerGroupManagerInitialized = true;

        // Initialize Generic Manager with Customer Group configuration
        const customerGroupManager = new GenericManager(customerGroupConfig);
        customerGroupManager.init();

        // Initialize DataTable (client-side) - check if already initialized
        let table;
        if ($.fn.DataTable.isDataTable(customerGroupConfig.selectors.dataTable)) {
            table = $(customerGroupConfig.selectors.dataTable).DataTable();
        } else {
            table = $(customerGroupConfig.selectors.dataTable).DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.',
                emptyTable: 'Tabloda herhangi bir veri mevcut değil',
                zeroRecords: 'Eşleşen kayıt bulunamadı'
            },
            columnDefs: [
                { orderable: false, targets: [0, 5] }
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });

                // Update button states after table redraw
                updateButtonStates();
            }
            });
        }

        // Load data from API
        async function loadCustomerGroups() {
            try {
                const response = await window.axios.get(customerGroupConfig.baseURL);
                const customerGroups = response.data.data || response.data;

                // Clear existing data
                table.clear();

                // Add new data
                if (Array.isArray(customerGroups)) {
                    customerGroups.forEach(group => {
                        const row = customerGroupConfig.formatters.formatTableRow(group);
                        table.row.add(row);
                    });
                }

                // Redraw table
                table.draw();
            } catch (error) {
                console.error('Error loading customer groups:', error);
                if (customerGroupManager && customerGroupManager.notifications) {
                    customerGroupManager.notifications.showError(customerGroupConfig.messages.loadError);
                }
            }
        }

        // Load initial data
        loadCustomerGroups();

        // Store table reference and load function globally for the manager
        customerGroupManager.dataTable = table;
        customerGroupManager.loadData = loadCustomerGroups;

        // Button state management
        function updateButtonStates() {
            const editBtn = document.querySelector(customerGroupConfig.selectors.editButton);
            const deleteBtn = document.querySelector(customerGroupConfig.selectors.deleteButton);
            const checkedBoxes = document.querySelectorAll(`${customerGroupConfig.selectors.dataTable} tbody input[type="checkbox"]:checked`);

            if (editBtn) {
                if (checkedBoxes.length === 1) {
                    editBtn.disabled = false;
                    editBtn.classList.remove('disabled-btn');
                } else {
                    editBtn.disabled = true;
                    editBtn.classList.add('disabled-btn');
                }
            }

            if (deleteBtn) {
                if (checkedBoxes.length > 0) {
                    deleteBtn.disabled = false;
                    deleteBtn.classList.remove('disabled-btn');
                } else {
                    deleteBtn.disabled = true;
                    deleteBtn.classList.add('disabled-btn');
                }
            }
        }

        // Checkbox event handlers
        $('#checkAll').on('change', function() {
            const checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });

        $(document).on('change', `${customerGroupConfig.selectors.dataTable} tbody input[type="checkbox"]`, function() {
            updateButtonStates();

            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                const allChecked = $(`${customerGroupConfig.selectors.dataTable} tbody input[type="checkbox"]`).length ===
                                 $(`${customerGroupConfig.selectors.dataTable} tbody input[type="checkbox"]:checked`).length;
                $('#checkAll').prop('checked', allChecked);
            }
        });

        // Edit button handler
        $(customerGroupConfig.selectors.editButton).on('click', function() {
            if (!this.disabled) {
                const checkedBox = document.querySelector(`${customerGroupConfig.selectors.dataTable} tbody input[type="checkbox"]:checked`);
                if (checkedBox) {
                    const customerGroupId = checkedBox.value;
                    customerGroupManager.showEditModal(customerGroupId);
                }
            }
        });

        // Initialize button states
        updateButtonStates();
    });
</script>


{% endblock %}