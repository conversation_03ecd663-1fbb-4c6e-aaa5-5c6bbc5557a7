{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/queue/queue.css') }}">
{% endblock %}

{% block page_content %}

<section class="queue-list-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Kuyruk Listesi</h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Karşılama Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/grup-kullanicilari">Kuyruk Listesi</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON><PERSON><PERSON> Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="dark-green-btn" id="addInternalBtn">
                            <span class="iconify" data-icon="hugeicons:file-add" data-inline="false"></span>
                            <span class="ms-2">Arama Alacak Dahili Seç</span>
                        </button>
                        <button class="edit-btn ms-2">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item internal-add-menu-item" id="mobileAddInternalBtn">
                                <span class="iconify" style="border-radius: 6px;   background-color: #008C3F;" data-icon="hugeicons:file-add" data-inline="false"></span>
                                <span class="text">Arama Alacak Dahili Seç</span>
                            </div>
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                    'ID',
                    'Kuyruk Adı',
                    'Müzik Grubu',
                    'Max. Bekleme',
                    'Periyodik Anons',
                    'Sıra Anonsu',
                    'Çağrı Dağıtımı',
                    'Dahil Grubu',
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox' },
                    '41', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                  [
                    { type: 'checkbox' },
                    '42', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                  [
                    { type: 'checkbox' },
                    '43', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                  [
                    { type: 'checkbox' },
                    '44', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                  [
                    { type: 'checkbox' },
                    '45', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                  [
                    { type: 'checkbox' },
                    '46', 'Lorem Ipsum', '', '5 Saniye', 'Pasif', 'Aktif', 'Ardışık', 'Arama'
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'queueListTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Kuyruk Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="anons-tab" data-bs-toggle="tab" data-bs-target="#anons" type="button" role="tab" aria-controls="anons" aria-selected="false">Anons Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="">
                        <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="internalGroup" class="form-label">Dahili Grubu</label>
                                    <select class="form-select" id="internalGroup" name="internalGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Yönetim</option>
                                        <option>İK</option>
                                        <option>Teknik</option>
                                        <option>Müşteri Hizmetleri</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="queueName" class="form-label">Kuyruk Adı</label>
                                    <input type="text" class="form-control" id="queueName" name="queueName">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="musicGroup" class="form-label">Bekleme Müzik Grubu</label>
                                    <select class="form-select" id="musicGroup" name="musicGroup">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="tryCount" class="form-label">Deneme Sayısı</label>
                                    <select class="form-select" id="tryCount" name="tryCount">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="maxWaitTime" class="form-label">Max. Kuyrukta Bekleme Süresi</label>
                                    <select class="form-select" id="maxWaitTime" name="maxWaitTime">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="maxTimeExceeded" class="form-label">Max. Süre Aşılırsa</label>
                                    <select class="form-select" id="maxTimeExceeded" name="maxTimeExceeded">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            <label class="form-label" for="">Kullanıcı Meşgulse Bile Dene</label>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="callInterval" class="form-label">Aramalar Arası Süre</label>
                                    <select class="form-select" id="callInterval" name="callInterval">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="distributionMethod" class="form-label">Aramaları Dağıtım Şekli</label>
                                    <select class="form-select" id="distributionMethod" name="distributionMethod">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-2 custom-checkbox d-flex align-items-center">
                                <input type="checkbox" id="allowedInternalCall">
                                <label class="custom-check me-2" for="allowedInternalCall">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="allowedInternalCall">
                                    Müsait Kullanıcı Yoksa Bile Kabul Et
                                </label>
                            </div>
                            <div class="mb-2 custom-checkbox d-flex align-items-center">
                                <input type="checkbox" id="allowedCallWithoutUsers">
                                <label class="custom-check me-2" for="allowedCallWithoutUsers">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="allowedCallWithoutUsers">
                                    Hiç Kullanıcı Yoksa Bile Kabul Et
                                </label>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="callDelay" class="form-label">Çağrı Geciktirme</label>
                                    <select class="form-select" id="callDelay" name="callDelay">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="internalTime" class="form-label">Dahili Çalma Süresi</label>
                                    <select class="form-select" id="internalTime" name="internalTime">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="sla" class="form-label">SLA Yanıtlama Süresi</label>
                                    <select class="form-select" id="sla" name="sla">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Anons Tab -->
                        <div class="tab-pane fade" id="anons" role="tabpanel" aria-labelledby="anons-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="anonsPeriod" class="form-label mb-0">Periyodik Anons Süresi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="anonsPeriod" name="anonsPeriod">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="anonsRecording" class="form-label mb-0">Periyodik Anons Ses Kaydı</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle"  data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="anonsRecording" name="anonsRecording">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <span class="iconify me-2" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        <input type="checkbox" id="queueAnons">
                                        <label class="custom-check me-2" for="queueAnons">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="queueAnons">
                                            Kuyruktaki Sıra Anonsu
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-12">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="queueAnonsInterval" class="form-label mb-0">Sıra Anonsu Aralığı</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="queueAnonsInterval" name="queueAnonsInterval">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="queueChangeAnonsInterval" class="form-label mb-0">Sıra Değişimi Anons Aralığı</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="queueChangeAnonsInterval" name="queueChangeAnonsInterval">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="maxQueueLimit" class="form-label mb-0">Maksimum Sıra Limiti</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <input type="text" class="form-control" id="maxQueueLimit" name="maxQueueLimit">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="queueAnonsAudio" class="form-label mb-0">Sıra Anons Ses Kaydı</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="queueAnonsAudio" name="queueAnonsAudio">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="firstInQueueAnons" class="form-label mb-0">Birinci Sıradasınız Anonsu</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <input type="text" class="form-control" id="firstInQueueAnons" name="firstInQueueAnons">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="queueChangeAnonsInterval" class="form-label mb-0">Sıra Anonsu Sonrası Geçiş Sesi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="queueChangeAnonsInterval" name="queueChangeAnonsInterval">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="soundAfterTransition" class="form-label mb-0">Geçiş Sesi Sonrası Son Anons</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <input type="text" class="form-control" id="soundAfterTransition" name="soundAfterTransition">
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>



<!-- Add Internal to Queue Modal -->
<div class="modal fade" id="addInternalModal" tabindex="-1" aria-labelledby="addInternalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addInternalModalLabel">Kuyruktaki Dahililer - (ID:41)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="p-4" style="background-color: #FCFCFB;">
                <div class="modal-body" style="overflow-y: auto; max-height: 450px;">
                    <div class="white-container px-0 py-4">
                         <div class="custom-data-table-wrapper">
                            {% set headers = [
                                'Dahili Numarası',
                                'Dahili Açıklama',
                                'Arama Kuyruğunda Kalsın Mı?',
                                'Çağrı Karşılama Önceliği'
                            ] %}

                            {% set rows = [
                                ['101', 'Müdür', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['102', 'Asistan', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['103', 'Satış', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['104', 'Teknik', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['105', 'Muhasebe', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ]
                            ] %}

                            {% include 'components/table.twig' with {
                                id: 'queueModalTable1',
                                headers: headers,
                                rows: rows
                            } %}
                        </div>
                    </div>
                    <div class="white-container px-0 py-4 mt-3">
                         <div class="custom-data-table-wrapper">
                            {% set headers = [
                                'Harici Numara',
                                'Harici No Açıklama',
                                'Arama Kuyruğunda Kalsın Mı?',
                                'Çağrı Karşılama Önceliği'
                            ] %}

                            {% set rows = [
                                ['101', 'Müdür', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['102', 'Asistan', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['103', 'Satış', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['104', 'Teknik', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ],
                                ['105', 'Muhasebe', 
                                    { type: 'html', html: '<label class="custom-checkbox"><input type="checkbox" class="queue-stay-check" checked/><span class="custom-check"><span class="iconify" data-icon="material-symbols:check-small-rounded" data-inline="false"></span></span></label>' },
                                    { type: 'html', html: '<select class="form-select priority-select"><option value="5">Çok Yüksek</option><option value="4">Yüksek</option><option value="3" selected>Normal</option><option value="2">Düşük</option><option value="1">Çok Düşük</option></select>' }
                                ]
                            ] %}

                            {% include 'components/table.twig' with {
                                id: 'queueModalTable2',
                                headers: headers,
                                rows: rows
                            } %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="white-btn" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="blue-btn" id="saveInternalBtn">Kaydet</button>
            </div>
        </div>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#queueListTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit ve delete butonlarını başlangıçta disabled yap
        window.editBtn = document.querySelector('.edit-btn');
        window.deleteBtn = document.querySelector('.delete-btn');
        window.darkGreenBtn = document.querySelector('.dark-green-btn');
        
        window.editBtn.disabled = true;
        window.deleteBtn.disabled = true;
        window.darkGreenBtn.disabled = true;
        
        window.editBtn.classList.add('disabled-btn');
        window.deleteBtn.classList.add('disabled-btn');
        window.darkGreenBtn.classList.add('disabled-btn');
        
        // İlk yükleme sırasında mobil menü öğelerinin durumunu da güncelle
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#queueListTable tbody input[type="checkbox"]:checked');
            
            // Edit ve dark-green (Kuyruğa Dahili Ekle) butonları sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                window.editBtn.disabled = false;
                window.editBtn.classList.remove('disabled-btn');
                window.darkGreenBtn.disabled = false;
                window.darkGreenBtn.classList.remove('disabled-btn');
            } else {
                window.editBtn.disabled = true;
                window.editBtn.classList.add('disabled-btn');
                window.darkGreenBtn.disabled = true;
                window.darkGreenBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                window.deleteBtn.disabled = false;
                window.deleteBtn.classList.remove('disabled-btn');
            } else {
                window.deleteBtn.disabled = true;
                window.deleteBtn.classList.add('disabled-btn');
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#queueListTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#queueListTable tbody input[type="checkbox"]').length === 
                                 $('#queueListTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        window.editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#queueListTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();
                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Kuyruk Düzenle';
                editModal.show();
            }
        });
        
        // Dahili Ekle butonuna tıklandığında
        document.getElementById('addInternalBtn').addEventListener('click', function() {
            if (!window.darkGreenBtn.disabled) {
                var checkedRow = document.querySelector('#queueListTable tbody input[type="checkbox"]:checked');
                if (checkedRow) {
                    // Seçili satırın ID'sini al
                    var rowData = table.row(checkedRow.closest('tr')).data();
                    var queueId = rowData[1]; // ID değeri
                    
                    // Modal başlığını güncelle
                    document.getElementById('addInternalModalLabel').textContent = 'Kuyruktaki Dahililer - (ID:' + queueId + ')';
                    
                    // Modal'ı aç
                    var internalModal = new bootstrap.Modal(document.getElementById('addInternalModal'));
                    internalModal.show();
                }
            }
        });
        
        // Mobil dahili ekle butonuna tıklandığında
        document.getElementById('mobileAddInternalBtn').addEventListener('click', function() {
            if (!window.darkGreenBtn.disabled) {
                document.getElementById('addInternalBtn').click();
            }
            document.querySelector('.dropdown-menu').classList.remove('show');
        });
        
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Kuyruk Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Kuyruk Ekle';
            
            // Form alanlarını temizle
            const formInputs = document.querySelectorAll('#addModal input, #addModal select');
            formInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });


        var modalTable = $('#queueModalTable1').DataTable({
            searching: false,
            pageLength: 3,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });


        var modalTable2 = $('#queueModalTable2').DataTable({
            searching: false,
            pageLength: 3,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
    });
</script>



{% endblock %}