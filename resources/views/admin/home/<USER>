{% block styles %}
    <link rel="stylesheet" href="{{ asset('css/home/<USER>') }}">
{% endblock %}

{% block page_content %}
    <section class="home-section p-4">
        <div class="row row-cols-xl-3 row-cols-1" style="row-gap: 1.5rem;">
              <div class="col">
                <div class="white-container p-0">
                  <div class="d-flex align-items-start justify-content-between p-4">
                      <div class="d-flex flex-column ">
                          <h4 class="custom-title" style="font-size: 1rem;"><PERSON>elen Çağrı İstatistikleri</h4>
                          <small class="mt-1 xs-text" style="color: var(--gray); font-weight: 400;">Bugün</small>
                      </div>
                      <div class="d-flex align-items-center ">
                          <small class="fw-semibold" style="color: var(--black);">250</small>
                          <span class="iconify" data-icon="hugeicons:arrow-down-02" data-inline="false" style="color: var(--red); font-size: 16px;"></span>
                      </div>
                  </div>
                   <hr class="custom-hr mt-0">
                   <div class="px-4">
                      <div class="chart-container position-relative" style="height: 160px;">
                        <canvas id="incomingCallsChart"></canvas>
                        <div class="chart-center-value">250</div>
                      </div>
                   </div>
                    <hr class="custom-hr mt-3">
                    <div class="d-flex justify-content-center gap-4 mb-3">
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #12CC50;"></span>
                          <span class="chart-legend-text">Yanıtlandı <strong>200</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #E45158;"></span>
                          <span class="chart-legend-text">Yanıtlanmadı <strong>50</strong></span>
                        </div>
                    </div>
                </div>
              </div>
              <div class="col">
                <div class="white-container p-0">
                  <div class="d-flex align-items-start justify-content-between p-4">
                      <div class="d-flex flex-column ">
                          <h4 class="custom-title" style="font-size: 1rem;">Giden Çağrı İstatistikleri</h4>
                          <small class="mt-1 xs-text" style="color: var(--gray); font-weight: 400;">Bugün</small>
                      </div>
                      <div class="d-flex align-items-center ">
                          <small class="fw-semibold" style="color: var(--black);">250</small>
                          <span class="iconify" data-icon="hugeicons:arrow-up-02" data-inline="false" style="color: var(--green); font-size: 16px;"></span>
                      </div>
                  </div>
                  <hr class="custom-hr mt-0">
                  <div class="px-4">
                      <div class="chart-container position-relative" style="height: 160px;">
                        <canvas id="outgoingCallsChart"></canvas>
                        <div class="chart-center-value">400</div>
                      </div>
                  </div>
                  <hr class="custom-hr mt-3">
                    <div class="d-flex justify-content-center gap-4 mb-3">
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #12CC50;"></span>
                          <span class="chart-legend-text">Başarılı <strong>350</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #E45158;"></span>
                          <span class="chart-legend-text">Başarısız <strong>50</strong></span>
                        </div>
                      </div>
                </div>
              </div>
              <div class="col">
                <div class="white-container p-0">
                  <div class="d-flex align-items-start justify-content-between p-4">
                      <div class="d-flex flex-column ">
                          <h4 class="custom-title" style="font-size: 1rem;">Genel Çağrı İstatistikleri</h4>
                          <small class="mt-1 xs-text" style="color: var(--gray); font-weight: 400;">Bugün</small>
                      </div>
                      <div class="d-flex align-items-center ">
                          <small class="fw-semibold" style="color: var(--black);">250</small>
                          <span class="iconify" data-icon="hugeicons:arrow-down-02" data-inline="false" style="color: var(--red); font-size: 16px;"></span>
                      </div>
                  </div>
                   <hr class="custom-hr mt-0">
                   <div class="px-4 ">
                      <div class="chart-container position-relative" style="height: 160px;">
                        <canvas id="totalCallsChart"></canvas>
                        <div class="chart-center-value">275</div>
                      </div>
                   </div>
                    <hr class="custom-hr mt-3">
                    <div class="d-flex  mb-3 px-3 desktop-legend" style="flex-wrap: nowrap;">
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #12CC50;"></span>
                          <span class="chart-legend-text">Gelen <strong>125</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #E45158;"></span>
                          <span class="chart-legend-text">Giden <strong>70</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #6B7AED;"></span>
                          <span class="chart-legend-text">Dahili <strong>50</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                          <span class="chart-legend" style="background-color: #E7DB31;"></span>
                          <span class="chart-legend-text">Harici <strong>30</strong></span>
                        </div>
                      </div>
                </div>
              </div>
        </div>

        <div class="white-container mt-3 p-0 w-100">
            <h4 class="custom-title p-4">Duyurular</h4>
            <hr class="custom-hr my-0">
            <div class="p-4 d-flex justify-content-between align-items-start">
                <div class="ann-table-wrapper" style="flex:1;">
                    <table class="table ann-table">
                        <thead class="table-light">
                            <tr >
                                <th scope="col" id="dateHeader" style="cursor:pointer;">
                                    Tarih
                                    <span class="iconify" data-icon="bi:sort-alpha-down" data-inline="false" id="dateSortIcon" style="font-size: 16px;"></span>
                                </th>
                                <th scope="col">Başlık</th>
                                <th scope="col">Duyuru</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>12.06.2025</td>
                                <td>Bakım Çalışması</td>
                                <td>Sunucularımızda 13 Haziran'da bakım yapılacaktır.</td>
                            </tr>
                            <tr>
                                <td>10.06.2025</td>
                                <td>Yeni Özellik</td>
                                <td>CRM sistemine yeni raporlama modülü eklendi.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
            </div>
        </div>
    </section>


<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var select = document.getElementById('callStatistics');
    function updateSelectColor() {
        if(select.value === '' || select.value === undefined || select.value === null || select.selectedIndex === 0) {
            select.style.color = '#94A3B8';
        } else {
            select.style.color = '#111827';
        }
    }
    updateSelectColor();
    select.addEventListener('change', updateSelectColor);
});
</script>



<script>
document.addEventListener('DOMContentLoaded', function() {
    let sortAsc = true;
    const table = document.querySelector('.ann-table');
    const dateHeader = document.getElementById('dateHeader');
    const icon = document.getElementById('dateSortIcon');
    dateHeader.addEventListener('click', function() {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        rows.sort((a, b) => {
            const dateA = a.children[0].textContent.trim().split('.').reverse().join('-');
            const dateB = b.children[0].textContent.trim().split('.').reverse().join('-');
            if (sortAsc) {
                return dateA.localeCompare(dateB);
            } else {
                return dateB.localeCompare(dateA);
            }
        });
        rows.forEach(row => tbody.appendChild(row));
        sortAsc = !sortAsc;
        // İkonu değiştirme, hep aynı kalsın
    });
    // Başlangıçta ikonu doğru şekilde ayarla
    icon.setAttribute('data-icon', 'bi:sort-alpha-down');
});
</script>

<script>
// Chart.js legend for chart area
function renderChartLegend() {
    const legendHtml = `
        <div class="d-flex flex-row align-items-center gap-3">
            <div class="d-flex align-items-center"><span style="display:inline-block;width:16px;height:16px;background:#93E3B8;border-radius:4px;margin-right:8px;"></span><span style="color:#94A3B8;font-size:12px;font-weight:500;">Yanıtlandı</span></div>
            <div class="d-flex align-items-center"><span style="display:inline-block;width:16px;height:16px;background:#E45158;border-radius:4px;margin-right:8px;"></span><span style="color:#94A3B8;font-size:12px;font-weight:500;">Yanıtlanmadı</span></div>
            <div class="d-flex align-items-center"><span style="display:inline-block;width:16px;height:16px;background:#ABC9FE;border-radius:4px;margin-right:8px;"></span><span style="color:#94A3B8;font-size:12px;font-weight:500;">Toplam</span></div>
        </div>
    `;
    document.getElementById('chartLegendContainer').innerHTML = legendHtml;
}
document.addEventListener('DOMContentLoaded', renderChartLegend);
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Admin nav dropdown
    document.querySelectorAll('.admin-dropdown-toggle').forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentElement;
            const menu = parent.querySelector('.admin-dropdown-menu');
            // Diğer açık menüleri kapat
            document.querySelectorAll('.admin-dropdown-menu').forEach(function(m) {
                if(m !== menu) m.style.display = 'none';
            });
            // Toggle
            if(menu.style.display === 'block') {
                menu.style.display = 'none';
                document.removeEventListener('mousedown', closeAdminDropdownOnClickOutside);
            } else {
                menu.style.display = 'block';
                setTimeout(function() {
                    document.addEventListener('mousedown', closeAdminDropdownOnClickOutside);
                }, 0);
            }
            function closeAdminDropdownOnClickOutside(ev) {
                if (!menu.contains(ev.target) && !toggle.contains(ev.target)) {
                    menu.style.display = 'none';
                    document.removeEventListener('mousedown', closeAdminDropdownOnClickOutside);
                }
            }
        });
    });
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Configuration for all pie charts
  const pieChartConfig = {
    type: 'pie',
    options: {
      responsive: true,
      maintainAspectRatio: false,
      cutout: '60%',
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        }
      },
      elements: {
        arc: {
          borderWidth: 0
        }
      }
    }
  };
  
  // Incoming Calls Chart
  new Chart(document.getElementById('incomingCallsChart'), {
    ...pieChartConfig,
    data: {
      labels: ['Yanıtlandı', 'Yanıtlanmadı'],
      datasets: [{
        data: [200, 50],
        backgroundColor: [
          '#12CC50',  // Green for answered
          '#E45158',  // Red for missed
        ],
        hoverOffset: 4
      }]
    }
  });
  
  // Outgoing Calls Chart
  new Chart(document.getElementById('outgoingCallsChart'), {
    ...pieChartConfig,
    data: {
      labels: ['Başarılı', 'Başarısız'],
      datasets: [{
        data: [350, 50],
        backgroundColor: [
          '#12CC50',  // Green for successful
          '#E45158',  // Red for failed
        ],
        hoverOffset: 4
      }]
    }
  });
  
  // Total Calls Chart 
  new Chart(document.getElementById('totalCallsChart'), {
    ...pieChartConfig,
    data: {
      labels: ['Gelen', 'Giden', 'Dahili', 'Harici Y.'],
      datasets: [{
        data: [125, 70, 50, 30],
        backgroundColor: [
          '#12CC50',  // Green for incoming
          '#E45158',  // Red for outgoing
          '#6B7AED',  // Blue for internal
          '#E7DB31',  // Yellow for external
        ],
        hoverOffset: 4
      }]
    }
  });
});
</script>
</div>
{% endblock %}

