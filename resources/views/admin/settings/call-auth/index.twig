{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/settings/settings.css') }}">
{% endblock %}


{% block page_content %}

<section class="call-auth-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Arama Yetkilendirme</h4>
        <div class="d-flex align-items-center route-links">
            <a>Sistem Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/arama-yetkilendirme">Arama Yetkilendirme</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Arama Yetkilendirme Grupları</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                       
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'ID',
                  'Açıklama',
                  'Oluşturma Tarihi',
                  'Son Düzenleme Tarihi',
                  'İşlem'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '1',
                        'Prefix Yetkisi',
                        '2025-02-17 15:33:0',
                        '2025-02-17 15:33:0',
                        { type: 'html', html: '<button class="prefix-btn">
                        Prefix Listesi
                        <span class="iconify ms-2" data-icon="hugeicons:arrow-right-02" data-inline="false" style="font-size:16px;"></span>
                        </button>' }
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1',
                        'Prefix Yetkisi',
                        '2025-02-17 15:33:0',
                        '2025-02-17 15:33:0',
                        { type: 'html', html: '<button class="prefix-btn">
                        Prefix Listesi
                        <span class="iconify ms-2" data-icon="hugeicons:arrow-right-02" data-inline="false" style="font-size:16px;"></span>
                        </button>' }
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1',
                        'Prefix Yetkisi',
                        '2025-02-17 15:33:0',
                        '2025-02-17 15:33:0',
                        { type: 'html', html: '<button class="prefix-btn">
                        Prefix Listesi
                        <span class="iconify ms-2" data-icon="hugeicons:arrow-right-02" data-inline="false" style="font-size:16px;"></span>
                        </button>' }
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'callAuthTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>

    <div class="p-4 ">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Prefix Listesi | Grup Adı : Prefix Yetkisi | Grup ID : 17</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="prefix-btn me-2">
                            <span class="iconify me-2" data-icon="hugeicons:arrow-left-02" data-inline="false"></span>
                            <span class="text">Grup Listesine Geri Dön</span>
                        </button>
                        <button class="edit-btn">
                            <span class="iconify " data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify " data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify " data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item" id="mobileBackBtn">
                                <span class="iconify prefix-btn" data-icon="hugeicons:arrow-left-02" data-inline="false"></span>
                                <span class="text">Grup Listesine Geri Dön</span>
                            </div>
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Prefix',
                  'Yetkisi',
                  'Grup Adı',
                  'Grup ID'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '1234',
                        'Yetkili',
                        'Prefix Yetkisi',
                        '17'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'prefixTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade ivr-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Mesai Saati Şablonu Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <!-- Grup Adı alanı - callAuthTable için gösterilecek -->
                            <div class="row mb-2 group-name-field">
                                <div class="col-12">
                                    <label for="groupName" class="form-label">Grup Adı</label>
                                    <input type="text" class="form-control" id="groupName" name="groupName">
                                </div>
                            </div>
                            <!-- Prefix alanı - prefixTable için gösterilecek -->
                            <div class="row mb-2 prefix-field">
                                <div class="col-12">
                                    <label for="prefix" class="form-label">Prefix</label>
                                    <input type="text" class="form-control" id="prefix" name="prefix">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="saveModalBtn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tabloları ve ilgili butonları dinamik olarak işle
        var tableIds = ['callAuthTable', 'prefixTable'];
        var dataTables = {};
        tableIds.forEach(function(tableId) {
            if (document.getElementById(tableId)) {
                dataTables[tableId] = $('#' + tableId).DataTable({
                    searching: false,
                    pageLength: 10,
                    language: {
                        lengthMenu: '_MENU_',
                        info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
                    },
                    columnDefs: [
                        { orderable: false, targets: 0 }
                    ],
                    drawCallback: function(settings) {
                        // Paging button stillemesi
                        document.querySelectorAll('#' + tableId + ' .dt-paging-button.current').forEach(function(btn) {
                            btn.style.color = '#FFFFFF';
                            btn.querySelectorAll('*').forEach(function(child) {
                                child.style.color = '#FFFFFF';
                            });
                        });
                    }
                });
                // dt-length'i dt-paging'in soluna taşı
                var length = $(dataTables[tableId].table().container()).find('.dt-length');
                var paging = $(dataTables[tableId].table().container()).find('.dt-paging');
                paging.before(length);
            }
        });

        // Her tablo için butonları ve checkboxları işle
        tableIds.forEach(function(tableId) {
            var container = document.getElementById(tableId)?.closest('.white-container');
            if (!container) return;
            var editBtn = container.querySelector('.edit-btn');
            var deleteBtn = container.querySelector('.delete-btn');
            var addBtn = container.querySelector('.add-new-btn');
            
            // Mobil menü öğelerini bulalım
            var mobileMenu = container.querySelector('.responsive-menu');
            var mobileEditBtn = mobileMenu?.querySelector('.edit-menu-item');
            var mobileDeleteBtn = mobileMenu?.querySelector('.delete-menu-item');
            
            // Başlangıçta butonları disable et
            if (editBtn) {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
                if (mobileEditBtn) {
                    mobileEditBtn.classList.add('disabled-menu-item');
                }
            }
            if (deleteBtn) {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
                if (mobileDeleteBtn) {
                    mobileDeleteBtn.classList.add('disabled-menu-item');
                }
            }
            
            // Checkbox ve buton durumlarını güncelleyen fonksiyon
            function updateButtonStates() {
                var checkedBoxes = container.querySelectorAll('tbody input[type="checkbox"]:checked');
                if (editBtn) {
                    if (checkedBoxes.length === 1) {
                        editBtn.disabled = false;
                        editBtn.classList.remove('disabled-btn');
                        if (mobileEditBtn) {
                            mobileEditBtn.classList.remove('disabled-menu-item');
                        }
                    } else {
                        editBtn.disabled = true;
                        editBtn.classList.add('disabled-btn');
                        if (mobileEditBtn) {
                            mobileEditBtn.classList.add('disabled-menu-item');
                        }
                    }
                }
                if (deleteBtn) {
                    if (checkedBoxes.length > 0) {
                        deleteBtn.disabled = false;
                        deleteBtn.classList.remove('disabled-btn');
                        if (mobileDeleteBtn) {
                            mobileDeleteBtn.classList.remove('disabled-menu-item');
                        }
                    } else {
                        deleteBtn.disabled = true;
                        deleteBtn.classList.add('disabled-btn');
                        if (mobileDeleteBtn) {
                            mobileDeleteBtn.classList.add('disabled-menu-item');
                        }
                    }
                }
            }
            
            // DataTables jQuery kullanarak çalışalım
            var $table = $('#' + tableId);
            
            // Tümünü seç/deselect
            $table.on('change', 'thead input[type="checkbox"]', function() {
                var checked = this.checked;
                $table.find('tbody input[type="checkbox"]').prop('checked', checked);
                updateButtonStates();
            });
            
            // Herhangi bir checkbox değiştiğinde
            $table.on('change', 'tbody input[type="checkbox"]', function() {
                updateButtonStates();
                // checkAll kontrolü
                var allCheckboxes = $table.find('tbody input[type="checkbox"]').length;
                var checkedBoxes = $table.find('tbody input[type="checkbox"]:checked').length;
                $table.find('thead input[type="checkbox"]').prop('checked', allCheckboxes === checkedBoxes && allCheckboxes > 0);
            });
            
            // Edit butonuna tıklandığında
            if (editBtn) {
                editBtn.addEventListener('click', function() {
                    if (!this.disabled) {
                        var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                        // Modal başlığını tabloya göre ayarla
                        document.getElementById('addModalLabel').textContent = (tableId === 'callAuthTable') ? 'Grup Düzenle' : 'Prefix Düzenle';
                        
                        // Modal içeriğini tabloya göre ayarla
                        if (tableId === 'callAuthTable') {
                            document.querySelector('.group-name-field').style.display = 'block';
                            document.querySelector('.prefix-field').style.display = 'none';
                        } else { // prefixTable
                            document.querySelector('.group-name-field').style.display = 'none';
                            document.querySelector('.prefix-field').style.display = 'block';
                        }
                        
                        // Seçili kayıttan değerleri al
                        var checkedRow = container.querySelector('tbody input[type="checkbox"]:checked').closest('tr');
                        if (tableId === 'callAuthTable') {
                            // Grup adını al (3. sütun: açıklama)
                            var groupName = checkedRow.cells[2].textContent.trim();
                            document.getElementById('groupName').value = groupName;
                        } else { // prefixTable
                            // Prefix al (2. sütun: prefix)
                            var prefix = checkedRow.cells[1].textContent.trim();
                            document.getElementById('prefix').value = prefix;
                        }
                        
                        editModal.show();
                    }
                });
            }
            
            // Add new button modal functionality
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    // Modal başlığını tabloya göre ayarla
                    document.getElementById('addModalLabel').textContent = (tableId === 'callAuthTable') ? 'Grup Ekle' : 'Prefix Ekle';
                    
                    // Modal içeriğini tabloya göre ayarla
                    if (tableId === 'callAuthTable') {
                        document.querySelector('.group-name-field').style.display = 'block';
                        document.querySelector('.prefix-field').style.display = 'none';
                    } else { // prefixTable
                        document.querySelector('.group-name-field').style.display = 'none';
                        document.querySelector('.prefix-field').style.display = 'block';
                    }
                    
                    // Form alanlarını temizle
                    document.getElementById('groupName').value = '';
                    document.getElementById('prefix').value = '';
                    
                    var addModal = new bootstrap.Modal(document.getElementById('addModal'));
                    addModal.show();
                });
            }
            
            // Delete butonuna tıklandığında
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    if (!this.disabled) {
                        var checkedBoxes = container.querySelectorAll('tbody input[type="checkbox"]:checked');
                        if (checkedBoxes.length > 0) {
                            const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                            deleteModal.show();
                            // Not: Silme işlemi sonradan eklenecek
                        }
                    }
                });
            }
            
            // Kaydet butonuna işlevsellik ekle
            document.getElementById('saveModalBtn').addEventListener('click', function() {
                // Hangi tablodan açıldığını kontrol et
                var activeTable = '';
                var tableIds = ['callAuthTable', 'prefixTable'];
                
                tableIds.forEach(function(tableId) {
                    var container = document.getElementById(tableId)?.closest('.white-container');
                    if (!container) return;
                    var editBtn = container.querySelector('.edit-btn');
                    if (editBtn && !editBtn.disabled) {
                        activeTable = tableId;
                    }
                });
                
                // Aktif tablo yoksa, butonlardan kontrol et
                if (!activeTable) {
                    if (document.querySelector('.group-name-field').style.display === 'block') {
                        activeTable = 'callAuthTable';
                    } else {
                        activeTable = 'prefixTable';
                    }
                }
                
                // Değerleri al
                var value = '';
                if (activeTable === 'callAuthTable') {
                    value = document.getElementById('groupName').value;
                    // Burada API çağrısı yapabilirsiniz
                    console.log('Grup Kaydediliyor:', value);
                } else { // prefixTable
                    value = document.getElementById('prefix').value;
                    // Burada API çağrısı yapabilirsiniz
                    console.log('Prefix Kaydediliyor:', value);
                }
                
                // Modalı kapat
                var modal = bootstrap.Modal.getInstance(document.getElementById('addModal'));
                modal.hide();
                
                // Başarılı mesajı gösterilebilir
                alert(activeTable === 'callAuthTable' ? 'Grup kaydedildi!' : 'Prefix kaydedildi!');
            });
            
            // İlk yüklemede butonları güncelle
            updateButtonStates();
        });
        
        // Responsive ve mobil menü işlemleri (varsa)
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });

    // Prefix butonları ile scroll işlemleri
    var callAuthTable = document.getElementById('callAuthTable');
    var prefixTable = document.getElementById('prefixTable');
    // 1. tablodaki prefix-btn (grup listesinde)
    if (callAuthTable) {
        callAuthTable.addEventListener('click', function(e) {
            if (e.target.closest('.prefix-btn')) {
                if (prefixTable) {
                    prefixTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    }
    // 2. tablodaki prefix-btn (prefix listesinde geri dön)
    if (prefixTable) {
        prefixTable.closest('.white-container').addEventListener('click', function(e) {
            if (e.target.closest('.prefix-btn')) {
                if (callAuthTable) {
                    callAuthTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    }
</script>


{% endblock %}