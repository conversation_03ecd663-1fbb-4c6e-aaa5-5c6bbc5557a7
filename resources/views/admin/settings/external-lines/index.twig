{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/settings/settings.css') }}">
{% endblock %}


{% block page_content %}

<section class="external-lines-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Dış Hat İşlemleri</h4>
        <div class="d-flex align-items-center route-links">
            <a>Sistem Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/dis-hat-islemleri"><PERSON><PERSON><PERSON> İşlemleri</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Hat Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                       
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <!-- Desktop filters -->
                <div class="d-none d-md-flex align-items-center w-100">
                    <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">
                    <select class="form-select w-auto" style="min-width: 200px;" id="chooseGroup" name="chooseGroup" placeholder="Grup Seç...">
                        <option disabled selected>Grup Seç</option>
                        <option>Arama</option>
                    </select>
                </div>
                
                <!-- Mobile filter button -->
                <div class="d-md-none d-flex justify-content-between w-100">
                    <button class="blue-btn flex-grow-1 me-2" id="filterButton">Filtrele</button>
                    <button class="filter-icon-btn" id="filterIconButton">
                        <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                    </button>
                </div>
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'ID',
                  'Açıklama',
                  'IP/Domain',
                  'Kullanıcı Adı',
                  'Parola',
                  'Dahili Grubu',
                  'Mesai Şablonu',
                  'Yönlendirilen Hedef'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '1',
                        'Dış Hat 1',
                        '***********',
                        'kullanici1',
                        'parola1',
                        'Grup 1',
                        'Mesai 1',
                        'Hedef 1'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'externalLinesTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade ivr-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Hat Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="externalLineDescription" class="form-label">Açıklama</label>
                                    <input type="text" class="form-control" id="externalLineDescription" name="externalLineDescription">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="domain" class="form-label">IP/Domain</label>
                                    <input type="text" class="form-control" id="domain" name="domain">
                                </div>
                                <div class="col-md-6">
                                    <label for="username" class="form-label">Kullanıcı Adı</label>
                                    <input type="text" class="form-control" id="username" name="username">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="password" class="form-label">Parola</label>
                                    <input type="text" class="form-control" id="password" name="password">
                                </div>
                                <div class="col-md-6">
                                    <label for="internalGroup" class="form-label">Dahili Grubu</label>
                                    <select class="form-select" id="internalGroup" name="internalGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Grup 1</option>
                                        <option>Grup 2</option>
                                        <option>Grup 3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-2 custom-checkbox">
                                <input type="checkbox" id="receiveInternalDial">
                                <label class="custom-check me-2" for="receiveInternalDial">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="receiveInternalDial">
                                Çağrı Alabilir
                                </label>

                                <input type="checkbox" id="canMakeCall">
                                <label class="custom-check mx-2" for="canMakeCall">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="canMakeCall">
                                Çağrı Yapabilir
                                </label>

                                <input type="checkbox" id="faxInternalDial">
                                <label class="custom-check mx-2" for="faxInternalDial">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="faxInternalDial">
                                Faks Alabilir
                                </label>
                            </div>

                            <div class="d-flex align-items-center mb-3 custom-checkbox">
                                <input type="checkbox" id="incomingCallRecording">
                                <label class="custom-check me-2" for="incomingCallRecording">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="incomingCallRecording">
                                Gelen Çağrı Ses Kaydı
                                </label>

                                <input type="checkbox" id="outgoingCallRecording">
                                <label class="custom-check mx-2" for="outgoingCallRecording">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="outgoingCallRecording">
                                Giden Çağrı Ses Kaydı
                                </label>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="directedTarget" class="form-label">Yönlendirilen Hedef</label>
                                    <select class="form-select" id="directedTarget" name="directedTarget">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Hedef 1</option>
                                        <option>Hedef 2</option>
                                        <option>Hedef 3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="workingHoursTemplate" class="form-label">Mesai Saati Şablonu</label>
                                    <select class="form-select" id="workingHoursTemplate" name="workingHoursTemplate">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Şablon 1</option>
                                        <option>Şablon 2</option>
                                        <option>Şablon 3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="incomingCallFilter" class="form-label">Gelen Çağrı Filtresi</label>
                                    <select class="form-select" id="incomingCallFilter" name="incomingCallFilter">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Filtre 1</option>
                                        <option>Filtre 2</option>
                                        <option>Filtre 3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="outgoingCallFilter" class="form-label">Giden Çağrı Filtresi</label>
                                    <select class="form-select" id="outgoingCallFilter" name="outgoingCallFilter">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Filtre 1</option>
                                        <option>Filtre 2</option>
                                        <option>Filtre 3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2 d-flex align-items-end">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="lineSelectionCode" class="form-label mb-0">Hat Seçim Kodu</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="lineSelectionCode" name="lineSelectionCode">
                                        <option selected disabled>Seçiniz...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <span class="iconify me-1" data-icon="solar:shield-warning-linear" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        <small style="color: #94A3B8; font-size: 12px; font-weight: 400;">10 ile 99 arasında bir değer girebilirsiniz.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>


<!-- Filter Modal for Mobile -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header px-4">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body px-4">
                    <div class="mb-2">
                        <label for="mobileInternalNo" class="form-label">Numara ile ara</label>
                        <input type="text" class="form-control" id="mobileInternalNo" name="mobileInternalNo" placeholder="Numara ile ara...">
                    </div>
                    <div class="mb-2">
                        <label for="chooseModalGroup" class="form-label">Grup Seç</label>
                        <select class="form-select" id="chooseModalGroup" name="chooseModalGroup">
                            <option disabled selected>Grup Seç</option>
                            <option>Arama</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer px-4">
                    <button type="button" class="blue-btn w-100" id="applyFilters">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#externalLinesTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit ve delete butonlarını başlangıçta disabled yap
        window.editBtn = document.querySelector('.edit-btn');
        window.deleteBtn = document.querySelector('.delete-btn');
        
        window.editBtn.disabled = true;
        window.deleteBtn.disabled = true;
        
        window.editBtn.classList.add('disabled-btn');
        window.deleteBtn.classList.add('disabled-btn');
        
        // İlk sayfa yüklendiğinde buton durumlarını kontrol et
        updateButtonStates();
        
        // Mobil menü öğelerinin durumunu güncelle
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#externalLinesTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                window.editBtn.disabled = false;
                window.editBtn.classList.remove('disabled-btn');
            } else {
                window.editBtn.disabled = true;
                window.editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                window.deleteBtn.disabled = false;
                window.deleteBtn.classList.remove('disabled-btn');
            } else {
                window.deleteBtn.disabled = true;
                window.deleteBtn.classList.add('disabled-btn');
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#externalLinesTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#externalLinesTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#externalLinesTable tbody input[type="checkbox"]').length === 
                                 $('#externalLinesTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        window.editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Hat Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Dahili Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Hat Ekle';
            
            // Form alanlarını temizle
            const formInputs = document.querySelectorAll('#addModal input, #addModal select');
            formInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
        
        // Delete butonuna tıklandığında
        window.deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#externalLinesTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Not: Silme işlemi sonradan eklenecek
                }
            }
        });
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>


<script>
document.addEventListener('DOMContentLoaded', function() {
    var select = document.getElementById('chooseInternal');
    var mobileSelect = document.getElementById('mobileChooseInternal');
    
    function updateSelectColor() {
        if(select.value === '' || select.value === undefined || select.value === null || select.selectedIndex === 0) {
            select.style.color = '#94A3B8';
        } else {
            select.style.color = '#111827';
        }
    }
    updateSelectColor();
    select.addEventListener('change', updateSelectColor);
    
    // Mobile select color update
    function updateMobileSelectColor() {
        if(mobileSelect.value === '' || mobileSelect.value === undefined || mobileSelect.value === null || mobileSelect.selectedIndex === 0) {
            mobileSelect.style.color = '#94A3B8';
        } else {
            mobileSelect.style.color = '#111827';
        }
    }
    updateMobileSelectColor();
    mobileSelect.addEventListener('change', updateMobileSelectColor);
    
    // Filter modal functionality
    const filterButton = document.getElementById('filterButton');
    const filterIconButton = document.getElementById('filterIconButton');
    const filterModal = new bootstrap.Modal(document.getElementById('filterModal'));
    const applyFiltersButton = document.getElementById('applyFilters');
    const mobileInternalNo = document.getElementById('mobileInternalNo');
    const desktopInternalNo = document.getElementById('internalNo');
    
    // Sync filter values between desktop and mobile
    desktopInternalNo.addEventListener('input', function() {
        mobileInternalNo.value = this.value;
    });
    
    select.addEventListener('change', function() {
        mobileSelect.value = this.value;
        updateMobileSelectColor();
    });
    
    // Only icon button shows filter modal
    if (filterIconButton) {
        filterIconButton.addEventListener('click', function() {
            filterModal.show();
        });
    }
});
</script>

{% endblock %}