{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/call-manage/call-manage.css') }}">
{% endblock %}

{% block page_content %}

<div class="auto-call-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Otomatik Arama</h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/otomatik-arama">Otomatik Arama</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Ka<PERSON><PERSON><PERSON></h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="play-btn me-2">
                           <span class="iconify" data-icon="hugeicons:play" data-inline="false"></span>
                           <span class="text">Başlat</span>
                        </button>
                        <button class="pause-btn me-2">
                          <span class="iconify" data-icon="hugeicons:pause" data-inline="false"></span>
                          <span class="text">Duraksat</span>
                        </button>
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn me-2">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                        <div class="dropdown">
                            <button class="upload-data-btn dropdown-toggle" type="button" id="uploadDataDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="iconify" data-icon="solar:archive-up-minimlistic-linear" data-inline="false"></span>
                                <span class="text">Yükle</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="uploadDataDropdown">
                                <li><a class="dropdown-item" href="#">Excel'den Yükle</a></li>
                                <li><a class="dropdown-item" href="#">Data Listesinden Yükle</a></li>
                                <li><a class="dropdown-item" href="#">Tekil Data Yükle</a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item play-menu-item">
                                <span class="iconify play-btn" data-icon="hugeicons:play" data-inline="false"></span>
                                <span class="text">Başlat</span>
                            </div>
                            <div class="dropdown-menu-item pause-menu-item">
                                <span class="iconify pause-btn" data-icon="hugeicons:pause" data-inline="false"></span>
                                <span class="text">Duraksat</span>
                            </div>
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                            <div class="dropdown-menu-item upload-menu-item position-relative" id="mobileUploadMenuItem">
                                <span class="iconify upload-data-btn" data-icon="solar:archive-up-minimlistic-linear" data-inline="false"></span>
                                <span class="text">Yükle</span>
                                <span class="iconify ms-1" data-icon="mdi:chevron-down" style="font-size: 18px;"></span>
                                <ul class="dropdown-menu mobile-upload-dropdown" style="display: none; position: absolute; left: 0; top: 100%; width: 100%;  z-index: 1000;">
                                    <li><a class="dropdown-item" href="#">Excel'den Yükle</a></li>
                                    <li><a class="dropdown-item" href="#">Data Listesinden Yükle</a></li>
                                    <li><a class="dropdown-item" href="#">Tekil Data Yükle</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-100" id="startDate" name="startDate">
                                <input type="date" class="form-control w-100" id="endDate" name="endDate">
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                 <input type="text" class="form-control"  id="callerNumber" name="callerNumber" placeholder="Arayan Numara..." >
                                <input type="text" class="form-control"  id="dialedNumber" name="dialedNumber" placeholder="Aranan Numara..." >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="callerAgent" name="callerAgent">
                                    <option disabled selected>Geri Arayan Agent...</option>
                                    <option>Agent 1</option>
                                </select>

                                <select class="form-select w-auto" id="queueSelect" name="queueSelect">
                                    <option disabled selected>Kuyruk Seçin...</option>
                                    <option>Kuyruk 1</option>
                                </select>

                                
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="callStatusSelect" name="callStatusSelect">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Durum 1</option>
                                </select>

                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Oluşturma Tarihi',
                  'Kampanya Adı',
                  'Hedef Tipi',
                  'Dış Hat',
                  'Tamamlanan Data',
                  'Devam Eden Data',
                  'Toplam Data',
                  'Data Durumu',
                  'Ç.Durumu'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '2023-10-01',
                    'Kampanya 1',
                    'Tip 1',
                    '5551234567',
                    '100',
                    '50',
                    '150',
                    { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                    { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' }
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'autoCallTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade auto-call-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Kampanya Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Genel Ayarlar</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="time-settings-tab" data-bs-toggle="tab" data-bs-target="#time-settings" type="button" role="tab" aria-controls="time-settings" aria-selected="true">Gün ve Saat Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                        <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                            <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="doNotDisturb" class="form-label mb-0">Şube</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="branchSelect" name="branchSelect">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Şube 1</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="doNotDisturb" class="form-label mb-0">Kampanya Adı</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="text" class="form-control" id="campaignName" name="campaignName">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callDescription" class="form-label mb-0">Açıklama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <textarea id="callDescription" name="callDescription" class="form-control" style="resize: none !important;" rows="3"></textarea>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callStartDate" class="form-label mb-0">Başlangıç Tarihi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="date" class="form-control" id="callStartDate" name="callStartDate">
                                    </div>

                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callEndDate" class="form-label mb-0">Bitiş Tarihi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="date" class="form-control" id="callEndDate" name="callEndDate">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="externalLine" class="form-label mb-0">Dış Hat</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="externalLine" name="externalLine">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Hat 1</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callAttempt" class="form-label mb-0">Arama Denemesi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callAttempt" name="callAttempt">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Deneme 1</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="targetType" class="form-label mb-0">Hedef Tipi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="targetType" name="targetType">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Hedef 1</option>
                                        </select>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callAgain" class="form-label mb-0">Tekrar Arama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callAgain" name="callAgain">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Arama 1</option>
                                        </select>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callSimultaneously" class="form-label mb-0">Eşzamanlı Arama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callSimultaneously" name="callSimultaneously">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Arama 1</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade p-0" id="time-settings" role="tabpanel" aria-labelledby="time-settings-tab">
                                    <div class="custom-data-table-wrapper">
                                            {% set headers = [
                                            'Gün',
                                            'Durum',
                                            'Arama Başlangıcı',
                                            'Arama Bitişi'
                                            ] %}

                                            {% set rows = [
                                            [
                                                
                                                'Pazartesi',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                                
                                            ],
                                            [
                                                
                                                'Salı',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            [
                                                
                                                'Çarşamba',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            [
                                                
                                                'Perşembe',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            [
                                                
                                                'Cuma',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            [
                                                
                                                'Cumartesi',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            [
                                                
                                                'Pazar',
                                                { type: 'checkbox', class: 'row-check' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                                { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                                
                                            ],
                                            ] %}

                                            {% include 'components/table.twig' with {
                                                id: 'timeSettingsTable',
                                                headers: headers,
                                                rows: rows,
                                                class: 'time-settings-table'
                                            } %}
                                    </div>
                            </div>
                        </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="modal fade auto-call-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate" name="modalStartDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate" name="modalEndDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Arayan Numara</label>
                            <input type="text" class="form-control" id="modalCallerNumber" name="modalCallerNumber" placeholder="Arayan Numara">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Aranan Numara</label>
                            <input type="text" class="form-control" id="modalDialedNumber" name="modalDialedNumber" placeholder="Aranan Numara">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Geri Arayan Agent</label>
                            <select class="form-select" id="modalReturningAgent" name="modalReturningAgent">
                                <option disabled selected>Geri Arayan Agent...</option>
                                <option>Agent 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Kuyruk</label>
                            <select class="form-select" id="modalQueueSelect" name="modalQueueSelect">
                                <option disabled selected>Kuyruk Seçin...</option>
                                <option>Kuyruk 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Çağrı Durumu</label>
                            <select class="form-select" id="modalCallStatusSelect" name="modalCallStatusSelect">
                                <option disabled selected>Çağrı Durumu Seçin</option>
                                <option>Durum 1</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#autoCallTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        var deleteBtn = document.querySelector('.delete-btn');
        editBtn.disabled = true;
        deleteBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        deleteBtn.classList.add('disabled-btn');

                // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#autoCallTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#autoCallTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#autoCallTable tbody input[type="checkbox"]').length === 
                                 $('#autoCallTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#autoCallTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();

                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Kampanya Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Kampanya Ekle';
            
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#autoCallTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        updateButtonStates();
    });
</script>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#timeSettingsTable').DataTable({
            searching: false,
            paging: false,
            info: false,
            lengthChange: false,
            bLengthChange: false,
            dom: 't',
            order: [],
            ordering: true,
            autoWidth: false,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 1 } // 2. sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşıma işlemi gereksiz oldu
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Upload Dropdown
    var mobileUploadMenuItem = document.getElementById('mobileUploadMenuItem');
    if (mobileUploadMenuItem) {
        var mobileDropdown = mobileUploadMenuItem.querySelector('.mobile-upload-dropdown');
        mobileUploadMenuItem.addEventListener('click', function(e) {
            e.stopPropagation();
            if (mobileDropdown.style.display === 'block') {
                mobileDropdown.style.display = 'none';
            } else {
                // Kapatılacak başka açık menü varsa kapat
                document.querySelectorAll('.mobile-upload-dropdown').forEach(function(el) {
                    el.style.display = 'none';
                });
                mobileDropdown.style.display = 'block';
            }
        });
        // Sayfanın başka yerine tıklanınca menüyü kapat
        document.addEventListener('click', function() {
            mobileDropdown.style.display = 'none';
        });
    }
});
</script>

{% endblock %}