{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/call-manage/call-manage.css') }}">
{% endblock %}

{% block page_content %}

<div class="planned-calls-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Planlanmış Çağrılar</h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/planlanmis-cagrilar">Planlanmış Çağrılar</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON>lanmış Çağrı Listesi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 w-100 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 w-100 mobile-excel-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="dateFilterOption" name="dateFilterOption" >
                                    <option disabled selected>Tarih Filtreleme Seçeneği</option>
                                    <option>Aktif</option>
                                </select>
                                <select class="form-select w-auto"  id="callResultType" name="callResultType">
                                    <option disabled selected>Çağrı Sonuç Tipi...</option>
                                    <option>Arama</option>
                                </select>
                                <input type="text" class="form-control w-auto" id="nameSearch" name="nameSearch" placeholder="Adı Soyadı içinde ara" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="startDate" name="startDate" >
                                <select class="form-select w-auto"  id="branchSelect" name="branchSelect">
                                    <option disabled selected>Şube Seçin...</option>
                                    <option>Arama</option>
                                </select>
                                 <input type="text" class="form-control w-auto"  id="phoneSearch"  name="phoneSearch" placeholder="Telefon No içinde ara" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                 <input type="date" class="form-control w-auto"  id="endDate" name="endDate" >

                                <select class="form-select w-auto" id="callStatus" name="callStatus" >
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Arama</option>
                                </select>

                                
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseStatus" name="chooseStatus">
                                    <option disabled selected>Durum Seç</option>
                                    <option>Durum 1</option>
                                </select>

                                <select class="form-select w-auto" id="targetAgent" name="targetAgent">
                                    <option disabled selected>Hedef Agent</option>
                                    <option>Agent 1</option>
                                </select>

                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                        <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'ID',
                  'Aranacak Tarih',
                  'İşlem',
                  'Aranan Tarih',
                  'Şube',
                  'Arayan Agent',
                  'Adı Soyadı',
                  'Numara',
                  'Durum',
                  'Ç.Durumu'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '1', '2023-10-01 10:00', 'Arama', '2023-10-01 10:05', 'Şube A', 'Agent 1', 'Ali Veli', '5551234567',{ type: 'html', html: '<span class="status-badge active">Aktif</span>' }, { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' },
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'waitingMusicTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Planlı Arama Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Genel Ayarlar</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="">
                        <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                        <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label for="uploadType" class="form-label">Yükleme Tipi</label>
                                    <select class="form-select" id="uploadType" name="uploadType">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Tip 1</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <label for="branchGroup" class="form-label">Şube Grubu</label>
                                    <select class="form-select" id="branchGroup" name="branchGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Grup 1</option>
                                    </select>
                                </div>

                                <div class="col-sm-4">
                                    <label for="callDate" class="form-label">Aranacak Tarih</label>
                                    <input type="date" class="form-control w-100" id="callDate" name="callDate">
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerName" class="form-label">Adı</label>
                                    <input type="text" class="form-control" id="customerName" name="customerName">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerSurname" class="form-label">Soyadı</label>
                                    <input type="text" class="form-control" id="customerSurname" name="customerSurname">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="callNumber" class="form-label">Aranacak Numara</label>
                                    <input type="text" class="form-control" id="callNumber" name="callNumber">
                                </div>

                                <div class="col-sm-6">
                                    <label for="callResult" class="form-label">Arama Sonucu</label>
                                    <select class="form-select" id="callResult" name="callResult">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Grup 1</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="callNote" class="form-label">Arama Notu</label>
                                    <textarea id="callNote" class="form-control" name="callNote" style="resize: none !important;" rows="2"></textarea>
                                </div>

                                <div class="col-sm-6">
                                    <label for="callDescription" class="form-label">Açıklama</label>
                                <textarea id="callDescription" class="form-control" name="callDescription" style="resize: none !important;" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                        <button type="button" class="blue-btn">Kaydet</button>
                    </div>
            </div>
        </form>
    </div>
</div>

<div class="modal fade planned-calls-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Tarih Filtreleme Seçeneği</label>
                            <select class="form-select" id="modalDateFilterOption" name="modalDateFilterOption">
                                <option disabled selected>Tarih Filtreleme Seçeneği</option>
                                <option>Aktif</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Çağrı Sonuç Tipi</label>
                            <select class="form-select" id="modalResultType" name="modalResultType">
                                <option disabled selected>Çağrı Sonuç Tipi...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Adı Soyadı içinde ara</label>
                            <input type="text" class="form-control" name="modalNameSearch" id="modalNameSearch" placeholder="Adı Soyadı içinde ara">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="modalStartDate" id="modalStartDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Şube Seçin</label>
                            <select class="form-select" id="modalBranch" name="modalBranch">
                                <option disabled selected>Şube Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Telefon No içinde ara</label>
                            <input type="text" class="form-control" name="modalPhoneSearch" id="modalPhoneSearch" placeholder="Telefon No içinde ara">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="modalEndDate" id="modalEndDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Çağrı Durumu</label>
                            <select class="form-select" id="modalCallStatus" name="modalCallStatus">
                                <option disabled selected>Çağrı Durumu Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Durum</label>
                            <select class="form-select" id="modalStatus" name="modalStatus">
                                <option disabled selected>Durum Seç</option>
                                <option>Durum 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Hedef Agent</label>
                            <select class="form-select" id="modalTargetAgent" name="modalTargetAgent">
                                <option disabled selected>Hedef Agent</option>
                                <option>Agent 1</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#waitingMusicTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        var deleteBtn = document.querySelector('.delete-btn');
        editBtn.disabled = true;
        deleteBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        deleteBtn.classList.add('disabled-btn');

                // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#waitingMusicTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#waitingMusicTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#waitingMusicTable tbody input[type="checkbox"]').length === 
                                 $('#waitingMusicTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#waitingMusicTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();

                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Grup Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Grup Ekle';
            
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#waitingMusicTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        updateButtonStates();
    });
</script>

{% endblock %}