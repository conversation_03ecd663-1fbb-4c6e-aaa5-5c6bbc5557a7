{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/call-manage/call-manage.css') }}">
{% endblock %}

{% block page_content %}

<div class="meet-conf-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Oto Anket</h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/oto-anket">Oto Anket</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Ka<PERSON><PERSON><PERSON>esi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="play-btn me-2">
                           <span class="iconify" data-icon="hugeicons:play" data-inline="false"></span>
                           <span class="text">Başlat</span>
                        </button>
                        <button class="pause-btn me-2">
                          <span class="iconify" data-icon="hugeicons:pause" data-inline="false"></span>
                          <span class="text">Duraksat</span>
                        </button>
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn me-2">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                        <div class="dropdown">
                            <button class="upload-data-btn dropdown-toggle" type="button" id="uploadDataDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="iconify" data-icon="solar:archive-up-minimlistic-linear" data-inline="false"></span>
                                <span class="text">Yükle</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="uploadDataDropdown">
                                <li><a class="dropdown-item" href="#">Excel'den Yükle</a></li>
                                <li><a class="dropdown-item" href="#">Data Listesinden Yükle</a></li>
                                <li><a class="dropdown-item" href="#">Tekil Data Yükle</a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item play-menu-item">
                                <span class="iconify play-btn" data-icon="hugeicons:play" data-inline="false"></span>
                                <span class="text">Başlat</span>
                            </div>
                            <div class="dropdown-menu-item pause-menu-item">
                                <span class="iconify pause-btn" data-icon="hugeicons:pause" data-inline="false"></span>
                                <span class="text">Duraksat</span>
                            </div>
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                            <div class="dropdown-menu-item upload-menu-item position-relative" id="mobileUploadMenuItem">
                                <span class="iconify upload-data-btn" data-icon="solar:archive-up-minimlistic-linear" data-inline="false"></span>
                                <span class="text">Yükle</span>
                                <span class="iconify ms-1" data-icon="mdi:chevron-down" style="font-size: 18px;"></span>
                                <ul class="dropdown-menu mobile-upload-dropdown" style="display: none; position: absolute; left: 0; min-width: fit-content; top: 100%; width: 100%; z-index: 1000;">
                                    <li><a class="dropdown-item" href="#">Excel'den Yükle</a></li>
                                    <li><a class="dropdown-item" href="#">Data Listesinden Yükle</a></li>
                                    <li><a class="dropdown-item" href="#">Tekil Data Yükle</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-100"  id="startDate" name="startDate" >
                                <input type="date" class="form-control w-100"  id="endDate" name="endDate">
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="dateFilterOption" name="dateFilterOption">
                                    <option disabled selected>Tarih Filtreleme Seçeneği</option>
                                    <option>Seçenek 1</option>
                                </select>
                                <select class="form-select w-auto" id="dataStatusSelect" name="dataStatusSelect">
                                    <option disabled selected>Data Durumunu Seçin...</option>
                                    <option>Bekliyor</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="externalLineSelect" name="externalLineSelect">
                                    <option disabled selected>Dış Hat Seçin...</option>
                                    <option>Hat 1</option>
                                </select>

                                <select class="form-select w-auto" id="workStatusSelect" name="workStatusSelect">
                                    <option disabled selected>Çalışma Durumu Seçin...</option>
                                    <option>Bekliyor</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control" id="campaignName" name="campaignName" placeholder="Kampanya Adı İçinde Ara..." >
                                <select class="form-select w-auto"  id="branchSelect" name="branchSelect">
                                    <option disabled selected>Şube Seçin...</option>
                                    <option>Şube 1</option>
                                </select>
                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Oluşturma Tarihi',
                  'Kampanya Adı',
                  'Hedef Tipi',
                  'Dış Hat',
                  'Tamamlanan Data',
                  'Devam Eden Data',
                  'Toplam Data',
                  'Data Durumu',
                  'Ç.Durumu'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '2023-10-01',
                    'Kampanya 1',
                    'Tip 1',
                    '5551234567',
                    '100',
                    '50',
                    '150',
                    { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                    { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' }
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'meetConfTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade auto-call-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Kampanya Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Genel Ayarlar</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link " id="ivr-settings-tab" data-bs-toggle="tab" data-bs-target="#ivr-settings" type="button" role="tab" aria-controls="ivr-settings" aria-selected="true">IVR Ayarları</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link " id="key-settings-tab" data-bs-toggle="tab" data-bs-target="#key-settings" type="button" role="tab" aria-controls="key-settings" aria-selected="true">Tuş Ayarları</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="time-settings-tab" data-bs-toggle="tab" data-bs-target="#time-settings" type="button" role="tab" aria-controls="time-settings" aria-selected="true">Gün ve Saat Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    
                        <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                            <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="doNotDisturb" class="form-label mb-0">Şube</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="modalBranchSelect" name="modalBranchSelect">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Şube 1</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="doNotDisturb" class="form-label mb-0">Kampanya Adı</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="text" class="form-control" id="campaignName" name="campaignName">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="surveyDescription" class="form-label mb-0">Açıklama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <textarea id="surveyDescription" name="surveyDescription" class="form-control" style="resize: none !important;" rows="3"></textarea>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="modalCallStartDate" class="form-label mb-0">Başlangıç Tarihi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="date" class="form-control" id="modalCallStartDate" name="modalCallStartDate">
                                    </div>

                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="modalCallEndDate" class="form-label mb-0">Bitiş Tarihi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <input type="date" class="form-control" id="modalCallEndDate" name="modalCallEndDate">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="externalLine" class="form-label mb-0">Dış Hat</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="externalLine" name="externalLine">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Hat 1</option>
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callAttempt" class="form-label mb-0">Arama Denemesi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callAttempt" name="callAttempt">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Deneme 1</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="targetType" class="form-label mb-0">Hedef Tipi</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="targetType" name="targetType">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Hedef 1</option>
                                        </select>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callAgain" class="form-label mb-0">Tekrar Arama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callAgain" name="callAgain">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Arama 1</option>
                                        </select>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="d-flex align-items-center mb-2">
                                            <label for="callSimultaneously" class="form-label mb-0">Eşzamanlı Arama</label>
                                            <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        </div>
                                        <select class="form-select" id="callSimultaneously" name="callSimultaneously">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Arama 1</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <!-- IVR Tab -->
                        <div class="tab-pane fade" id="ivr-settings" role="tabpanel" aria-labelledby="ivr-settings-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="dialpadWait" class="form-label mb-0">Tuşlama Bekleme</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="dialpadWait" name="dialpadWait">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="dialpadRetry" class="form-label mb-0">Tuşlama Deneme</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="dialpadRetry" name="dialpadRetry">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="welcomeSound" class="form-label mb-0">Hoşgeldiniz Sesi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="welcomeSound" name="welcomeSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="wrongKeySound" class="form-label mb-0">Hatalı Tuşlama Sesi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="wrongKeySound" name="wrongKeySound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="closeSound" class="form-label mb-0">Kapatma Sesi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="closeSound" name="closeSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="transferSound" class="form-label mb-0">Geçiş Sesi</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="transferSound" name="transferSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- Tuş Ayarları Tab -->
                        <div class="tab-pane fade " id="key-settings" role="tabpanel" aria-labelledby="key-settings-tab">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="iconify me-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                        <label for="noKeyAction" class="form-label mb-0">Tuşlama yapılmazsa</label>
                                    </div>
                                    <select class="form-select" id="noKeyAction" name="noKeyAction">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Yönlendir</option>
                                        <option>Beklet</option>
                                        <option>Sonlandır</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row g-2 mb-3">
                                {% for i in 1..9 %}
                                <div class="col-4">
                                    <div class="d-flex align-items-center mb-1 justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <span class="iconify me-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                            <label for="keyAction{{i}}" class="form-label mb-0">Tuş {{i}}</label>
                                        </div>
                                        <div class="btn-group key-toggle-group" data-target="keyAction{{i}}">
                                            <button type="button" class="icon-toggle-btn active" data-type="select">
                                                <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                            </button>
                                            <button type="button" class="icon-toggle-btn" data-type="input">
                                                <span class="iconify" data-icon="ri:input-cursor-move" data-inline="false"></span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="key-toggle-content" data-target="keyAction{{i}}">
                                        <select class="form-select" id="keyAction{{i}}" name="keyAction{{i}}">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Yönlendir</option>
                                            <option>Beklet</option>
                                            <option>Sonlandır</option>
                                        </select>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="row g-2 mb-3">
                                <div class="col-4"></div>
                                <div class="col-4">
                                    <div class="d-flex align-items-center mb-1 justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <span class="iconify me-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                            <label for="keyAction0" class="form-label mb-0">Tuş 0</label>
                                        </div>
                                        <div class="btn-group key-toggle-group" data-target="keyAction0">
                                            <button type="button" class="icon-toggle-btn active" data-type="select">
                                                <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                            </button>
                                            <button type="button" class="icon-toggle-btn" data-type="input">
                                                <span class="iconify" data-icon="ri:input-cursor-move" data-inline="false"></span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="key-toggle-content" data-target="keyAction0">
                                        <select class="form-select" id="keyAction0" name="keyAction0">
                                            <option selected disabled>Seçiniz...</option>
                                            <option>Yönlendir</option>
                                            <option>Beklet</option>
                                            <option>Sonlandır</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4"></div>
                            </div>
                        </div>

                        <!-- Saat Tab -->
                        <div class="tab-pane fade p-0" id="time-settings" role="tabpanel" aria-labelledby="time-settings-tab">
                            <div class="custom-data-table-wrapper">
                                        {% set headers = [
                                        'Gün',
                                        'Durum',
                                        'Arama Başlangıcı',
                                        'Arama Bitişi'
                                        ] %}

                                        {% set rows = [
                                        [
                                            
                                            'Pazartesi',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                        
                                            
                                        ],
                                        [
                                            
                                            'Salı',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        [
                                            
                                            'Çarşamba',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        [
                                            
                                            'Perşembe',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        [
                                            
                                            'Cuma',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        [
                                            
                                            'Cumartesi',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        [
                                            
                                            'Pazar',
                                            { type: 'checkbox', class: 'row-check' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="09:00:00" step="1">' },
                                            { type: 'html', html: '<input type="time" class="form-control time-input" value="18:00:00" step="1">' }
                                            
                                        ],
                                        ] %}

                                        {% include 'components/table.twig' with {
                                            id: 'timeSettingsTable',
                                            headers: headers,
                                            rows: rows,
                                            class: 'time-settings-table'
                                        } %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="modal fade auto-call-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate" name="modalStartDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate" name="modalEndDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Tarih Filtreleme Seçeneği</label>
                            <select class="form-select" id="modalDateFilter" name="modalDateFilter">
                                <option disabled selected>Tarih Filtreleme Seçin...</option>
                                <option>Seçenek 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Data Durumu</label>
                            <select class="form-select" id="modalDataStatus" name="modalDataStatus">
                                <option disabled selected>Data Durumunu Seçin...</option>
                                <option>Durum 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Dış Hat</label>
                            <select class="form-select" id="modalExternalLine" name="modalExternalLine">
                                <option disabled selected>Dış Hat Seçin...</option>
                                <option>Hat 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Çalışma Durumu</label>
                            <select class="form-select" id="modalWorkStatus" name="modalWorkStatus">
                                <option disabled selected>Çalışma Durumunu Seçin...</option>
                                <option>Durum 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Kampanya Adı</label>
                            <input type="text" class="form-control" id="modalCampaignName" name="modalCampaignName" placeholder="Kampanya Adı İçinde Ara...">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Şube</label>
                            <select class="form-select" id="modalBranch" name="modalBranch">
                                <option disabled selected>Şube Seçin...</option>
                                <option>Şube 1</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#meetConfTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        var deleteBtn = document.querySelector('.delete-btn');
        editBtn.disabled = true;
        deleteBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        deleteBtn.classList.add('disabled-btn');

                // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#meetConfTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#meetConfTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#meetConfTable tbody input[type="checkbox"]').length === 
                                 $('#meetConfTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#meetConfTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();

                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Kampanya Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Kampanya Ekle';
            
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#meetConfTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        updateButtonStates();
    });
</script>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#timeSettingsTable').DataTable({
            searching: false,
            paging: false,
            info: false,
            lengthChange: false,
            bLengthChange: false,
            dom: 't',
            order: [],
            ordering: true,
            autoWidth: false,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 1 } // 2. sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşıma işlemi gereksiz oldu
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tuş ayarları butonları için toggle
    for (let i = 0; i <= 9; i++) {
        const selectBtn = document.getElementById(`tus${i}-select-btn`);
        const inputBtn = document.getElementById(`tus${i}-input-btn`);
        const valueArea = document.querySelector(`.tus${i}-value-area`);
        if (!selectBtn || !inputBtn || !valueArea) continue;
        selectBtn.addEventListener('click', function() {
            selectBtn.classList.add('active');
            inputBtn.classList.remove('active');
            valueArea.innerHTML = `<select class='form-select'><option selected disabled>Seçiniz...</option><option>Yönlendir</option><option>Beklet</option><option>Sonlandır</option></select>`;
        });
        inputBtn.addEventListener('click', function() {
            inputBtn.classList.add('active');
            selectBtn.classList.remove('active');
            valueArea.innerHTML = `<input type='text' class='form-control' placeholder='Aksiyon girin...'>`;
        });
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tuş değerlerini saklamak için obje
    var keyValues = {};
    // Her tuş için hem select hem input değişikliğini dinle
    document.querySelectorAll('.key-toggle-content').forEach(function(content) {
        var target = content.getAttribute('data-target');
        keyValues[target] = { select: '', input: '' };
        // Select değişikliği
        var select = content.querySelector('select');
        if (select) {
            select.addEventListener('change', function() {
                keyValues[target].select = this.value;
            });
        }
        // Input değişikliği
        var input = content.querySelector('input');
        if (input) {
            input.addEventListener('input', function() {
                keyValues[target].input = this.value;
            });
        }
    });
    function handleToggleButton(groupClass, contentClass) {
        document.querySelectorAll('.' + groupClass).forEach(function(group) {
            var target = group.getAttribute('data-target');
            var content = document.querySelector('.' + contentClass + '[data-target="' + target + '"]');
            if (!content) return;
            group.querySelectorAll('.icon-toggle-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    group.querySelectorAll('.icon-toggle-btn').forEach(function(b) { b.classList.remove('active'); });
                    btn.classList.add('active');
                    if (btn.getAttribute('data-type') === 'select') {
                        var select = content.querySelector('select');
                        var input = content.querySelector('input');
                        if (!select) {
                            var newSelect = document.createElement('select');
                            newSelect.className = 'form-select';
                            newSelect.id = target;
                            newSelect.innerHTML = '<option selected disabled>Seçiniz...</option><option>Yönlendir</option><option>Beklet</option><option>Sonlandır</option>';
                            if (keyValues[target] && keyValues[target].select) {
                                newSelect.value = keyValues[target].select;
                            }
                            newSelect.addEventListener('change', function() {
                                keyValues[target].select = this.value;
                            });
                            if (input) input.replaceWith(newSelect);
                        } else {
                            select.style.display = '';
                            if (input) input.style.display = 'none';
                            if (keyValues[target] && keyValues[target].select) {
                                select.value = keyValues[target].select;
                            }
                        }
                    } else {
                        var select = content.querySelector('select');
                        var input = content.querySelector('input');
                        if (!input) {
                            var newInput = document.createElement('input');
                            newInput.type = 'text';
                            newInput.className = 'form-control';
                            newInput.placeholder = 'Değer girin...';
                            newInput.id = target + '_input';
                            if (keyValues[target] && keyValues[target].input) {
                                newInput.value = keyValues[target].input;
                            }
                            newInput.addEventListener('input', function() {
                                keyValues[target].input = this.value;
                            });
                            if (select) select.replaceWith(newInput);
                        } else {
                            input.style.display = '';
                            if (select) select.style.display = 'none';
                            if (keyValues[target] && keyValues[target].input) {
                                input.value = keyValues[target].input;
                            }
                        }
                    }
                });
            });
        });
    }
    handleToggleButton('key-toggle-group', 'key-toggle-content');
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Upload Dropdown
    var mobileUploadMenuItem = document.getElementById('mobileUploadMenuItem');
    if (mobileUploadMenuItem) {
        var mobileDropdown = mobileUploadMenuItem.querySelector('.mobile-upload-dropdown');
        mobileUploadMenuItem.addEventListener('click', function(e) {
            e.stopPropagation();
            if (mobileDropdown.style.display === 'block') {
                mobileDropdown.style.display = 'none';
            } else {
                // Kapatılacak başka açık menü varsa kapat
                document.querySelectorAll('.mobile-upload-dropdown').forEach(function(el) {
                    el.style.display = 'none';
                });
                mobileDropdown.style.display = 'block';
            }
        });
        // Sayfanın başka yerine tıklanınca menüyü kapat
        document.addEventListener('click', function() {
            mobileDropdown.style.display = 'none';
        });
    }
});
</script>

{% endblock %}