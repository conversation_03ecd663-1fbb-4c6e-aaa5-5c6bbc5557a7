{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/call-manage/call-manage.css') }}">
{% endblock %}


{% block page_content %}

<section class="call-result-types-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Planlı Çağrı Sonuç Tipleri</h4>
        <div class="d-flex align-items-center route-links">
            <a>Sistem Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/cagri-sonuc-tipleri"> Çağrı Sonuç Tipleri</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Planlı Çağrı Sonuç Tipleri</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                       
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Adı',
                  'Açıklama',
                  'Ekleme Tarihi',
                  'Kayıtlı Sonuç'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '0532 123 45 67',
                        'Açıklama 1',
                        '2023-10-01',
                        'Sonuç 1'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'callResultTypesTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade ivr-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Sonuç Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="typeName" class="form-label">Tip Adı</label>
                                    <input type="text" class="form-control" id="typeName" name="typeName" placeholder="Tip Adı">
                                </div>
                                <div class="col-sm-6">
                                    <label for="typeDescription" class="form-label">Tip Açıklaması</label>
                                    <input type="text" class="form-control" id="typeDescription" name="typeDescription" placeholder="Tip Açıklaması">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#callResultTypesTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit ve delete butonlarını başlangıçta disabled yap
        window.editBtn = document.querySelector('.edit-btn');
        window.deleteBtn = document.querySelector('.delete-btn');
        
        window.editBtn.disabled = true;
        window.deleteBtn.disabled = true;
        
        window.editBtn.classList.add('disabled-btn');
        window.deleteBtn.classList.add('disabled-btn');
        
        // İlk sayfa yüklendiğinde buton durumlarını kontrol et
        updateButtonStates();
        
        // Mobil menü öğelerinin durumunu güncelle
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#callResultTypesTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                window.editBtn.disabled = false;
                window.editBtn.classList.remove('disabled-btn');
            } else {
                window.editBtn.disabled = true;
                window.editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                window.deleteBtn.disabled = false;
                window.deleteBtn.classList.remove('disabled-btn');
            } else {
                window.deleteBtn.disabled = true;
                window.deleteBtn.classList.add('disabled-btn');
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#callResultTypesTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#callResultTypesTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#callResultTypesTable tbody input[type="checkbox"]').length === 
                                 $('#callResultTypesTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        window.editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Sonuç Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Dahili Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Sonuç Ekle';
            
            // Form alanlarını temizle
            const formInputs = document.querySelectorAll('#addModal input, #addModal select');
            formInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
        
        // Delete butonuna tıklandığında
        window.deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#callResultTypesTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Not: Silme işlemi sonradan eklenecek
                }
            }
        });
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>

{% endblock %}