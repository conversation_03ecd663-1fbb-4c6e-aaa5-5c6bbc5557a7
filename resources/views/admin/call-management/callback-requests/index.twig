{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/call-manage/call-manage.css') }}">
{% endblock %}


{% block page_content %}

<section class="callback-requests-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">G<PERSON></h4>
        <div class="d-flex align-items-center route-links">
            <a>Çağrı Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/geri-aranma-talepleri"> Geri Aranma <PERSON></a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-100"  id="startDate" name="startDate" >
                                <input type="date" class="form-control w-100"  id="endDate" name="endDate" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control" id="callerNumber" name="callerNumber" placeholder="Arayan Numara..." >
                                <input type="text" class="form-control" id="calledNumber" name="calledNumber" placeholder="Aranan Numara..." >
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select" id="returningAgentSelect" name="returningAgentSelect">
                                    <option disabled selected>Geri Arayan Agent...</option>
                                    <option>Agent 1</option>
                                </select>
                                <select class="form-select" id="queueSelect" name="queueSelect">
                                    <option disabled selected>Kuyruk Seçin...</option>
                                    <option>Kuyruk 1</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select" id="callStatusSelect" name="callStatusSelect">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Aktif</option>
                                    <option>Pasif</option>
                                </select>
                                <div class="d-flex">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Tarih',
                  'Kuyruk Adı',
                  'Arayan No',
                  'Aranan No',
                  'Bekleme Süresi',
                  'Durum',
                  'Sonuç',
                  'Geri Arayan Agent'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '2023-10-01 12:30',
                        'Kuyruk 1',
                        '555-1234',
                        '444-5678',
                        '00:05:30',
                        { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                        'Cevapsız Çağrı',
                        'Agent 1'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'callbackRequestsTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade missed-calls-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalStartDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalEndDate" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCallerNumber" class="form-label">Arayan Numara</label>
                            <input type="text" class="form-control" id="modalCallerNumber" placeholder="Arayan Numara...">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCalleeNumber" class="form-label">Aranan Numara</label>
                            <input type="text" class="form-control" id="modalCalleeNumber" placeholder="Aranan Numara...">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCallType" class="form-label">Geri Arayan Agent</label>
                            <select class="form-select" id="modalCallType">
                                <option disabled selected>Geri Arayan Agent...</option>
                                <option>Agent 1</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalQueueSelect" class="form-label">Kuyruk</label>
                            <select class="form-select" id="modalQueueSelect">
                                <option disabled selected>Kuyruk Seçin</option>
                                <option>Kuyruk 1</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCallStatus" class="form-label">Çağrı Durumu</label>
                            <select class="form-select" id="modalCallStatus">
                                <option disabled selected>Çağrı Durumu Seçin</option>
                                <option>Aktif</option>
                                <option>Pasif</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#callbackRequestsTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#callbackRequestsTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#callbackRequestsTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#callbackRequestsTable tbody input[type="checkbox"]').length === 
                                 $('#callbackRequestsTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>

{% endblock %}