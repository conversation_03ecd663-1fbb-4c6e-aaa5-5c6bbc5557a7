{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/ivr/ivr.css') }}">
{% endblock %}


{% block page_content %}

<section class="ivr-list-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">IVR Listesi</h4>
        <div class="d-flex align-items-center route-links">
            <a>IVR Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/ivr-listesi">IVR Listesi</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">IVR Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                       
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Dahili No ile ara...">
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'ID',
                  'Açıklama',
                  'Karşılama Sesi',
                  'Hatalı Tuşlama Sesi',
                  'Kapatma Sesi',
                  'Geçiş Sesi'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '1',
                        'Yönetim Dahili',
                        'Karşılama Sesi 1',
                        'Hatalı Tuşlama Sesi 1',
                        'Kapatma Sesi 1',
                        'Geçiş Sesi 1'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'ivrListTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade ivr-modal" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Dahili Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="permissions-tab" data-bs-toggle="tab" data-bs-target="#permissions" type="button" role="tab" aria-controls="permissions" aria-selected="false">Tuşlama Ayarları</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status-content" type="button" role="tab" aria-controls="status-content" aria-selected="false">Çağrı Puanlama Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="ivrDescription" class="form-label">IVR Açıklaması</label>
                                    <input type="text" class="form-control" id="ivrDescription">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="dialTimeout" class="form-label">Tuşlama Bekleme Süresi</label>
                                    <select class="form-select" id="dialTimeout" name="dialTimeout">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="5">5 saniye</option>
                                        <option value="10">10 saniye</option>
                                        <option value="15">15 saniye</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="dialAttempts" class="form-label">Tuşlama Deneme Miktarı</label>
                                    <select class="form-select" id="dialAttempts" name="dialAttempts">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="1">1 deneme</option>
                                        <option value="2">2 deneme</option>
                                        <option value="3">3 deneme</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-2 custom-checkbox d-flex align-items-center">
                                <input type="checkbox" id="allowInternalDial">
                                <label class="custom-check me-2" for="allowInternalDial">
                                    <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                </label>
                                <label class="form-check-label" for="allowInternalDial">
                                    Dahili No Tuşlamaya İzin Ver
                                </label>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="welcomeSound" class="form-label">Hoşgeldiniz Sesi</label>
                                    <select class="form-select" id="welcomeSound" name="welcomeSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Ses 1</option>
                                        <option>Ses 2</option>
                                        <option>Ses 3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="wrongKeySound" class="form-label">Hatalı Tuşlama Sesi</label>
                                    <select class="form-select" id="wrongKeySound" name="wrongKeySound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Ses 1</option>
                                        <option>Ses 2</option>
                                        <option>Ses 3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="closeSound" class="form-label">Kapatma Sesi</label>
                                    <select class="form-select" id="closeSound" name="closeSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Ses 1</option>
                                        <option>Ses 2</option>
                                        <option>Ses 3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="transitionSound" class="form-label">Tuşlama Yapılırsa Geçiş Sesi</label>
                                    <select class="form-select" id="transitionSound"  name="transitionSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Ses 1</option>
                                        <option>Ses 2</option>
                                        <option>Ses 3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="nonTransitionSound" class="form-label">Tuşlama Yapılmazsa Geçiş Sesi</label>
                                <select class="form-select" id="nonTransitionSound" name="nonTransitionSound">
                                    <option selected disabled>Seçiniz...</option>
                                    <option>Ses 1</option>
                                    <option>Ses 2</option>
                                    <option>Ses 3</option>
                                </select>
                            </div>
                        </div>

                        <!-- Tuşlama Ayarları Tab -->
                        <div class="tab-pane fade" id="permissions" role="tabpanel" aria-labelledby="permissions-tab">
                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="noKeyPress" class="form-label">Tuşlama Yapılmazsa</label>
                                    <select class="form-select" id="noKeyPress" name="noKeyPress">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="useTransitionSound">
                                        <label class="custom-check me-2" for="useTransitionSound">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="useTransitionSound">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress1" class="form-label">Tuş 1</label>
                                    <select class="form-select" id="keyPress1" name="keyPress1">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key1">
                                        <label class="custom-check me-2" for="key1">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key1">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress2" class="form-label">Tuş 2</label>
                                    <select class="form-select" id="keyPress2" name="keyPress2">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key2">
                                        <label class="custom-check me-2" for="key2">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key2">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress3" class="form-label">Tuş 3</label>
                                    <select class="form-select" id="keyPress3" name="keyPress3">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key3">
                                        <label class="custom-check me-2" for="key3">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key3">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress4" class="form-label">Tuş 4</label>
                                    <select class="form-select" id="keyPress4" name="keyPress4">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key4">
                                        <label class="custom-check me-2" for="key4">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key4">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress5" class="form-label">Tuş 5</label>
                                    <select class="form-select" id="keyPress5" name="keyPress5">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key5">
                                        <label class="custom-check me-2" for="key5">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key5">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress6" class="form-label">Tuş 6</label>
                                    <select class="form-select" id="keyPress6" name="keyPress6">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key6">
                                        <label class="custom-check me-2" for="key6">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key6">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress7" class="form-label">Tuş 7</label>
                                    <select class="form-select" id="keyPress7" name="keyPress7">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key7">
                                        <label class="custom-check me-2" for="key7">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key7">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress8" class="form-label">Tuş 8</label>
                                    <select class="form-select" id="keyPress8" name="keyPress8">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key8">
                                        <label class="custom-check me-2" for="key8">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key8">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress9" class="form-label">Tuş 9</label>
                                    <select class="form-select" id="keyPress9" name="keyPress9">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key9">
                                        <label class="custom-check me-2" for="key9">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key9">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-2 d-flex align-items-end gap-2">
                                <div class="col-lg-8 col-12">
                                    <label for="keyPress0" class="form-label">Tuş 0</label>
                                    <select class="form-select" id="keyPress0" name="keyPress0">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <div class="custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="key0">
                                        <label class="custom-check me-2" for="key0">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="key0">
                                            Geçiş Sesi kullanılsın mı?
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Durum Yönlendirmeleri Tab -->
                        <div class="tab-pane fade" id="status-content" role="tabpanel" aria-labelledby="status-tab">
                            <div class="mb-2">
                                <div class="custom-checkbox d-flex align-items-center">
                                    <input type="checkbox" id="activeStatus">
                                    <label class="custom-check me-2" for="activeStatus">
                                        <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                    </label>
                                    <label class="form-check-label" for="activeStatus">
                                        Aktif?
                                    </label>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="descriptionSound" class="form-label">Açıklama Sesi</label>
                                    <select class="form-select" id="descriptionSound" name="descriptionSound">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Ses 1</option>
                                        <option>Ses 2</option>
                                        <option>Ses 3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="scoreCampaign" class="form-label">Puanlama Kampanyası</label>
                                    <select class="form-select" id="scoreCampaign" name="scoreCampaign">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Kampanya 1</option>
                                        <option>Kampanya 2</option>
                                        <option>Kampanya 3</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#ivrListTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit ve delete butonlarını başlangıçta disabled yap
        window.editBtn = document.querySelector('.edit-btn');
        window.deleteBtn = document.querySelector('.delete-btn');
        
        window.editBtn.disabled = true;
        window.deleteBtn.disabled = true;
        
        window.editBtn.classList.add('disabled-btn');
        window.deleteBtn.classList.add('disabled-btn');
        
        // İlk sayfa yüklendiğinde buton durumlarını kontrol et
        updateButtonStates();
        
        // Mobil menü öğelerinin durumunu güncelle
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#ivrListTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                window.editBtn.disabled = false;
                window.editBtn.classList.remove('disabled-btn');
            } else {
                window.editBtn.disabled = true;
                window.editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                window.deleteBtn.disabled = false;
                window.deleteBtn.classList.remove('disabled-btn');
            } else {
                window.deleteBtn.disabled = true;
                window.deleteBtn.classList.add('disabled-btn');
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#ivrListTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#ivrListTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#ivrListTable tbody input[type="checkbox"]').length === 
                                 $('#ivrListTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        window.editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'IVR Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Dahili Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'IVR Ekle';
            
            // Form alanlarını temizle
            const formInputs = document.querySelectorAll('#addModal input, #addModal select');
            formInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
        
        // Delete butonuna tıklandığında
        window.deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#ivrListTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Not: Silme işlemi sonradan eklenecek
                }
            }
        });
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>

{% endblock %}