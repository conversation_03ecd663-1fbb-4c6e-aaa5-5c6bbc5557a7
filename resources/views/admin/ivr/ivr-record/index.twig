{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/ivr/ivr.css') }}">

{% endblock %}

{% block page_content %}

<div class="ivr-record-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">IVR Ses Kayıtları</h4>
        <div class="d-flex align-items-center route-links">
            <a>IVR Ayarları</a>
            <span class="mx-2">></span>
            <a href="/admin/ivr-ses-kayitlari">IVR Ses Kayıtları</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">IVR Se<PERSON></h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'IVR ID',
                  'Açıklama',
                  'Süre',
                  'Dinle'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '41', 'Lorem Ipsum', '2:42', { type: 'html', html: '<div class="audio-player-container"><button class="play-button" data-audio-id="41"><span class="iconify" data-icon="hugeicons:play-circle" data-inline="false"></span></button><div class="audio-progress-container"><div class="audio-progress-bar" data-audio-id="41"></div></div></div>' }
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '41', 'Lorem Ipsum', '4:20', { type: 'html', html: '<div class="audio-player-container"><button class="play-button" data-audio-id="42"><span class="iconify" data-icon="hugeicons:play-circle" data-inline="false"></span></button><div class="audio-progress-container"><div class="audio-progress-bar" data-audio-id="42"></div></div></div>' }
                  ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'ivrRecordingTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Grup Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Grup Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="">
                        <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                        <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                            <div class="row">
                                <div class="col-12 mb-2">
                                    <label for="voiceRecordingDescription" class="form-label">Ses Kaydı Açıklaması</label>
                                    <input type="text" class="form-control" id="voiceRecordingDescription" name="voiceRecordingDescription">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <label for="anonsFile" class="form-label">Ses Dosyası</label>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="anonsFile" name="anonsFile" accept="audio/*">
                                    </div>
                                    
                                    <div id="audioPreviewContainer" class="mt-3 w-100" style="display: none;">
                                        <div class="d-flex align-items-center">
                                            <audio id="audioPreview" controls style="width: 100%;"></audio>
                                            <button type="button" id="removeAudio" class="delete-btn ms-2">
                                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>



<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#ivrRecordingTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0, width: '5%' }, // İlk sütunda sıralama kapalı 
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var deleteBtn = document.querySelector('.delete-btn');
        deleteBtn.disabled = true;
        deleteBtn.classList.add('disabled-btn');

        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#ivrRecordingTable tbody input[type="checkbox"]:checked');
            
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#ivrRecordingTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#ivrRecordingTable tbody input[type="checkbox"]').length === 
                                 $('#ivrRecordingTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'IVR Ses Kaydı Ekle';
            
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#ivrRecordingTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        // Audio player functionality
        let currentAudio = null;
        let currentButton = null;
        let progressInterval = null;

        // Event delegation for play buttons
        document.querySelector('#ivrRecordingTable').addEventListener('click', function(e) {
            const playButton = e.target.closest('.play-button');
            if (!playButton) return;
            
            const audioId = playButton.getAttribute('data-audio-id');
            
            // If we have a currently playing audio, stop it
            if (currentAudio) {
                currentAudio.pause();
                currentAudio.currentTime = 0;
                
                if (currentButton) {
                    currentButton.classList.remove('playing');
                    const icon = currentButton.querySelector('.iconify');
                    if (icon) {
                        icon.setAttribute('data-icon', 'hugeicons:play-circle');
                    }
                }
                
                // Reset progress bar
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                
                // Reset all progress bars
                document.querySelectorAll('.audio-progress-bar').forEach(bar => {
                    bar.style.width = '0%';
                });
                
                // If the same button was clicked, just stop and return
                if (currentButton === playButton) {
                    currentAudio = null;
                    currentButton = null;
                    return;
                }
            }
            
            // Create new audio (in a real application, you would use the actual audio URL)
            const audioUrl = `/api/audio/${audioId}`;  // Example URL, replace with your actual API endpoint
            
            const audio = new Audio(audioUrl);
            
            audio.addEventListener('ended', function() {
                playButton.classList.remove('playing');
                const icon = playButton.querySelector('.iconify');
                if (icon) {
                    icon.setAttribute('data-icon', 'hugeicons:play-circle');
                }
                
                // Reset progress bar when audio ends
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                
                // Find and reset the specific progress bar
                const progressBar = document.querySelector(`.audio-progress-bar[data-audio-id="${audioId}"]`);
                if (progressBar) {
                    progressBar.style.width = '0%';
                }
                
                currentAudio = null;
                currentButton = null;
            });
            
            // Play the audio
            audio.play().catch(error => {
                console.error('Error playing audio:', error);
                // Here you could show an error message to the user
            });
            
            // Set up progress tracking
            const progressBar = document.querySelector(`.audio-progress-bar[data-audio-id="${audioId}"]`);
            if (progressBar) {
                // Clear any existing interval
                if (progressInterval) {
                    clearInterval(progressInterval);
                }
                
                // Update progress every 100ms
                progressInterval = setInterval(() => {
                    if (audio.duration) {
                        const progress = (audio.currentTime / audio.duration) * 100;
                        progressBar.style.width = `${progress}%`;
                    }
                }, 100);
            }
            
            // Update the button appearance
            playButton.classList.add('playing');
            const icon = playButton.querySelector('.iconify');
            if (icon) {
                icon.setAttribute('data-icon', 'hugeicons:pause-circle');
            }
            
            // Update current references
            currentAudio = audio;
            currentButton = playButton;
        });


        // Ses dosyası yükleme işlemleri
        const anonsFileInput = document.getElementById('anonsFile');
        const audioPreview = document.getElementById('audioPreview');
        const audioPreviewContainer = document.getElementById('audioPreviewContainer');
        const removeAudioBtn = document.getElementById('removeAudio');
        
        // Desteklenen formatlar ve maksimum boyut (10MB)
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        const SUPPORTED_FORMATS = ['audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg'];
        
        anonsFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            
            if (!file) {
                audioPreviewContainer.style.display = 'none';
                audioPreview.src = '';
                return;
            }
            
            // Dosya formatı kontrolü
            if (!SUPPORTED_FORMATS.includes(file.type) && !file.type.startsWith('audio/')) {
                alert('Lütfen desteklenen bir ses dosyası formatı seçin (MP3, WAV, OGG)');
                anonsFileInput.value = '';
                return;
            }
            
            // Dosya boyutu kontrolü
            if (file.size > MAX_FILE_SIZE) {
                alert('Dosya boyutu 10MB\'dan küçük olmalıdır');
                anonsFileInput.value = '';
                return;
            }
            
            // Geçerli bir ses dosyası, önizleme yap
            const objectURL = URL.createObjectURL(file);
            audioPreview.src = objectURL;
            audioPreviewContainer.style.display = 'block';
            
            // Ses dosyasının süresini al (metaveri yüklendikten sonra)
            audioPreview.onloadedmetadata = function() {
                const duration = audioPreview.duration;
                // Süre bilgisini başka bir yerde kullanabilirsiniz
                console.log(`Ses süresi: ${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}`);
            };
        });
        
        // Ses dosyasını kaldır butonu
        removeAudioBtn.addEventListener('click', function() {
            anonsFileInput.value = '';
            audioPreview.src = '';
            audioPreviewContainer.style.display = 'none';
        });
        
        updateButtonStates();
    });
</script>

{% endblock %}