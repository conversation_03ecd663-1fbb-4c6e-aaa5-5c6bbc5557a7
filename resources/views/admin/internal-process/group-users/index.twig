{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/internal/internal.css') }}">
{% endblock %}

{% block page_content %}

<section class="group-users-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1"><PERSON><PERSON><PERSON></h4>
        <div class="d-flex align-items-center route-links">
            <a><PERSON>hili <PERSON>lemleri</a>
            <span class="mx-2">></span>
            <a href="/admin/grup-kullanicilari"><PERSON><PERSON><PERSON></a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
        <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON><PERSON> Kullanıcı Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Kullanıcı Adı',
                  'Parola',
                  'Kullanıcı Açıklaması',
                  'E-Posta',
                  'Durum'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'User1', '1234', 'User1 Açıklama', '<EMAIL>', { type: 'html', html: '<span class="status-badge active">Aktif</span>' }
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'User2', '5678', 'User2 Açıklama', '<EMAIL>', { type: 'html', html: '<span class="status-badge deactive">Deaktif</span>' }
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'User3', '9101', 'User3 Açıklama', '<EMAIL>', { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' }
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'groupUsersTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>

<!-- Grup Kullanıcısı Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Grup Kullanıcısı Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="userTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="user-settings-tab" data-bs-toggle="tab" data-bs-target="#user-settings" type="button" role="tab" aria-controls="user-settings" aria-selected="true">Grup Kullanıcı Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="">
                        <div class="tab-content" id="userTabsContent">
                        <!-- Grup Kullanıcı Ayarları Tab -->
                        <div class="tab-pane fade show active" id="user-settings" role="tabpanel" aria-labelledby="user-settings-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="userName" class="form-label">Kullanıcı Adı</label>
                                    <input type="text" class="form-control" id="userName" name="userName">
                                </div>
                                <div class="col-md-6">
                                    <label for="userDescription" class="form-label">Açıklama</label>
                                    <input type="text" class="form-control" id="userDescription" name="userDescription">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="userEmail" class="form-label">E-posta</label>
                                    <input type="email" class="form-control" id="userEmail" name="userEmail">
                                </div>
                                <div class="col-md-6">
                                    <label for="userPassword" class="form-label">Parola</label>
                                    <input type="password" class="form-control" id="userPassword" name="userPassword" placeholder="Parola">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="userStatus" class="form-label">Durum</label>
                                    <select class="form-select" id="userStatus" name="userStatus">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="active">Aktif</option>
                                        <option value="deactive">Deaktif</option>
                                        <option value="waiting">Bekliyor</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="authorizedGroups" class="form-label">Yetkilendirilen Gruplar</label>
                                    <div class="mt-2">
                                        <div class="mb-2 custom-checkbox d-flex align-items-center">
                                            <input type="checkbox" id="searchGroup">
                                            <label class="custom-check me-2" for="searchGroup">
                                                <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                            </label>
                                            <label class="form-check-label" for="searchGroup">
                                                Arama
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#groupUsersTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        
        
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        editBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        
        // Delete butonu başlangıçta disabled yap
        var deleteBtn = document.querySelector('.delete-btn');
        deleteBtn.disabled = true;
        deleteBtn.classList.add('disabled-btn');
        
        // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#groupUsersTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }
            
            // Mobil menü öğelerinin durumunu güncelle
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#groupUsersTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#groupUsersTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#groupUsersTable tbody input[type="checkbox"]').length === 
                                 $('#groupUsersTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#groupUsersTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();
                
                // Form alanlarını doldur
                // [id, değer, sadece okunabilir mi]
                const fieldValues = [
                    ['userName', rowData[1], true], // Kullanıcı Adı - sadece okunabilir
                    ['userDescription', rowData[3], false], // Açıklama - düzenlenebilir
                    ['userEmail', rowData[4], true], // E-posta - sadece okunabilir
                    ['userPassword', rowData[2], true] // Parola - sadece okunabilir
                ];
                
                // Tüm alanları doldur ve okunabilir/düzenlenebilir yap
                fieldValues.forEach(([id, value, isReadOnly]) => {
                    const element = document.getElementById(id);
                    element.value = value;
                    
                    if (isReadOnly) {
                        element.readOnly = true;
                        element.classList.add('bg-light');
                    } else {
                        element.readOnly = false;
                        element.classList.remove('bg-light');
                    }
                });
                
                // Durum seçimini ayarla (düzenlenebilir olacak)
                var statusText = rowData[5].replace(/<[^>]*>/g, '').trim(); // HTML'i temizle
                var statusSelect = document.getElementById('userStatus');
                
                // Durum dropdown'ını aktif yap
                statusSelect.disabled = false;
                statusSelect.classList.remove('bg-light');
                
                // Uygun değeri seç
                for (var i = 0; i < statusSelect.options.length; i++) {
                    if (statusSelect.options[i].text === statusText) {
                        statusSelect.selectedIndex = i;
                        break;
                    }
                }
                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Grup Kullanıcısı Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Kullanıcısı Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Grup Kullanıcısı Ekle';
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
        
        // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#groupUsersTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });
        
        // Sayfa yüklendiğinde buton durumlarını kontrol et
        updateButtonStates();
    });
</script>

{% endblock %}