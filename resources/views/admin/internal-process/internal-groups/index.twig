{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/internal/internal.css') }}">
{% endblock %}

{% block page_content %}

<section class="internal-group-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1"><PERSON><PERSON><PERSON></h4>
        <div class="d-flex align-items-center route-links">
            <a>Dahili <PERSON>lemler<PERSON></a>
            <span class="mx-2">></span>
            <a href="/admin/dahili-gruplari"><PERSON><PERSON><PERSON></a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON><PERSON><PERSON></h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="edit-btn">
                           <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                           <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Arama Yap...">                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Grup Adı',
                  'Grup Açıklama',
                  'Ekleme Tarihi',
                  'Kayıtlı Dahili'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Mavi Takım', 'Ara', '2023-11-15 14:22:10', '2'
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Kırmızı Grup', 'Sorgula', '2024-01-05 08:45:30', '2'
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Yeşil Ekip', 'Bul', '2026-03-12 17:55:45', '2'
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Sarı Çete', 'İncele', '2026-03-12 17:55:45', '2'
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Turuncu Birlik', 'Gözden geçir', '2026-03-12 17:55:45', '2'
                  ],
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Mor Kadro', 'Araştır', '2026-03-12 17:55:45', '2'
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'internalGroupTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<!-- Grup Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Grup Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="group-settings-tab" data-bs-toggle="tab" data-bs-target="#group-settings" type="button" role="tab" aria-controls="group-settings" aria-selected="true">Grup Ayarları</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="">
                        <div class="tab-content" id="editTabsContent">
                        <!-- Grup Ayarları Tab -->
                        <div class="tab-pane fade show active" id="group-settings" role="tabpanel" aria-labelledby="group-settings-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="groupName" class="form-label">Grup Adı</label>
                                    <input type="text" class="form-control" id="groupName" name="groupName">
                                </div>
                                <div class="col-md-6">
                                    <label for="groupDescription" class="form-label">Grup Açıklaması</label>
                                    <input type="text" class="form-control" id="groupDescription" name="groupDescription">
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>




<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#internalGroupTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        var deleteBtn = document.querySelector('.delete-btn');
        editBtn.disabled = true;
        deleteBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        deleteBtn.classList.add('disabled-btn');

                // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#internalGroupTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#internalGroupTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#internalGroupTable tbody input[type="checkbox"]').length === 
                                 $('#internalGroupTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#internalGroupTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();
                
                // Form alanlarını doldur
                // Basit değerleri ayarla - [id, değer, sadece okunabilir mi]
                const fieldValues = [
                    ['groupName', rowData[1], false], // Grup Adı - düzenlenebilir
                    ['groupDescription', rowData[2], false], // Grup Açıklaması - düzenlenebilir
                ];
                
                // Tüm alanları doldur
                fieldValues.forEach(([id, value, isReadOnly]) => {
                    const element = document.getElementById(id);
                    element.value = value;
                    
                    if (isReadOnly) {
                        element.readOnly = true;
                        element.classList.add('bg-light');
                    }
                });
                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Grup Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Grup Ekle';
            
            // Form alanlarını temizle
            document.getElementById('groupName').value = '';
            document.getElementById('groupDescription').value = '';
            
            // Tüm form alanlarını düzenlenebilir yap
            document.getElementById('groupName').readOnly = false;
            document.getElementById('groupName').classList.remove('bg-light');
            document.getElementById('groupDescription').readOnly = false;
            document.getElementById('groupDescription').classList.remove('bg-light');
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#internalGroupTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        updateButtonStates();
    });
</script>


{% endblock %}