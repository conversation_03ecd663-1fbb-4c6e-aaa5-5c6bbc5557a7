{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/agent-report/agent-report.css') }}">
{% endblock %}

{% block page_content %}

<div class="agent-status-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Agent <PERSON></h4>
        <div class="d-flex align-items-center route-links">
            <a>Agent <PERSON>or<PERSON></a>
            <span class="mx-2">></span>
            <a href="/admin/agent-durumlari">Agent <PERSON></a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Agent Durum <PERSON>esi</h4>
                <div class="d-flex align-items-center">
                     <div class="action-buttons d-flex">
                        <button class="online-listen-btn">
                            <span class="iconify" style="font-size:20px;" data-icon="solar:headphones-square-linear" data-inline="false"></span>
                            <span class="text ms-2">Online Dinleme Yap</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                           <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify online-listen-btn" data-icon="solar:headphones-square-linear" data-inline="false"></span>
                                <span class="text">Online Dinleme Yap</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="callStatus" name="callStatus">
                                    <option disabled selected>Çağrı Durumu...</option>
                                    <option>Aktif</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex gap-3 align-items-center">
                                <span style="font-size: 0.875rem; font-weight: 400; color: black; white-space: nowrap;">Çağrı Tipi:</span>
                                <select class="form-select w-100"  id="callType" name="callType">
                                    <option selected>Tümü</option>
                                    <option>Çağrı Tipi 1</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseAgent" name="chooseAgent">
                                    <option disabled selected>Dahili Agent Seçin...</option>
                                    <option>Agent 1</option>
                                </select>    
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex align-items-center gap-3">
                                <select class="form-select w-100"  id="chooseBranch" name="chooseBranch">
                                    <option disabled selected>Şube Seçin...</option>
                                    <option>Şube 1</option>
                                </select>    
                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 px-2" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">
                                        <span class="iconify" style="font-size: 20px;" data-icon="hugeicons:arrow-right-02" data-inline="false"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Dahili',
                  'Açıklama',
                  'Şube',
                  'Panel Durumu',
                  'Çağrı Durumu',
                  'Çağrı Tipi',
                  'Arayan No',
                  'Aranan No',
                  'Çağrı Süresi'
                ] %}

                {% set rows = [
                  [
                    { type: 'checkbox', class: 'row-check' },
                    '1234',
                    'Açıklama 1',
                    'Şube 1',
                    { type: 'html', html: '<span class="status-badge deactive">Çevrimdışı</span>' },
                    { type: 'html', html: '<span class="status-badge deactive">Boşta</span>' },
                    'Gelen Çağrı',
                    '555-1234',
                    '444-5678',
                    '00:05:30'
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'agentStatusTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<div class="modal fade work-stats-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Çağrı Durumu</label>
                            <select class="form-select" id="modalCallStatus" name="modalCallStatus">
                                <option disabled selected>Çağrı Durumu Seçin...</option>
                                <option>Aktif</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Çağrı Tipi</label>
                            <select class="form-select" id="modalCallType" name="modalCallType">
                                <option selected>Tümü</option>
                                <option>Aktif</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Dahili Agent</label>
                            <select class="form-select" id="modalTargetAgent" name="modalTargetAgent">
                                <option disabled selected>Dahili Agent Seçin...</option>
                                <option>Agent 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Şube</label>
                            <select class="form-select" id="modalBranch" name="modalBranch">
                                <option disabled selected>Şube Seçin</option>
                                <option>Şube 1</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#agentStatusTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var onlineListenBtn = document.querySelector('.online-listen-btn');
        onlineListenBtn.disabled = true;
        onlineListenBtn.classList.add('disabled-btn');

        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#agentStatusTable tbody input[type="checkbox"]:checked');
            
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                onlineListenBtn.disabled = false;
                onlineListenBtn.classList.remove('disabled-btn');
            } else {
                onlineListenBtn.disabled = true;
                onlineListenBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#agentStatusTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#agentStatusTable tbody input[type="checkbox"]').length === 
                                 $('#agentStatusTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        

        updateButtonStates();
    });
</script>

{% endblock %}