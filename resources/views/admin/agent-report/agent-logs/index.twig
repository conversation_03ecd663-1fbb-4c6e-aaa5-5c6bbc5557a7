
{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/agent-report/agent-report.css') }}">
{% endblock %}

{% block page_content %}

<div class="agent-logs-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Agent <PERSON></h4>
        <div class="d-flex align-items-center route-links">
            <a>Agent <PERSON></a>
            <span class="mx-2">></span>
            <a href="/admin/agent-loglari">Agent <PERSON></a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title"><PERSON></h4>
            </div>
            <hr class="custom-hr">
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-100"  id="internalNo" name="internalNo" >
                                <input type="date" class="form-control w-100"  id="internalNo" name="internalNo" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" name="chooseAgent" id="chooseAgent">
                                    <option disabled selected>Dahili Agent Seçin...</option>
                                    <option>Agent 1</option>
                                </select>
                                <select class="form-select w-auto" id="chooseGroup" name="chooseGroup">
                                    <option disabled selected>Grup Seçin...</option>
                                    <option>Grup 1</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseOperationType" name="chooseOperationType">
                                    <option disabled selected>İşlem Türü Seçin...</option>
                                    <option>İşlem Türü 1</option>
                                </select>    
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseBreakType" name="chooseBreakType">
                                    <option disabled selected>Mola Tipi Seçin...</option>
                                    <option>Mola Tipi 1</option>
                                </select>
                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  
                  'İşlem Türü',
                  'Dahili',
                  'Dahili Açıklama',
                  'Başlangıç Zamanı',
                  'Bitiş Zamanı',
                  'Süre',
                  'IP Adresi',
                  'Mola Tipi',
                  'İşlem Açıklama'
                ] %}

                {% set rows = [
                  [
                    'Gelen Çağrı',
                    'Agent 1',
                    'Gelen çağrı açıklaması',
                    '2023-10-01 10:00:00',
                    '2023-10-01 10:05:00',
                    '00:05:00',
                    '***********',
                    'Mola Tipi 1',
                    'Gelen çağrı işlemi açıklaması'
                  ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'agentLogsTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<div class="modal fade work-stats-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form action="">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate" name="modalStartDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate" name="modalEndDate">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Grup</label>
                            <select class="form-select" id="modalTargetGroup" name="modalTargetGroup">
                                <option disabled selected>Grup Seçin...</option>
                                <option>Grup 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Dahili Agent</label>
                            <select class="form-select" id="modalTargetAgent" name="modalTargetAgent">
                                <option disabled selected>Dahili Agent Seçin</option>
                                <option>Agent 1</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">İşlem Türü</label>
                            <select class="form-select" id="modalTargetOperationType" name="modalTargetOperationType">
                                <option disabled selected>İşlem Türü Seçin</option>
                                <option>İşlem Türü 1</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label class="form-label">Mola Tipi</label>
                            <select class="form-select" id="modalTargetBreakType" name="modalTargetBreakType">
                                <option disabled selected>Mola Tipi Seçin</option>
                                <option>Mola Tipi 1</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#agentLogsTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#agentLogsTable tbody input[type="checkbox"]', function() {            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#agentLogsTable tbody input[type="checkbox"]').length === 
                                 $('#agentLogsTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
    });
</script>

{% endblock %}