{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/calls/calls.css') }}">
{% endblock %}


{% block page_content %}

<section class="calls-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Çağrılar</h4>
        <div class="d-flex align-items-center route-links">
            <a href="/admin/cagrilar">Çağrı Listesi</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs mb-3" id="callsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="call-list-tab" data-bs-toggle="tab" data-bs-target="#call-list" type="button" role="tab" aria-controls="call-list" aria-selected="true">Çağrı Listesi</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="queue-calls-tab" data-bs-toggle="tab" data-bs-target="#queue-calls" type="button" role="tab" aria-controls="queue-calls" aria-selected="false">Gelen Çağrılar</button>
            </li>
        </ul>
        
        <!-- Tab Content -->
        <div class="tab-content" style="padding-inline:20px; overflow-y: hidden !important;" id="callsTabsContent">
            <!-- Çağrı Listesi Tab -->
            <div class="tab-pane fade show active p-0" id="call-list" role="tabpanel" aria-labelledby="call-list-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="table-title">Çağrı Listesi</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button - Sadece 768px ve altında görünür -->
                    <div class="d-md-none d-flex justify-content-between px-4 mb-3 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 w-100 px-3 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn w-100 me-2 px-3 mobile-excel-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto"  id="startDate" name="startDate" >
                                <input type="date" class="form-control w-auto"  id="endDate" name="endDate" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto" id="callerNumber" name="callerNumber" placeholder="Arayan No" >
                                <input type="text" class="form-control w-auto" id="calledNumber" name="calledNumber" placeholder="Aranan No" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto"  id="chooseInternal" name="chooseInternal">
                                    <option disabled selected>Dahili Seç</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto" id="chooseStatus" name="chooseStatus">
                                    <option disabled selected>Durum Seç</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto"  id="callResultType" name="callResultType">
                                    <option disabled selected>Çağrı Sonuç Tipi Seç</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto"  id="chooseGroup" name="chooseGroup">
                                    <option disabled selected>Grup Seç</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto"  id="chooseScore" name="chooseScore">
                                    <option disabled selected>Puan Seç</option>
                                    <option>Arama</option>
                                </select>

                                <div class="d-flex align-items-center">
                                    <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                    <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                        { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                        'Tarih',
                        'Arayan No',
                        'Aranan No',
                        'Yanıtlayan',
                        'Bekleme S.',
                        'Çağrı Türü',
                        'Ses Kaydı',
                        'Yönetici Puanı',
                        'IVR Puanı',
                        'Konuşma S.',
                        'Durum',
                        'Notlar',
                        'Çağrı Sonuç Tipi'
                        ] %}

                        {% set rows = [
                            [
                                { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                { type: 'html', html: '<span class="status-badge active">Gelen Çağrılar</span>' },
                                'Lorem Ipsum',
                                '5',
                                '4',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                                'Lorem Ipsum',
                                'Gelen Çağrı'
                            ],
                            [
                                { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                { type: 'html', html: '<span class="status-badge deactive">Giden Çağrılar</span>' },
                                'Lorem Ipsum',
                                '5',
                                '4',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge deactive">Deaktif</span>' },
                                'Lorem Ipsum',
                                'Giden Çağrı'
                            ],
                            [
                                { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                { type: 'html', html: '<span class="status-badge all-calls">Tüm Çağrılar</span>' },
                                'Lorem Ipsum',
                                '5',
                                '4',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' },
                                'Lorem Ipsum',
                                'Tüm Çağrı'
                            ]
                        ] %}

                        {% include 'components/table.twig' with {
                        id: 'callListTable',
                        headers: headers,
                        rows: rows
                        } %}
                    </div>
                </div>
            </div>
            
            <!-- Kuyruk Çağrıları Tab -->
            <div class="tab-pane fade p-0" id="queue-calls" role="tabpanel" aria-labelledby="queue-calls-tab">
                <div class="p-0">
                    <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                        <h4 class="custom-title">Çağrı Listesi</h4>
                    </div>
                    <hr class="custom-hr">
                    <!-- Mobile Filter Button - Sadece 768px ve altında görünür -->
                    <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn w-100 me-2 px-3 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn w-100 me-2 px-3 mobile-excel-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                                <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="date" class="form-control w-auto" id="startDate" name="startDate" >
                                <input type="date" class="form-control w-auto"  id="endDate" name="endDate" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto" id="callerNumber" name="callerNumber" placeholder="Arayan No" >
                                <select class="form-select w-auto" id="callStatus" name="callStatus">
                                    <option disabled selected>Çağrı Durumu Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="respondingInternal" name="respondingInternal">
                                    <option disabled selected>Yanıtlayan Dahili Seçin...</option>
                                    <option>Arama</option>
                                </select>

                                <select class="form-select w-auto" id="queueSelect" name="queueSelect">
                                    <option disabled selected>Kuyruk Seçin...</option>
                                    <option>Arama</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                                <button class="dark-green-btn flex-grow-1 px-3" id="resetFilterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            </div>
                        </div>
                    </div>
                    <div class="custom-data-table-wrapper">
                        {% set headers = [
                        { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                        'Tarih',
                        'Kuyruk Adı',
                        'Arayan No',
                        'Aranan No',
                        'Yanıtlayan',
                        'Bekleme S.',
                        'Konuşma S.',
                        'Durum',
                        'Ses Kaydı'
                        ] %}

                        {% set rows = [
                            [
                                { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                'Kuyruk 1',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                                'Lorem Ipsum'
                            ],
                            [
                                 { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                'Kuyruk 1',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge deactive">Deaktif</span>' },
                                'Lorem Ipsum'
                            ],
                            [
                                 { type: 'checkbox', name: 'select' },
                                '11.11.2025',
                                'Kuyruk 1',
                                '00000000000',
                                '00000000000',
                                'Lorem Ipsum',
                                '02:15',
                                '00:30',
                                { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' },
                                'Lorem Ipsum'
                            ]
                        ] %}

                        {% include 'components/table.twig' with {
                        id: 'queueCallsTable',
                        headers: headers,
                        rows: rows
                        } %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filtre Modalı -->
<div class="modal fade calls-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalStartDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="modalStartDate">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalEndDate" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="modalEndDate">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalCallerNo" class="form-label">Arayan No</label>
                            <input type="text" class="form-control" id="modalCallerNo" placeholder="Arayan No">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCalledNo" class="form-label">Aranan No</label>
                            <input type="text" class="form-control" id="modalCalledNo" placeholder="Aranan No">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalExternal" class="form-label">Dış Hat</label>
                            <select class="form-select" id="modalExternal">
                                <option disabled selected>Dış Hat Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalStatus" class="form-label">Durum</label>
                            <select class="form-select" id="modalStatus" name="modalStatus">
                                <option disabled selected>Durum Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCallType" class="form-label">Çağrı Sonuç Tipi</label>
                            <select class="form-select" id="modalCallType" name="modalCallType">
                                <option disabled selected>Çağrı Sonuç Tipi Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalGroup" class="form-label">Grup</label>
                            <select class="form-select" id="modalGroup" name="modalGroup">
                                <option disabled selected>Grup Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalScore" class="form-label">Puan</label>
                            <select class="form-select" id="modalScore" name="modalScore">
                                <option disabled selected>Puan Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tabloları ve ilgili butonları dinamik olarak işle
        var tableIds = ['callListTable', 'queueCallsTable'];
        var dataTables = {};
        tableIds.forEach(function(tableId) {
            if (document.getElementById(tableId)) {
                dataTables[tableId] = $('#' + tableId).DataTable({
                    searching: false,
                    pageLength: 10,
                    language: {
                        lengthMenu: '_MENU_',
                        info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
                    },
                    columnDefs: [
                        { orderable: false, targets: 0 }
                    ],
                    drawCallback: function(settings) {
                        // Paging button stillemesi
                        document.querySelectorAll('#' + tableId + ' .dt-paging-button.current').forEach(function(btn) {
                            btn.style.color = '#FFFFFF';
                            btn.querySelectorAll('*').forEach(function(child) {
                                child.style.color = '#FFFFFF';
                            });
                        });
                    }
                });
                // dt-length'i dt-paging'in soluna taşı
                var length = $(dataTables[tableId].table().container()).find('.dt-length');
                var paging = $(dataTables[tableId].table().container()).find('.dt-paging');
                paging.before(length);
            }
        });

        // Her tablo için butonları ve checkboxları işle
        tableIds.forEach(function(tableId) {

            var $table = $('#' + tableId);
            var container = $table.closest('.white-container');
            // Tümünü seç/deselect
            $table.on('change', 'thead input[type="checkbox"]', function() {
                var checked = this.checked;
                $table.find('tbody input[type="checkbox"]').prop('checked', checked);
            });
            
        });
        
        // Responsive ve mobil menü işlemleri (varsa)
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
        
        // Filtre Modalı ve Mobil Filtre Butonları İşlemleri
        // 'show.bs.modal' eventini sadece bir kez ekle
        $('#filterModal').on('show.bs.modal', function() {
            // Aktif tab'ı bul
            var activeTab = $('.tab-pane.active').attr('id');
            updateFilterModalContent(activeTab);
        });
        
        // Aktif tab bilgisini globalde tut
        var currentActiveTab = $('.tab-pane.active').attr('id'); // Sayfa yüklenince aktif tabı ata

        // Tab değişikliğini dinle ve aktif tabı güncelle
        $('a[data-bs-toggle="tab"], button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            var targetTab = $(e.target).attr('data-bs-target') || $(e.target).attr('href');
            if (targetTab && targetTab.startsWith('#')) {
                currentActiveTab = targetTab.replace('#', '');
            }
        });

        // Filtre ikonuna tıklanınca sadece aktif tab bilgisini güncelle
        $('.filter-icon-btn').on('click', function() {
            currentActiveTab = $('.tab-pane.active').attr('id');
        });
        // Modal açılırken içeriği güncelle
        $('#filterModal').on('show.bs.modal', function() {
            if (currentActiveTab) {
                updateFilterModalContent(currentActiveTab);
            }
        });
        
        // Modal içeriğini aktif tab'a göre güncelle
        function updateFilterModalContent(activeTabId) {
            // Modal içeriğini temizle
            $('#filterModal .modal-body').empty();
            
            // Aktif tab'deki input alanlarını kopyala
            var filterFields = $('#' + activeTabId + ' .internal-choose .col');
            
            // Her bir sütundaki input alanlarını modalda göster
            filterFields.each(function(index) {
                // Bu sütundaki tüm d-md-flex içindeki form elemanlarını al
                var formElements = $(this).find('.d-md-flex').children().not('button');
                
                // Eğer form elemanı varsa, bunları modalda göster
                if (formElements.length > 0) {
                    formElements.each(function() {
                        var element = $(this);
                        var elementType = element.prop('tagName').toLowerCase();
                        var elementClone;
                        
                        // Eğer bu bir input, select veya başka bir form elemanı ise
                        if (elementType === 'input' || elementType === 'select') {
                            // Yeni bir div oluştur
                            var formGroup = $('<div class="col-12 mb-3"></div>');
                            
                            // Placeholder veya etiket için metin belirle
                            var labelText = '';
                            if (element.attr('placeholder')) {
                                labelText = element.attr('placeholder');
                            } else if (element.is('select')) {
                                labelText = element.find('option:selected').text();
                            } else if (element.attr('type') === 'date') {
                                // Başlangıç ve bitiş tarihleri için özel etiketler
                                if (index === 0) {
                                    labelText = (element.index() === 0) ? 'Başlangıç Tarihi' : 'Bitiş Tarihi';
                                }
                            }
                            
                            // Etiket ekle
                            var label = $('<label class="form-label"></label>').text(labelText);
                            formGroup.append(label);
                            
                            // Element'i klonla ve form-control sınıfını ekle
                            elementClone = element.clone().addClass('form-control');
                            elementClone.removeClass('w-auto'); // w-auto sınıfını kaldır
                            
                            // Klonlanmış elementi form grubuna ekle
                            formGroup.append(elementClone);
                            
                            // Form grubunu modalın body kısmına ekle
                            $('#filterModal .modal-body').append(formGroup);
                        }
                    });
                }
            });


        }
        
        // Modal değerlerini form alanlarına aktar
        function transferModalValuesToForm(activeTabId) {
            // Modal içindeki tüm input ve select elemanlarını al
            $('#filterModal .modal-body input, #filterModal .modal-body select').each(function(index) {
                var modalValue = $(this).val();
                var inputType = $(this).prop('tagName').toLowerCase();
                var inputIndex = index;
                
                // Aktif tab'deki karşılık gelen form elemanını bul
                var formElements = $('#' + activeTabId + ' .internal-choose .d-md-flex').find('input, select').not('button');
                
                // Eğer indeksteki eleman varsa değerini aktar
                if (formElements.length > inputIndex) {
                    $(formElements[inputIndex]).val(modalValue);
                }
            });
        }
        
        // Mobil Filtrele butonları
        $('.mobile-filter-btn').on('click', function() {
            // En yakın tab'i bul
            var tabContent = $(this).closest('.tab-pane');
            var tabId = tabContent.attr('id');
            
            // Hangi tab'de olduğumuza göre masaüstü filtre butonlarını tetikle
            if (tabId === 'call-list') {
                $('#call-list #filterButton').click();
            } else if (tabId === 'queue-calls') {
                $('#queue-calls #filterButton').click();
            }
        });
        
        // Mobil Excel butonları
        $('.mobile-excel-btn').on('click', function() {
            // En yakın tab'i bul
            var tabContent = $(this).closest('.tab-pane');
            var tabId = tabContent.attr('id');
            
            // Hangi tab'de olduğumuza göre masaüstü excel butonlarını tetikle
            if (tabId === 'call-list') {
                $('#call-list #resetFilterButton').click();
            } else if (tabId === 'queue-calls') {
                $('#queue-calls #resetFilterButton').click();
            }
        });
    });

</script>


{% endblock %}