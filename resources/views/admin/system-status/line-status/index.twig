{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/system/system.css') }}">
{% endblock %}


{% block page_content %}

<section class="line-status-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Hat Durumu</h4>
        <div class="d-flex align-items-center route-links">
            <a>Sistem Durumu</a>
            <span class="mx-2">></span>
            <a href="/admin/hat-durumu">Hat Durumu</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Hat Listesi</h4>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center justify-content-end px-4 internal-choose gap-3">

               
                    <select class="form-select" style="width: fit-content; min-width: 200px;" id="chooseGroup" name="chooseGroup" placeholder="Grup Seç...">
                        <option disabled selected>Grup Seç...</option>
                        <option>Arama</option>
                    </select>

                     <button class="blue-btn px-3 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                    
                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Hat',
                  'Bağlantılan IP',
                  'Durum'
                ] %}

                {% set rows = [
                    [
                         { type: 'checkbox', name: 'select' },
                        'Hat 1',
                        '***********',
                        { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                    ],
                    [
                         { type: 'checkbox', name: 'select' },
                        'Hat 2',
                        '***********',
                        { type: 'html', html: '<span class="status-badge deactive">Deaktif</span>' },
                    ],
                    [
                         { type: 'checkbox', name: 'select' },
                        'Hat 3',
                        '***********',
                        { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' },
                    ],
                ] %}

                {% include 'components/table.twig' with {
                  id: 'lineStatusTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#lineStatusTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        updateButtonStates();
        
        // Masaüstü butonlarını checkbox seçimine göre güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#lineStatusTable tbody input[type="checkbox"]:checked');
            var stopBtn = document.querySelector('.stop-call-btn');
            var listenBtn = document.querySelector('.online-listen-btn');
            // Her iki buton sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                if (stopBtn) {
                    stopBtn.disabled = false;
                    stopBtn.classList.remove('disabled-btn');
                }
                if (listenBtn) {
                    listenBtn.disabled = false;
                    listenBtn.classList.remove('disabled-btn');
                }
            } else {
                if (stopBtn) {
                    stopBtn.disabled = true;
                    stopBtn.classList.add('disabled-btn');
                }
                if (listenBtn) {
                    listenBtn.disabled = true;
                    listenBtn.classList.add('disabled-btn');
                }
            }
        }
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#lineStatusTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#lineStatusTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#lineStatusTable tbody input[type="checkbox"]').length ===
                                 $('#lineStatusTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        // Sayfa ilk açıldığında da butonları güncelle
        updateButtonStates();
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // updateMobileMenuItems fonksiyonunu override et
        const originalUpdateMobileMenuItems = window.updateMobileMenuItems;
        window.updateMobileMenuItems = function() {
            if (typeof originalUpdateMobileMenuItems === 'function') {
                originalUpdateMobileMenuItems();
            }
            // Sadece bu sayfa için: checkbox seçimine göre mobil menü item'larını güncelle
            var checkedCount = document.querySelectorAll('#lineStatusTable tbody input[type="checkbox"]:checked').length;
            var container = document.querySelector('.call-status-section .white-container');
            if (container) {
                var mobileMenu = container.querySelector('.responsive-menu');
                if (mobileMenu) {
                    var menuItems = mobileMenu.querySelectorAll('.dropdown-menu-item');
                    menuItems.forEach(function(item) {
                        if (checkedCount === 1) {
                            item.classList.remove('disabled');
                        } else {
                            item.classList.add('disabled');
                        }
                    });
                }
            }
        };
        // Sayfa ilk açıldığında da mobil menü item'larını güncelle
        window.updateMobileMenuItems();
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>

{% endblock %}