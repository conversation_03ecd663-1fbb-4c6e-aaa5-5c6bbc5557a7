{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/system/system.css') }}">
{% endblock %}


{% block page_content %}

<section class="call-status-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Çağrı Durumu</h4>
        <div class="d-flex align-items-center route-links">
            <a>Sistem Durumu</a>
            <span class="mx-2">></span>
            <a href="/admin/cagri-durumu">Çağr<PERSON> Durumu</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Ça<PERSON>r<PERSON> Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                       
                        <button class="stop-call-btn">
                            <span class="iconify" data-icon="hugeicons:call-blocked-02" data-inline="false"></span>
                            <span class="text">Çağrıyı Duraksat</span>
                        </button>
                        <button class="ms-2 online-listen-btn">
                            <span class="iconify" data-icon="solar:headphones-square-linear" data-inline="false"></span>
                            <span class="text">Online Dinleme Yap</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item">
                                <span class="iconify stop-call-btn" data-icon="hugeicons:call-blocked-02" data-inline="false"></span>
                                <span class="text">Çağrıyı Duraksat</span>
                            </div>
                            <div class="dropdown-menu-item">
                               <span class="iconify online-listen-btn" data-icon="solar:headphones-square-linear" data-inline="false"></span>
                                <span class="text">Online Dinleme Yap</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">

                <div class="d-md-none d-flex justify-content-between w-100">
                    <div class="d-flex align-items-center w-100">
                        <button class="blue-btn me-2 px-3 w-100 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                        <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                            <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                        </button>
                    </div>
                </div>

                <div class="d-none d-md-flex align-items-center w-100 gap-3">
                    <select class="form-select " id="chooseCallStatus" name="chooseCallStatus" placeholder="Çağrı Durumu Seç...">
                        <option disabled selected>Çağrı Durumu Seç...</option>
                        <option>Arama</option>
                    </select>
                    <select class="form-select " id="chooseCallType" name="chooseCallType" placeholder="Çağrı Tipi Seç...">
                        <option disabled selected>Çağrı Tipi Seç...</option>
                        <option>Arama</option>
                    </select>
                    <select class="form-select flex-grow-1" id="chooseGroup" name="chooseGroup" placeholder="Grup Seç...">
                        <option disabled selected>Grup Seç...</option>
                        <option>Arama</option>
                    </select>

                     <button class="blue-btn px-3 mobile-filter-btn" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                    
                </div>
                
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Arayan No',
                  'Aranan No',
                  'Dahili',
                  'Çağrı Tipi',
                  'Durum',
                  'Çağrı Süresi',
                  'Kodek'
                ] %}

                {% set rows = [
                    [
                        { type: 'checkbox', name: 'select' },
                        '0532 123 45 67',
                        '0532 765 43 21',
                        '1234',
                        'Gelen',
                        { type: 'html', html: '<span class="status-badge active">Aktif</span>' },
                        '00:05:12',
                        'G.711'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '0532 123 45 67',
                        '0532 765 43 21',
                        '1234',
                        'Gelen',
                        { type: 'html', html: '<span class="status-badge deactive">Deaktif</span>' },
                        '00:05:12',
                        'G.711'
                    ],
                    [
                        { type: 'checkbox', name: 'select' },
                        '0532 123 45 67',
                        '0532 765 43 21',
                        '1234',
                        'Gelen',
                        { type: 'html', html: '<span class="status-badge waiting">Bekliyor</span>' },
                        '00:05:12',
                        'G.711'
                    ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'callStatusTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>

<div class="modal fade calls-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-12 mb-3">
                            <label for="modalStatus" class="form-label">Çağrı Durumu Seç...</label>
                            <select class="form-select" id="modalStatus" name="modalStatus">
                                <option disabled selected>Çağrı Durumu Seç...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalCallType" class="form-label">Çağrı Tipi Seç</label>
                            <select class="form-select" id="modalCallType" name="modalCallType">
                                <option disabled selected>Çağrı Tipi Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="modalGroup" class="form-label">Grup Seç</label>
                            <select class="form-select" id="modalGroup" name="modalGroup">
                                <option disabled selected>Grup Seç</option>
                                <option>Arama</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#callStatusTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        updateButtonStates();
        
        // Masaüstü butonlarını checkbox seçimine göre güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#callStatusTable tbody input[type="checkbox"]:checked');
            var stopBtn = document.querySelector('.stop-call-btn');
            var listenBtn = document.querySelector('.online-listen-btn');
            // Her iki buton sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                if (stopBtn) {
                    stopBtn.disabled = false;
                    stopBtn.classList.remove('disabled-btn');
                }
                if (listenBtn) {
                    listenBtn.disabled = false;
                    listenBtn.classList.remove('disabled-btn');
                }
            } else {
                if (stopBtn) {
                    stopBtn.disabled = true;
                    stopBtn.classList.add('disabled-btn');
                }
                if (listenBtn) {
                    listenBtn.disabled = true;
                    listenBtn.classList.add('disabled-btn');
                }
            }
        }
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#callStatusTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#callStatusTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#callStatusTable tbody input[type="checkbox"]').length ===
                                 $('#callStatusTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        // Sayfa ilk açıldığında da butonları güncelle
        updateButtonStates();
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // updateMobileMenuItems fonksiyonunu override et
        const originalUpdateMobileMenuItems = window.updateMobileMenuItems;
        window.updateMobileMenuItems = function() {
            if (typeof originalUpdateMobileMenuItems === 'function') {
                originalUpdateMobileMenuItems();
            }
            // Sadece bu sayfa için: checkbox seçimine göre mobil menü item'larını güncelle
            var checkedCount = document.querySelectorAll('#callStatusTable tbody input[type="checkbox"]:checked').length;
            var container = document.querySelector('.call-status-section .white-container');
            if (container) {
                var mobileMenu = container.querySelector('.responsive-menu');
                if (mobileMenu) {
                    var menuItems = mobileMenu.querySelectorAll('.dropdown-menu-item');
                    menuItems.forEach(function(item) {
                        if (checkedCount === 1) {
                            item.classList.remove('disabled');
                        } else {
                            item.classList.add('disabled');
                        }
                    });
                }
            }
        };
        // Sayfa ilk açıldığında da mobil menü item'larını güncelle
        window.updateMobileMenuItems();
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>

{% endblock %}