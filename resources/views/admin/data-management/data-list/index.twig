{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/data-manage/data-manage.css') }}">
{% endblock %}

{% block page_content %}

<div class="data-list-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Data Listesi</h4>
        <div class="d-flex align-items-center route-links">
            <a>Data Yönetimi</a>
            <span class="mx-2">></span>
            <a href="/admin/data-listesi"> Data Listesi</a>
        </div>
    </div>
     <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <h4 class="table-title">Data Listesi</h4>
                <div class="d-flex align-items-center">
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="download-btn me-2" id="downloadBtn">
                            <span class="iconify" data-icon="hugeicons:book-upload" data-inline="false"></span>
                            <span class="ms-2">Data Yükle</span>
                        </button>
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item mobile-download-menu-item" id="mobileDownloadBtn">
                               <span class="iconify" style="background-color:#6149A5; border-radius: 6px;" data-icon="hugeicons:book-upload" data-inline="false"></span>
                                <span class="text">Data Yükle</span>
                            </div>
                            
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-md-none d-flex justify-content-between px-4 w-100">
                        <div class="d-flex align-items-center w-100">
                            <button class="blue-btn me-2 px-3 mobile-filter-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            <button class="dark-green-btn me-2 px-3 mobile-excel-btn w-100" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Excel</button>
                            <button class="filter-icon-btn" data-bs-toggle="modal" data-bs-target="#filterModal">
                               <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                            </button>
                        </div>
            </div>
                    
            <div class="row row-cols-lg-4 row-cols-md-2 internal-choose px-4" style="row-gap: 1rem;">
                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto"  id="nameSearch" name="nameSearch" placeholder="Adı Soyadı içinde ara" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <input type="text" class="form-control w-auto"  id="numberSearch" name="numberSearch" placeholder="Numara İle Ara" >
                            </div>
                        </div>

                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <select class="form-select w-auto" id="chooseCriteria" name="chooseCriteria" placeholder="Kriter Seçin...">
                                    <option disabled selected>Data Grubunu Seçin...</option>
                                    <option>Grup 1</option>
                                </select>
                            </div>
                        </div>


                        <div class="col">
                            <div class="d-none d-md-flex flex-column gap-3">
                                <button class="blue-btn flex-grow-1 me-2 px-3" id="filterButton" style="height: 32px !important; font-size: 0.875rem; font-weight: 400;">Filtrele</button>
                            </div>
                        </div>
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Adı Soyadı',
                  'Numara',
                  'Açıklama',
                  'Data Grubu',
                  'Şube',
                  'Ekleme Tarihi'
                ] %}

                {% set rows = [
                  
                  [
                    { type: 'checkbox', class: 'row-check' },
                    'Ahmet Yılmaz',
                    '555-123-4567',
                    'Açıklama 1',
                    'Data Grubu 1',
                    'Şube 1',
                    '2023-01-01'
                  ]
                ] %}

                {% include 'components/table.twig' with {
                  id: 'dataListTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</div>


<!-- Grup Ekle Modal -->
<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Grup Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="dataGroup" class="form-label">Data Grubu</label>
                                    <select class="form-select" id="dataGroup" name="dataGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Data Grubu 1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="iconify me-1" data-icon="solar:shield-warning-linear" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                <small style="color: #94A3B8; font-size: 12px; font-weight: 400;">Data Grubu oluşturmadan data ekleyemezsiniz.</small>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <label for="customerName" class="form-label">Adı</label>
                                    <input type="text" class="form-control" id="customerName" name="customerName">
                                </div>

                                <div class="col-sm-6">
                                    <label for="customerSurname" class="form-label">Soyadı</label>
                                    <input type="text" class="form-control" id="customerSurname" name="customerSurname">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="customerNumber" class="form-label">Numara</label>
                                    <input type="text" class="form-control" id="customerNumber" name="customerNumber">
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="customerDescription" class="form-label">Açıklama</label>
                                <textarea id="customerDescription" name="customerDescription" class="form-control" style="resize: none !important;" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="stepNextBtn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Filtre Modalı -->
<div class="modal fade data-filter" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Adı Soyadı</label>
                            <input type="text" class="form-control" id="modalName" name="modalName" placeholder="Adı Soyadı İçinde Ara">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Numara</label>
                            <input type="text" class="form-control" id="modalPhone" name="modalPhone" placeholder="Numara ile ara...">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Data Grubu</label>
                            <select class="form-select" id="modalDataGroup" name="modalDataGroup">
                                <option disabled selected>Data Grubunu Seçin...</option>
                                <option>Arama</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn" id="applyFilters">Uygula</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#dataListTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit butonu başlangıçta disabled yap
        var editBtn = document.querySelector('.edit-btn');
        var deleteBtn = document.querySelector('.delete-btn');
        editBtn.disabled = true;
        deleteBtn.disabled = true;
        editBtn.classList.add('disabled-btn');
        deleteBtn.classList.add('disabled-btn');

                // Global değişkenleri tanımla
        window.editBtn = editBtn;
        window.deleteBtn = deleteBtn;
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#dataListTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                editBtn.disabled = false;
                editBtn.classList.remove('disabled-btn');
            } else {
                editBtn.disabled = true;
                editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }

            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('.row-check').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#dataListTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#dataListTable tbody input[type="checkbox"]').length === 
                                 $('#dataListTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedRow = document.querySelector('#dataListTable tbody input[type="checkbox"]:checked').closest('tr');
                var rowData = table.row(checkedRow).data();

                
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Data Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Data Ekle';
            
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });


         // Delete butonuna tıklandığında
        deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#dataListTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Silme onaylandığında yapılacak işlemler
                    document.getElementById('confirmDeleteBtn').onclick = function() {
                        // Burada seçilen satırları silme işlemi yapılacak
                        // Şimdilik sadece modalı kapatıyoruz
                        deleteModal.hide();
                    };
                }
            }
        });

        updateButtonStates();
    });
</script>

{% endblock %}