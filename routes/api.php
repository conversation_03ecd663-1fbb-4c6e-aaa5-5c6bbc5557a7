<?php

use App\Http\Controllers\BreakController;
use App\Http\Controllers\CallController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CustomerGroupController;
use App\Http\Controllers\CustomerCriteriaController;
use App\Http\Controllers\CustomerTypeController;
use App\Http\Controllers\DialNumbersController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::middleware(['multi-auth'])->group(function () {
    Route::post('/call-create', [CallController::class, 'handleIncomingCall']);
    Route::post('/call-update', [CallController::class, 'handleAcceptCall']);
    Route::post('/call-recording', [CallController::class, 'storeRecording']);
});

// Diğer tüm endpointler klasik web+auth altında:
Route::middleware(['web', 'auth'])->group(function () {
    Route::apiResource('customers', CustomerController::class);
    Route::apiResource('customer-groups', CustomerGroupController::class);
    Route::apiResource('customer-criteria', CustomerCriteriaController::class);
    Route::apiResource('customer-types', CustomerTypeController::class);
    Route::apiResource('dial-numbers', DialNumbersController::class);
    Route::apiResource('calls', CallController::class);
    Route::get('users', [UserController::class, 'index']);
    Route::get('users/agents', [UserController::class, 'getAgents']);
    Route::delete('customers', [CustomerController::class, 'destroyMultiple']);

    // Break management routes
    Route::post('breaks/start', [BreakController::class, 'startBreak']);
    Route::post('breaks/end', [BreakController::class, 'endBreak']);
    Route::get('breaks/status', [BreakController::class, 'getBreakStatus']);
    Route::delete('customer-groups', [CustomerGroupController::class, 'destroyMultiple']);
    Route::delete('customer-criteria', [CustomerCriteriaController::class, 'destroyMultiple']);
    Route::delete('customer-types', [CustomerTypeController::class, 'destroyMultiple']);
    Route::delete('dial-numbers', [DialNumbersController::class, 'destroyMultiple']);
});