<?php

namespace App\Services;

use App\Repositories\ModelRepository;

class ModelService
{
    protected $repository;

    // Constructor'da repository'yi al<PERSON>
    public function __construct(ModelRepository $repository)
    {
        $this->repository = $repository;
    }

    // Tüm verileri al
    public function getAll()
    {
        return $this->repository->getAll();
    }

    // Yeni bir veri oluştur
    public function create(array $data)
    {
        return $this->repository->create($data);
    }

    // Veriyi güncelle
    public function update($id, array $data)
    {
        return $this->repository->update($id, $data);
    }

    // Veriyi sil
    public function delete($id)
    {
        return $this->repository->delete($id);
    }

    // Tek bir veriyi al
    public function find($id)
    {
        return $this->repository->find($id);
    }
}
