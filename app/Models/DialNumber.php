<?php

namespace App\Models;

use App\Traits\UseCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DialNumber extends Model
{
    use HasFactory, UseCompany;

    protected $fillable = [
        'dial_code',
        'password',
        'agent_password',
        'domain_info',
        'internal_description',
        'email',
        'display_number',
        'group_name',
        'status',
        'internal_group',
        'internal_username',
        'codec',
        'display_search',
        'search_prefix_group',
        'channel_limit',
        'timeout_duration',
        'internal_call_allowed',
        'external_call_allowed',
        'receive_external_call_allowed',
        'incoming_call_recording',
        'dialable_from_ivr',
        'call_waiting_disabled',
        'show_name',
        'do_not_disturb',
        'work_schedule',
        'all_calls',
        'no_answer',
        'if_busy',
        'if_closed',
        'company_id'
    ];
    
    public function user()
    {
        return $this->hasOne(User::class, 'dial_id');
    }

}