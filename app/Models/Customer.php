<?php

namespace App\Models;

use App\Traits\UseCompany;
use App\Traits\Uuidable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class Customer extends Model
{
    use HasFactory, SoftDeletes, Uuidable, UseCompany;

    protected $table = 'customers';

    // Veritabanı kolonları
    protected $fillable = [
        'uuid', 'first_name', 'last_name', 'phone', 'oid', 'company_id', 'agent_id', 
        'identity_no', 'birth_day', 'customer_company_id', 'gsm', 'fax', 'email', 
        'address', 'neighborhood', 'city', 'country', 'can_receive_sms', 
        'can_receive_call', 'tax_no', 'tax_office', 'description', 'status',
        'customer_group_id', 'customer_criteria_id', 'customer_type_id', 'dynamic_ivr_id',
    ];

    protected static function booted()
    {
        static::addGlobalScope('agent', function (Builder $builder) {
            $user = auth()->user();
            if ($user && $user->role === 'agent') {
                $builder->where('agent_id', $user->id);
            }
        });
    }

    /**
     * İlişkiler
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id');
    }
    
    public function customerGroup()
    {
        return $this->belongsTo(CustomerGroup::class, 'customer_group_id');
    }

    public function customerCriteria()
    {
        return $this->belongsTo(CustomerCriteria::class, 'customer_criteria_id');
    }

    public function customerType()
    {
        return $this->belongsTo(CustomerType::class, 'customer_type_id');
    }
    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            0 => '<span class="status-badge active">Aktif</span>',
            1 => '<span class="status-badge deactive">Deaktif</span>',
            2 => '<span class="status-badge waiting">Bekliyor</span>',
            default => '<span class="status-badge unknown">Bilinmiyor</span>',
        };
    }

}
