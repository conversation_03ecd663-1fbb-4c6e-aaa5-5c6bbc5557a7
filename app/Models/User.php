<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'internal_number',
        'sip_pass',
        'ringtone',
        'password',
        'role',
        'company_id',
        'dial_id',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
 
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get all breaks for this user.
     */
    public function breaks()
    {
        return $this->hasMany(AgentBreak::class);
    }

    /**
     * Get the current active break for this user.
     */
    public function activeBreak()
    {
        return $this->hasOne(AgentBreak::class)->whereNull('ended_at');
    }

    /**
     * Check if the user is currently on break.
     *
     * @return bool
     */
    public function isOnBreak()
    {
        return $this->activeBreak()->exists();
    }

    /**
     * Get the current active break instance.
     *
     * @return AgentBreak|null
     */
    public function getCurrentBreak()
    {
        return $this->activeBreak()->first();
    }

    public function dialNumber()
    {
        return $this->belongsTo(DialNumber::class, 'dial_id');
    }

}