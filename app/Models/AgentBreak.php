<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class AgentBreak extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_id',
        'break_type',
        'break_description',
        'started_at',
        'ended_at',
        'duration_seconds',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the break.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that owns the break.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Check if the break is currently active (not ended).
     *
     * @return bool
     */
    public function isActive()
    {
        return is_null($this->ended_at);
    }

    /**
     * Get the current duration of the break in seconds.
     *
     * @return int
     */
    public function getCurrentDurationSeconds()
    {
        if ($this->isActive()) {
            return Carbon::now()->diffInSeconds($this->started_at);
        }

        return $this->duration_seconds ?? 0;
    }

    /**
     * Get formatted duration string.
     *
     * @return string
     */
    public function getFormattedDuration()
    {
        $seconds = $this->getCurrentDurationSeconds();

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $remainingSeconds);
        }
    }

    /**
     * Scope to get active breaks.
     */
    public function scopeActive($query)
    {
        return $query->whereNull('ended_at');
    }

    /**
     * Scope to get breaks for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get breaks for a specific company.
     */
    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get the break type in a human-readable format.
     *
     * @return string
     */
    public function getBreakTypeDisplayAttribute()
    {
        $types = [
            'mola' => 'Mola',
            'toplanti' => 'Toplantı',
            'egitim' => 'Eğitim',
            'diger' => 'Diğer'
        ];

        return $types[$this->break_type] ?? ucfirst($this->break_type);
    }
}
