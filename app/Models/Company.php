<?php

namespace App\Models;

use App\Traits\Uuidable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes, Uuidable;

    protected $table = 'companies';

    protected $fillable = [
        'uuid', 'name', 'slug', 'email', 'phone', 'phone_2', 'fax', 'owner_id', 'address', 'website'
    ];

    public function settings()
    {
        return $this->hasMany(CompanySetting::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    
    public function getSettingValue($key)
    {
        $setting = $this->settings->firstWhere('key', $key);
        return $setting ? $setting->value : null; 
    }
}
