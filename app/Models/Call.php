<?php

namespace App\Models;

use App\Enum\CallStatus;
use App\Enum\CallType;
use App\Traits\UseCompany;
use App\Traits\UseUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class Call extends Model
{
    use SoftDeletes, UseCompany, UseUser;

    protected $fillable = [
        'user_id',
        'company_id',
        'caller_id',
        'hash',
        'duration',
        'status',
        'type',
        'record_path',
        'dial_number',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function getStatusAttribute()
    {
        return CallStatus::tryFrom($this->attributes['status']) ?? CallStatus::COMPLETED;
    }

    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            CallStatus::MISSED    => 'Cevapsız',
            CallStatus::ON_CALL   => 'Çağrıda',
            CallStatus::COMPLETED => 'Tamamlandı',
            CallStatus::FAILED    => 'Başarısız',
            default               => 'Bilinmiyor',
        };
    }

    public function getTypeAttribute()
    {
        return CallType::tryFrom($this->attributes['type']) ?? CallType::INCOMING;
    }

    public function getTypeLabelAttribute()
    {
        return match ($this->type) {
            CallType::OUTGOING => 'Giden',
            CallType::INCOMING => 'Gelen',
            default    => 'Bilinmiyor',
        };
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            CallStatus::MISSED    => 'deactive',
            CallStatus::COMPLETED => 'active',
            CallStatus::ON_CALL   => 'waiting',
            CallStatus::FAILED    => 'deactive',
            default               => 'unknown',
        };
    }

}
