<?php

namespace App\Providers;

use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('*', function ($view) {
            $user = Auth::user();
            $view->with('authUser', $user);

            if ($user && Auth::check()) {
                $company = $user->load('company.settings')->company;
                $view->with('company', $company);

                // Share break status with all views
                try {
                    $activeBreak = $user->getCurrentBreak();
                    $view->with('userActiveBreak', $activeBreak);
                    $view->with('userIsOnBreak', $user->isOnBreak());
                } catch (\Exception $e) {
                    // Fallback if break methods fail
                    $view->with('userActiveBreak', null);
                    $view->with('userIsOnBreak', false);
                }
            } else {
                $view->with('userActiveBreak', null);
                $view->with('userIsOnBreak', false);
            }
            });
    }
}
