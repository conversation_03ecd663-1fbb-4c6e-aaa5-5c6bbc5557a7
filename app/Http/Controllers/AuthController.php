<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class AuthController extends Controller
{
   public function login(Request $request)
    {
        // Query parametresine göre loginType belirle
        $loginType = $request->query('type', 'agent');
        if (Auth::check()) {
            return redirect('/');
        }
        return view('register.index', ['loginType' => $loginType]);
    }

    public function attempt(Request $request)
    {
        $credentials = $request->only('email', 'password');
        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            session(['last_login_type' => $user->role]);
            return redirect('/');
        }
        // Hatalı girişte, hangi tipteyse ona geri dön
        $loginType = $request->query('type', 'agent');
        return back()->withInput()->withErrors(['email' => 'Geçersiz giriş bilgileri'])->with('loginType', $loginType);
    }
    
    public function logout()
    {
        $redirect = '/giris';
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->role === 'admin') {
                $redirect = '/giris?type=admin';
            } else {
                $redirect = '/giris?type=agent';
            }
        } else if (session('last_login_type') === 'admin') {
            $redirect = '/giris?type=admin';
        } else {
            $redirect = '/giris?type=agent';
        }
        Auth::logout();
        session()->forget('last_login_type');
        return redirect($redirect);
    }
}
