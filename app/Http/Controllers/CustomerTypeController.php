<?php

namespace App\Http\Controllers;

use App\Services\CustomerTypeService;
use App\Http\Requests\StoreCustomerTypeRequest;
use App\Http\Requests\UpdateCustomerTypeRequest;
use App\Traits\CrudActions;

class CustomerTypeController extends Controller
{
    use CrudActions;

    public function __construct(CustomerTypeService $customerTypeService)
    {
        $this->service = $customerTypeService;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCustomerTypeRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCustomerTypeRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'müşteri tipi';
    }
}
