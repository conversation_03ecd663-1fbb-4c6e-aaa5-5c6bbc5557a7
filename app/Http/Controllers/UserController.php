<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Get all agents (users with role = 'agent')
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAgents()
    {
        try {
            $agents = User::where('role', 'agent')
                ->select('id', 'name', 'email', 'role')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $agents,
                'message' => 'Agents retrieved successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving agents.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all users
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $users = User::select('id', 'name', 'email', 'role')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $users,
                'message' => 'Users retrieved successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving users.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
