<?php

namespace App\Http\Controllers;

use App\Services\CustomerGroupService;
use App\Http\Requests\StoreCustomerGroupRequest;
use App\Http\Requests\UpdateCustomerGroupRequest;
use App\Traits\CrudActions;

class CustomerGroupController extends Controller
{
    use CrudActions;

    public function __construct(CustomerGroupService $customerGroupService)
    {
        $this->service = $customerGroupService;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCustomerGroupRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCustomerGroupRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'müşteri grubu';
    }
}
