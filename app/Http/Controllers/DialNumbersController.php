<?php

namespace App\Http\Controllers;

use App\Models\DialNumber;
use App\Services\DialNumberService;
use App\Traits\CrudActions;
use App\Http\Requests\StoreDialNumberRequest;
use App\Http\Requests\UpdateDialNumberRequest;

class DialNumbersController extends Controller
{
    use CrudActions;

    public function __construct(DialNumberService $service)
    {
        $this->service = $service;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreDialNumberRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateDialNumberRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'dahili numara';
    }

    public function internalList()
    {
        $dial_numbers = DialNumber::with('user')
        ->whereHas('user')
        ->orderBy('created_at', 'desc')
        ->get();
        return view('admin.internal-process.internal-list.index', compact('dial_numbers'));
    }

    public function internalGroups()
    {
        return view('admin.internal-process.internal-groups.index');
    }
}
