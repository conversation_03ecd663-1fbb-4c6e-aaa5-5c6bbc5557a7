<?php

namespace App\Http\Controllers;

use App\Services\CustomerService;
use App\Http\Requests\StoreCustomerRequest;
use App\Http\Requests\UpdateCustomerRequest;
use App\Traits\CrudActions;

class CustomerController extends Controller
{
    use CrudActions;

    public function __construct(CustomerService $service)
    {
        $this->service = $service;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCustomerRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCustomerRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'müşteri';
    }

    public function customerList() {
        $customers = $this->service->getAll();
        if (request()->ajax()) {
            return view('user.customer-list.content', compact('customers'))->render();
        }
        return view('user.customer-list.index', compact('customers'));
    }
}

