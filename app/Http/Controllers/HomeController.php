<?php

namespace App\Http\Controllers;

use App\Models\Call;
use App\Models\AgentBreak;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    public function index()
    {
        $view_tag = request()->ajax() ? 'content' : 'index';
        $user = Auth::user();

        if ($user->role === 'admin') {
            return view('admin.home.' . $view_tag);
        } else {
            // Call statistics
            $incoming_calls_count = Call::where('user_id', $user->id)->count();
            $outgoing_calls_count = Call::where('user_id', $user->id)->where('type', 'OUTGOING')->count();

            // Break statistics
            $today_break_time = $this->getBreakTime($user->id);

            $data = [
                'user' => $user,
                'incoming_calls_count' => $incoming_calls_count,
                'outgoing_calls_count' => $outgoing_calls_count,
                'today_break_time' => $today_break_time,
            ];

            return view('user.home.' . $view_tag, compact('data'));
        }
    }

    /**
     * Get break statistics for a user
     */
    private function getBreakTime($userId)
    {
        $today = Carbon::today();
        $breaks = AgentBreak::where('user_id', $userId)
            ->whereDate('started_at', $today)
            ->orWhere(function ($query) use ($userId, $today) {
                $query->where('user_id', $userId)
                    ->whereNull('ended_at')
                    ->whereDate('started_at', '<=', $today);
            })
            ->get();

        if ($breaks->isEmpty()) {
            return '00:00:00';
        }

        $totalSeconds = 0;

        foreach ($breaks as $break) {
            if ($break->ended_at) {
                $totalSeconds += $break->duration_seconds ?? 0;
            } else {
                $startTime = Carbon::parse($break->started_at);
                $totalSeconds += abs(Carbon::now()->diffInSeconds($startTime));
            }
        }

        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }


    public function profile()
    {
        return view('admin.profile.index');
    }
}
