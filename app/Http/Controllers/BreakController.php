<?php

namespace App\Http\Controllers;

use App\Models\AgentBreak;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class BreakController extends Controller
{
    /**
     * Start a new break for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function startBreak(Request $request)
    {
        try {
            Log::info('Break start request received', [
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            // Validate the request
            $validator = Validator::make($request->all(), [
                'break_type' => 'required|string|max:255',
                'break_description' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Geçersiz veri gönderildi.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();

            // Check if user already has an active break
            $activeBreak = AgentBreak::where('user_id', $user->id)
                ->whereNull('ended_at')
                ->first();

            if ($activeBreak) {
                return response()->json([
                    'success' => false,
                    'message' => 'Zaten aktif bir molanız bulunmaktadır.',
                    'data' => [
                        'active_break' => $activeBreak
                    ]
                ], 409);
            }

            // Create new break record
            $break = AgentBreak::create([
                'user_id' => $user->id,
                'company_id' => $user->company_id,
                'break_type' => $request->break_type,
                'break_description' => $request->break_description,
                'started_at' => Carbon::now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Mola başarıyla başlatıldı.',
                'data' => [
                    'break' => $break,
                    'break_id' => $break->id,
                    'started_at' => $break->started_at->format('Y-m-d H:i:s'),
                    'break_type' => $break->break_type,
                    'break_description' => $break->break_description
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Mola başlatılırken bir hata oluştu.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * End the current active break for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function endBreak(Request $request)
    {
        try {
            $user = Auth::user();

            // Find the active break
            $activeBreak = AgentBreak::where('user_id', $user->id)
                ->whereNull('ended_at')
                ->first();

            if (!$activeBreak) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aktif bir mola bulunamadı.',
                ], 404);
            }

            // Calculate duration and end the break
            $endTime = Carbon::now();
            $startTime = Carbon::parse($activeBreak->started_at);
            $durationSeconds = abs($endTime->diffInSeconds($startTime));

            $activeBreak->update([
                'ended_at' => $endTime,
                'duration_seconds' => $durationSeconds,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Mola başarıyla sonlandırıldı.',
                'data' => [
                    'break' => $activeBreak->fresh(),
                    'break_id' => $activeBreak->id,
                    'ended_at' => $endTime->format('Y-m-d H:i:s'),
                    'duration_seconds' => $durationSeconds,
                    'duration_formatted' => $this->formatDuration($durationSeconds)
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Mola sonlandırılırken bir hata oluştu.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the current active break status for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBreakStatus()
    {
        try {
            $user = Auth::user();

            $activeBreak = AgentBreak::where('user_id', $user->id)
                ->whereNull('ended_at')
                ->first();

            if (!$activeBreak) {
                return response()->json([
                    'success' => true,
                    'message' => 'Aktif mola bulunamadı.',
                    'data' => [
                        'has_active_break' => false,
                        'active_break' => null
                    ]
                ], 200);
            }

            // Calculate current duration
            $currentTime = Carbon::now();
            $startTime = Carbon::parse($activeBreak->started_at);
            $currentDurationSeconds = $currentTime->diffInSeconds($startTime);

            return response()->json([
                'success' => true,
                'message' => 'Aktif mola bilgisi getirildi.',
                'data' => [
                    'has_active_break' => true,
                    'active_break' => $activeBreak,
                    'current_duration_seconds' => $currentDurationSeconds,
                    'current_duration_formatted' => $this->formatDuration($currentDurationSeconds)
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Mola durumu alınırken bir hata oluştu.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format duration in seconds to human readable format
     *
     * @param int $seconds
     * @return string
     */
    private function formatDuration($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $remainingSeconds);
        }
    }
}
