<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCallRequest;
use App\Http\Requests\UpdateCallRequest;
use App\Services\CallService;
use App\Services\UserService;
use App\Traits\CrudActions;
use Illuminate\Http\Request;

class Call<PERSON>ontroller extends Controller
{
    use CrudActions;
    protected $userService;

    public function __construct(CallService $service, UserService $userService)
    {
        $this->service = $service;
        $this->userService = $userService;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCallRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCallRequest::class;
    }

    protected function getResourceName(): string
    {
        return 'çağrı';
    }
    public function calls() {
        $calls = $this->service->getAllCategorized();
        if (request()->ajax()) {
            return view('user.calls.content', compact('calls'))->render();
        }
        return view('user.calls.index', compact('calls'));
    }

    public function storeRecording(Request $request)
    {
        $file = $request->file('file');
        $call_id = $request->input('call_id');
        $duration = $request->input('duration');
        if (!$file || !$call_id) {
            return response()->json(['error' => 'File and call ID are required'], 400);
        }

        $this->service->storeRecording([
            'file' => $file,
            'call_id' => $call_id,
            'duration' => $duration,
        ]);

        return response()->json(['message' => 'Call recording stored successfully'], 201);
    
    }

    public function handleIncomingCall(Request $request){
        $callData = $request->all();
        $callData['type'] = 'INCOMING';

        $this->service->create($callData);
        return response()->json(['message' => 'Incoming call handled successfully'], 200);
    }

    public function handleAcceptCall(Request $request)
    {
        $callData = $request->all();
        $callData['status'] = 'ON_CALL';
        $dialed_user = $this->userService->getByDialId($callData['dial_number']);

        if (!$dialed_user) {
            return response()->json(['error' => 'Dialed user not found'], 404);
        }
        
        $callData['user_id'] = $dialed_user->id ?? null;
        $callData['company_id'] = $dialed_user->company_id ?? null;

        $call = $this->service->getCallByCallId($callData['hash']);


        $this->service->update($call->id, $callData);
        return response()->json(['message' => 'Call accepted successfully'], 200);
    }
}