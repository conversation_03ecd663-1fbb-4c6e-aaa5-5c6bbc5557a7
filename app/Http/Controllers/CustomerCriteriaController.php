<?php

namespace App\Http\Controllers;

use App\Services\CustomerCriteriaService;
use App\Http\Requests\StoreCustomerCriteriaRequest;
use App\Traits\CrudActions; 
use App\Http\Requests\UpdateCustomerCriteriaRequest;


class CustomerCriteriaController extends Controller
{
    use CrudActions;

    public function __construct(CustomerCriteriaService $service)
    {
        $this->service = $service;
    }

    protected function getStoreRequestClass(): string
    {
        return StoreCustomerCriteriaRequest::class;
    }

    protected function getUpdateRequestClass(): string
    {
        return UpdateCustomerCriteriaRequest::class;
    }
    protected function getResourceName(): string
    {
        return 'müşteri kriteri';
    }
}
