<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCallRequest;
use App\Http\Requests\UpdateCallRequest;
use App\Services\CallService;
use App\Traits\CrudActions;
use Illuminate\Http\Request;

class CallController extends Controller
{
    use CrudActions;

    public function __construct(CallService $service)
    {
        $this->service = $service;
    }

    protected function getStoreRequestClass()
    {
        return null;
    }

    protected function getUpdateRequestClass()
    {
        return null;
    }

    protected function getResourceName(): string
    {
        return 'çağrı kaydı';
    }
    
}
