<?php

namespace App\Http\Controllers;

class AdminController extends Controller
{

    // Sidebar


    public function monitoring()
    {
        return view('admin.monitoring.index');
    }

    public function groupUsers()
    {
        return view('admin.internal-process.group-users.index');
    }

    public function queueList()
    {
        return view('admin.queue.queue-list.index');
    }

    public function waitingMusic()
    {
        return view('admin.queue.waiting-music.index');
    }

    public function anonsRecording()
    {
        return view('admin.queue.anons-recording.index');
    }

    public function ivrList()
    {
        return view('admin.ivr.ivr-list.index');
    }

    public function dynamicIvrList()
    {
        return view('admin.ivr.dynamic-ivr-list.index');
    }

    public function scoreList()
    {
        return view('admin.ivr.score-list.index');
    }

    public function ivrRecording()
    {
        return view('admin.ivr.ivr-record.index');
    }

    public function externalLines()
    {
        return view('admin.settings.external-lines.index');
    }


    public function shiftTemplates()
    {
        return view('admin.settings.shift-template.index');
    }

    public function callAuthorization()
    {
        return view('admin.settings.call-auth.index');
    }

    public function blacklist()
    {
        return view('admin.settings.blacklist.index');
    }

    public function calls()
    {
        return view('admin.calls.index');
    }

    public function callStatus()
    {
        return view('admin.system-status.call-status.index');
    }

    public function internalStatus()
    {
        return view('admin.system-status.internal-status.index');
    }

    public function lineStatus()
    {
        return view('admin.system-status.line-status.index');
    }

    public function advancedFeatures()
    {
        return view('admin.advanced.index');
    }

    public function contact()
    {
        return view('admin.contact.index');
    }


    // Topbar

    public function ccDashboard()
    {
        return view('admin.cc-dashboard.index');
    }

    public function customerList()
    {
        return view('admin.customer.customer-list.index');
    }

    public function customerGroups()
    {
        return view('admin.customer.customer-groups.index');
    }

    public function customerCriteria()
    {
        return view('admin.customer.customer-criteria.index');
    }

    public function customerTypes()
    {
        return view('admin.customer.customer-types.index');
    }

    public function plannedCalls()
    {
        return view('admin.call-management.planned-calls.index');
    }

    public function callResultTypes()
    {
        return view('admin.call-management.call-result-types.index');
    }

    public function missedCalls()
    {
        return view('admin.call-management.missed-calls.index');
    }

    public function missedIncomingCalls()
    {
        return view('admin.call-management.missed-incoming-calls.index');
    }

    public function callBackRequests()
    {
        return view('admin.call-management.callback-requests.index');
    }

    public function autoCall()
    {
        return view('admin.call-management.auto-call.index');
    }

    public function meetConfirmation()
    {
        return view('admin.call-management.meet-conf.index');
    }

    public function autoSurvey()
    {
        return view('admin.call-management.auto-survey.index');
    }

    public function dataList()
    {
        return view('admin.data-management.data-list.index');
    }

    public function dataGroups()
    {
        return view('admin.data-management.data-groups.index');
    }

    public function authorizations()
    {
        return view('admin.crm-settings.authorizations.index');
    }

    public function agentBreakTypes()
    {
        return view('admin.crm-settings.agent-break.index');
    }

    public function workStatistics()
    {
        return view('admin.agent-report.work-stats.index');
    }

    public function agentStatus()
    {
        return view('admin.agent-report.agent-status.index');
    }

    public function agentLogs()
    {
        return view('admin.agent-report.agent-logs.index');
    }
}