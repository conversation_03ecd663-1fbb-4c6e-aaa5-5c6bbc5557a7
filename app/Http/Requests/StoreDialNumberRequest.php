<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDialNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dial_code' => 'required|string|max:10|unique:dial_numbers,dial_code',
            'password' => 'nullable|string|max:255',
            'agent_password' => 'nullable|string|max:255',
            'domain_info' => 'nullable|string|max:255',
            'internal_description' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'display_number' => 'nullable|string|max:255',
            'group_name' => 'nullable|string|max:255',
            'status' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom validation error messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'dial_code.required' => 'Dahili numara zorunludur.',
            'dial_code.string' => 'Dahili numara geçerli bir metin olmalıdır.',
            'dial_code.max' => 'Dahili numara en fazla 10 karakter olabilir.',
            'dial_code.unique' => 'Bu dahili numara zaten kullanılmaktadır.',
            'password.string' => 'Parola geçerli bir metin olmalıdır.',
            'password.max' => 'Parola en fazla 255 karakter olabilir.',
            'agent_password.string' => 'Agent parolası geçerli bir metin olmalıdır.',
            'agent_password.max' => 'Agent parolası en fazla 255 karakter olabilir.',
            'domain_info.string' => 'Domain bilgisi geçerli bir metin olmalıdır.',
            'domain_info.max' => 'Domain bilgisi en fazla 255 karakter olabilir.',
            'internal_description.string' => 'Dahili açıklaması geçerli bir metin olmalıdır.',
            'internal_description.max' => 'Dahili açıklaması en fazla 255 karakter olabilir.',
            'email.email' => 'Geçerli bir e-posta adresi giriniz.',
            'email.max' => 'E-posta adresi en fazla 255 karakter olabilir.',
            'display_number.string' => 'Görünen numara geçerli bir metin olmalıdır.',
            'display_number.max' => 'Görünen numara en fazla 255 karakter olabilir.',
            'group_name.string' => 'Grup adı geçerli bir metin olmalıdır.',
            'group_name.max' => 'Grup adı en fazla 255 karakter olabilir.',
            'status.string' => 'Durum geçerli bir metin olmalıdır.',
            'status.max' => 'Durum en fazla 255 karakter olabilir.',
        ];
    }
}
