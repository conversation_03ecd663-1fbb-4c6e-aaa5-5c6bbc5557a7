<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerCriteriaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|string|max:255|unique:customer_criterias,title',
            'description' => 'nullable|string',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'title.required' => 'Başlık alanı zorunludur.',
            'title.string' => 'Başlık geçerli bir metin olmalıdır.',
            'title.max' => 'Başlık en fazla 255 karakter olmalıdır.',
            'title.unique' => 'Bu başlık zaten kullanılmaktadır.',
            'description.string' => 'Açıklama geçerli bir metin olmalıdır.',
        ];
    }
}
