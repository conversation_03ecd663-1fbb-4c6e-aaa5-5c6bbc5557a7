<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // İzin kontrolü yapabilirsiniz
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:55',
            'last_name' => 'required|string|max:55',
            'phone' => 'nullable|string|max:55',
            'oid' => 'nullable|string|max:55',
            'company_id' => 'nullable|exists:companies,id',
            'agent_id' => [
                'nullable',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $user = \App\Models\User::find($value);
                        if (!$user || $user->role !== 'agent') {
                            $fail('<PERSON><PERSON><PERSON><PERSON> kullan<PERSON><PERSON><PERSON> geç<PERSON>li bir agent değil.');
                        }
                        if ($user && Auth::check() && $user->company_id !== Auth::user()->company_id) {
                            $fail('Sadece kendi şirketinizden agent seçebilirsiniz.');
                        }
                    }
                }
            ],
            'identity_no' => 'nullable|digits:10',
            'birth_day' => 'nullable|date',
            'customer_company_id' => 'nullable|exists:customer_companies,id',
            'gsm' => 'nullable|string|max:55',
            'fax' => 'nullable|string|max:55',
            'email' => 'nullable|email|unique:customers,email,' . $this->route('customer') . '|max:55', 
            'address' => 'nullable|string',
            'neighborhood' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:55',
            'country' => 'nullable|string|max:55',
            'can_receive_sms' => 'nullable|boolean',
            'can_receive_call' => 'nullable|boolean',
            'tax_no' => 'nullable|string|max:55',
            'tax_office' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|in:0,1,2',
            'customer_group_id' => 'nullable|exists:customer_groups,id',
            'customer_criteria_id' => 'nullable|exists:customer_criterias,id',
            'customer_type_id' => 'nullable|exists:customer_types,id',
            'dynamic_ivr_id' => 'nullable|exists:dynamic_ivrs,id',
        ];
    }
     /**
     * Get custom validation error messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'first_name.required' => 'İsim alanı zorunludur.',
            'first_name.string' => 'İsim geçerli bir metin olmalıdır.',
            'first_name.max' => 'İsim alanı en fazla 55 karakter olmalıdır.',
            
            'last_name.required' => 'Soyisim alanı zorunludur.',
            'last_name.string' => 'Soyisim geçerli bir metin olmalıdır.',
            'last_name.max' => 'Soyisim alanı en fazla 55 karakter olmalıdır.',
            
            'phone.string' => 'Telefon geçerli bir metin olmalıdır.',
            'phone.max' => 'Telefon numarası en fazla 55 karakter olmalıdır.',
            
            'oid.string' => 'OID geçerli bir metin olmalıdır.',
            'oid.max' => 'OID en fazla 55 karakter olmalıdır.',
            
            'company_id.exists' => 'Geçerli bir şirket seçmelisiniz.',
            'agent_id.exists' => 'Geçerli bir ajan seçmelisiniz.',
            'identity_no.digits' => 'Kimlik numarası en az 10 haneli olmalıdır.',
            'birth_day.date' => 'Doğum tarihi geçerli bir tarih olmalıdır.',
            'customer_company_id.exists' => 'Geçerli bir müşteri şirketi seçmelisiniz.',
            
            'gsm.string' => 'GSM numarası geçerli bir metin olmalıdır.',
            'gsm.max' => 'GSM numarası en fazla 55 karakter olmalıdır.',
            
            'fax.string' => 'Faks numarası geçerli bir metin olmalıdır.',
            'fax.max' => 'Faks numarası en fazla 55 karakter olmalıdır.',
            
            'email.email' => 'Geçerli bir e-posta adresi giriniz.',
            'email.unique' => 'Bu e-posta adresi zaten alınmış.',
            'email.max' => 'E-posta adresi en fazla 55 karakter olmalıdır.',
            
            'address.string' => 'Adres geçerli bir metin olmalıdır.',
            'neighborhood.string' => 'Mahalle adı geçerli bir metin olmalıdır.',
            'neighborhood.max' => 'Mahalle adı en fazla 255 karakter olmalıdır.',
            
            'city.string' => 'Şehir adı geçerli bir metin olmalıdır.',
            'city.max' => 'Şehir adı en fazla 55 karakter olmalıdır.',
            
            'country.string' => 'Ülke adı geçerli bir metin olmalıdır.',
            'country.max' => 'Ülke adı en fazla 55 karakter olmalıdır.',
            
            'can_receive_sms.boolean' => 'SMS alıp almayacağını belirtmelisiniz.',
            'can_receive_call.boolean' => 'Telefonla arama alıp almayacağını belirtmelisiniz.',
            
            'tax_no.string' => 'Vergi numarası geçerli bir metin olmalıdır.',
            'tax_no.max' => 'Vergi numarası en fazla 55 karakter olmalıdır.',
            
            'tax_office.string' => 'Vergi dairesi geçerli bir metin olmalıdır.',
            'tax_office.max' => 'Vergi dairesi en fazla 255 karakter olmalıdır.',
            
            'description.string' => 'Açıklama geçerli bir metin olmalıdır.',
            
            'status.in' => 'Durum sadece 0, 1 veya 2 olmalıdır.',
            
            'customer_group_id.exists' => 'Geçerli bir müşteri grubu seçmelisiniz.',
            'customer_criteria_id.exists' => 'Geçerli bir müşteri kriteri seçmelisiniz.',
            'customer_type_id.exists' => 'Geçerli bir müşteri tipi seçmelisiniz.',
            'dynamic_ivr_id.exists' => 'Geçerli bir dinamik IVR ID\'si seçmelisiniz.',
        ];
    }
}
