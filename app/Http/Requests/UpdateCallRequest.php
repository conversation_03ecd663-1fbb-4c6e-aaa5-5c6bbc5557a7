<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Enum\CallType;

class UpdateCallRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'caller_id'    => 'nullable|string|max:255',
            'hash'         => 'nullable|string|max:500',
            'duration'     => 'nullable|integer|min:0',
            'status'       => 'nullable|string|max:255',
            'type'         => ['nullable', 'string', Rule::in(['INCOMING', 'OUTGOING'])],
            'record_path'  => 'nullable|string|max:255',
            'dial_number'  => 'nullable|string|max:255',
            'company_id'   => 'nullable|exists:companies,id',
        ];
    }

    public function messages()
    {
        return [
            'caller_id.string'     => 'Arayan numara geçerli bir metin olmalıdır.',
            'caller_id.max'        => 'Arayan numara en fazla 255 karakter olabilir.',
            'hash.string'          => 'Hash geçerli bir metin olmalıdır.',
            'hash.max'             => 'Hash en fazla 500 karakter olabilir.',
            'duration.integer'     => 'Süre bir sayı olmalıdır.',
            'duration.min'         => 'Süre negatif olamaz.',
            'status.string'        => 'Durum geçerli bir metin olmalıdır.',
            'status.max'           => 'Durum en fazla 255 karakter olabilir.',
            'type.in'              => 'Geçerli bir çağrı türü belirtmelisiniz (INCOMING, OUTGOING).',
            'record_path.string'   => 'Kayıt yolu geçerli bir metin olmalıdır.',
            'record_path.max'      => 'Kayıt yolu en fazla 255 karakter olabilir.',
            'dial_number.string'   => 'Aranan numara geçerli bir metin olmalıdır.',
            'dial_number.max'      => 'Aranan numara en fazla 255 karakter olabilir.',
            'company_id.exists'    => 'Geçerli bir şirket seçmelisiniz.',
        ];
    }
}
