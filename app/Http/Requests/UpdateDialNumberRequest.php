<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDialNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dial_code' => 'required|string|max:10|unique:dial_numbers,dial_code,' . $this->route('dial_number'),
            'password' => 'nullable|string|max:255',
            'agent_password' => 'nullable|string|max:255',
            'domain_info' => 'nullable|string|max:255',
            'internal_description' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'display_number' => 'nullable|string|max:255',
            'group_name' => 'nullable|string|max:255',
            // 'status' => 'nullable|in:active,waiting,deactive',
            'internal_group' => 'nullable|string|max:255',
            'internal_username' => 'nullable|string|max:255',
            'codec' => 'nullable|string|max:255',
            'display_search' => 'nullable|string|max:255',
            'search_prefix_group' => 'nullable|string|max:255',
            'channel_limit' => 'nullable|string|max:255',
            'timeout_duration' => 'nullable|integer|min:0',
            'internal_call_allowed' => 'nullable|boolean',
            'external_call_allowed' => 'nullable|boolean',
            'receive_external_call_allowed' => 'nullable|boolean',
            'incoming_call_recording' => 'nullable|boolean',
            'dialable_from_ivr' => 'nullable|boolean',
            'call_waiting_disabled' => 'nullable|boolean',
            'show_name' => 'nullable|boolean',
            'do_not_disturb' => 'nullable|string|max:255',
            'work_schedule' => 'nullable|string|max:255',
            'all_calls' => 'nullable|string|max:255',
            'no_answer' => 'nullable|string|max:255',
            'if_busy' => 'nullable|string|max:255',
            'if_closed' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom validation error messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'dial_code.required' => 'Dahili numara zorunludur.',
            'dial_code.string' => 'Dahili numara geçerli bir metin olmalıdır.',
            'dial_code.max' => 'Dahili numara en fazla 10 karakter olabilir.',
            'dial_code.unique' => 'Bu dahili numara zaten kullanılmaktadır.',
            'password.string' => 'Parola geçerli bir metin olmalıdır.',
            'password.max' => 'Parola en fazla 255 karakter olabilir.',
            'agent_password.string' => 'Agent parolası geçerli bir metin olmalıdır.',
            'agent_password.max' => 'Agent parolası en fazla 255 karakter olabilir.',
            'domain_info.string' => 'Domain bilgisi geçerli bir metin olmalıdır.',
            'domain_info.max' => 'Domain bilgisi en fazla 255 karakter olabilir.',
            'internal_description.string' => 'Dahili açıklaması geçerli bir metin olmalıdır.',
            'internal_description.max' => 'Dahili açıklaması en fazla 255 karakter olabilir.',
            'email.email' => 'Geçerli bir e-posta adresi giriniz.',
            'email.max' => 'E-posta adresi en fazla 255 karakter olabilir.',
            'display_number.string' => 'Görünen numara geçerli bir metin olmalıdır.',
            'display_number.max' => 'Görünen numara en fazla 255 karakter olabilir.',
            'group_name.string' => 'Grup adı geçerli bir metin olmalıdır.',
            'group_name.max' => 'Grup adı en fazla 255 karakter olabilir.',
            'status.in' => 'Durum aktif, bekliyor veya deaktif olmalıdır.',
            'timeout_duration.integer' => 'Timeout süresi bir sayı olmalıdır.',
            'timeout_duration.min' => 'Timeout süresi negatif olamaz.',
            'internal_call_allowed.boolean' => 'Dahili arama izni geçerli bir değer olmalıdır.',
            'external_call_allowed.boolean' => 'Dışarı arama izni geçerli bir değer olmalıdır.',
            'receive_external_call_allowed.boolean' => 'Dışarıdan arama alma izni geçerli bir değer olmalıdır.',
            'incoming_call_recording.boolean' => 'Gelen çağrı kaydı geçerli bir değer olmalıdır.',
            'dialable_from_ivr.boolean' => 'IVR\'dan tuşlanabilir geçerli bir değer olmalıdır.',
            'call_waiting_disabled.boolean' => 'Arama bekletme kapalı geçerli bir değer olmalıdır.',
            'show_name.boolean' => 'İsim göster geçerli bir değer olmalıdır.',
        ];
    }
}
