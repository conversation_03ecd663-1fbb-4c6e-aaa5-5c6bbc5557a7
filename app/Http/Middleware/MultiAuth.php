<?php

namespace App\Http\Middleware;

use Closure;

class MultiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next, ...$guards)
    {

       if (auth('web')->check()) {
            auth()->shouldUse('web');
            return $next($request);
        }
        if (auth('api')->check()) {
            auth()->shouldUse('api');
            return $next($request);
        }
        $token = $request->header('X-Api-Token');
        if ($token && $token === env('SIP_API_TOKEN')) {
            return $next($request);
        }

        return response()->json(['message' => 'Unauthorized'], 401);
    }
}
