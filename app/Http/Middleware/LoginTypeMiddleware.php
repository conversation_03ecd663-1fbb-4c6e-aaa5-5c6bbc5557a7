<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class LoginTypeMiddleware
{
    public function handle(Request $request, Closure $next, $type = null)
    {
        // $type parametresi route'dan al<PERSON> (admin veya agent)
        if ($type) {
            // İsteğe özel bir attribute ekle
            $request->attributes->set('login_type', $type);
        }
        return $next($request);
    }
}
