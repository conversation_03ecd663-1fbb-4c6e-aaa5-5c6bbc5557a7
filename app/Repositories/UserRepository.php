<?php

namespace App\Repositories;

use App\Models\Call;
use App\Models\CustomerCriteria;
use App\Models\User;

class UserRepository extends ModelRepository
{
    public function __construct()
    {
        parent::__construct(new User());
    }
    
    public function getByDialId($dialCode)
    {
        return $this->model->whereHas('dialNumber', function ($query) use ($dialCode) {
            $query->where('dial_code', $dialCode);
        })->first();
    }
}