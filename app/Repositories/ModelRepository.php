<?php

namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;

class ModelRepository
{
    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    // Tüm verileri al
    public function getAll()
    {
        return $this->model::all();
    }

    // Yeni bir veri oluştur
    public function create(array $data)
    {
        return $this->model::create($data);
    }

    // Veriyi güncelle
    public function update($id, array $data)
    {
        $model = $this->model::findOrFail($id);
        $model->update($data);
        return $model;
    }

    // Veriyi sil
    public function delete($id)
    {
        $model = $this->model::findOrFail($id);
        $model->delete();
        return $model;
    }

    // Tek bir veriyi al
    public function find($id)
    {
        return $this->model::findOrFail($id);
    }
}
