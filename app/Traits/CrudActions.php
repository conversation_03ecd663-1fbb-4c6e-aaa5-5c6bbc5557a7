<?php

namespace App\Traits;

use Illuminate\Http\Request;

trait CrudActions
{
    protected $service;

    abstract protected function getStoreRequestClass(): string;
    abstract protected function getUpdateRequestClass(): string;

    protected function getResourceName(): string
    {   
        // Default resource name, can be overridden in child classes
        return 'kayıt';
    }

    public function index()
    {
        return response()->json($this->service->getAll());
    }

    public function show($id)
    {
        return response()->json($this->service->find($id));
    }

    public function store(Request $request)
    {
        $validated = app($this->getStoreRequestClass())->validated();
        $resource = $this->service->create($validated);

        return response()->json([
            'message' => $this->getResourceName() . ' başarıyla oluşturuldu.',
            'data' => $resource,
        ], 201);
    }

    public function update(Request $request, $id)
    {
        $validated = app($this->getUpdateRequestClass())->validated();
        $resource = $this->service->update($id, $validated);

        return response()->json([
            'message' => $this->getResourceName() . ' başarıyla güncellendi.',
            'data' => $resource,
        ]);
    }

    public function destroy($id)
    {
        $this->service->delete($id);

        return response()->json([
            'message' => $this->getResourceName() . ' başarıyla silindi.',
        ]);
    }

    public function destroyMultiple()
    {
        $ids = request()->input('ids', []);

        if (empty($ids)) {
            return response()->json([
                'message' => 'Silinecek ' . $this->getResourceName() . ' seçilmedi.',
            ], 400);
        }

        foreach ($ids as $id) {
            $this->service->delete($id);
        }

        return response()->json([
            'message' => count($ids) . ' ' . $this->getResourceName() . ' başarıyla silindi.',
        ]);
    }
}
