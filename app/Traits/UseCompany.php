<?php

namespace App\Traits;

use App\Scopes\CompanyOwnedScope;
use App\Scopes\CompanyScope;

trait UseCompany
{
    public static function bootUseCompany()
    {
        // Global scope: sadece kendi şirket verileri
        static::addGlobalScope(new CompanyScope);

        // Otomatik olarak company_id setle
        static::creating(function ($model) {
            if (auth()->check() && empty($model->company_id)) {
                $model->company_id = auth()->user()->company_id;
            }
        });
    }
}
