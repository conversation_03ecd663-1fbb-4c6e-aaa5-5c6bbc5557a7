.queue-list-section .route-links a,
.queue-list-section .route-links span,
.waiting-music-section .route-links a,
.waiting-music-section .route-links span,
.anons-recording-section .route-links a,
.anons-recording-section .route-links span
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}


#queueModalTable2_wrapper .dt-layout-row:first-child,
#queueModalTable1_wrapper .dt-layout-row:first-child {
  display: none !important;
}

/* Play button styles */
.play-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.play-button .iconify {
  font-size: 28px;
  color: #000;
}

.play-button.playing .iconify {
  color: #4B67C2;
}

.audio-player-container {
    display: flex;
    align-items: center;
    gap: 10px;
}
.audio-progress-container {
    width: 100%;
    height: 4px;
    background-color: #E4E7EB;
    border-radius: 3px;
    overflow: hidden;
}
.audio-progress-bar {
    width: 0%;
    height: 100%;
    background-color: #4B67C2;
    border-radius: 3px;
    transition: width 0.1s linear;
}

/* Table header width styles for anons recording table */
#anonsRecordingTable th:nth-child(2) {  /* ID header */
  width: 5% !important;
}

#anonsRecordingTable th:last-child {  /* Last header (Dinle) */
  width: 25% !important;
}

