@media (max-width: 991px) {
  .internal-choose {
    row-gap: 1rem;
  }
}

.planned-calls-section .route-links a,
.planned-calls-section .route-links span,
.calls-section .route-links a,
.calls-section .route-links span,
.agent-status-section .route-links a,
.agent-status-section .route-links span,
.customer-list-section .route-links a,
.customer-list-section .route-links span 
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

.calls-section .tab-content {
  max-height: 100% !important;
}

.customer-list-filter .modal-body, .work-stats-filter .modal-body {
  overflow-y: auto;
  padding-inline: 40px;
  padding-bottom: 40px;
}

#agentStatusTable th:nth-child(1) {
  width: 10%;
}

#agentStatusTable td:nth-child(1),
#customerListTable td:nth-child(1) 

{
  text-align: left !important;
}

#agentStatusTable .status-badge {
  width: 50%;
}

#customerListTable th:nth-child(1) {
  width: 5%;
}


.table.customer-list-info {
    border: 1px solid #F3F4F6 !important;
    border-radius: 12px !important;
    overflow: hidden;
}

.table.customer-list-info th {
    background: #F7F9FC;
    font-size: 12px;
    font-weight: 600;
    color: #444444;
    padding: 20px;
    vertical-align: middle;
    border-right: none;
    border-top: none;
    text-align: left !important;
    border-bottom: 1px solid #F3F4F6 !important;
}

.table.customer-list-info td {
    background: #fff;
    font-size: 12px;
    font-weight: 500;
    color: #444444;
    padding: 20px;
    vertical-align: middle;
    border-left: none;
    border-top: none;
    border-bottom: 1px solid #F3F4F6 !important;
}

.table.customer-list-info tr:last-child th,
.table.customer-list-info tr:last-child td {
    border-bottom: none !important;
}

.table.customer-list-info tbody tr {
    border-bottom: 1px solid #F3F4F6 !important;
}

.calls-filter .modal-body {
  overflow-y: auto;
  padding-inline: 40px;
  padding-bottom: 40px;
}

/* Audio Player Styles */
.audio-player-container {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 150px;
}

.play-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.play-button:hover {
    transform: scale(1.1);
}

.play-button .iconify {
    font-size: 24px;
    color: #4B67C2;
}

.play-button.playing .iconify {
    color: #E74C3C;
}

.audio-progress-container {
    flex: 1;
    height: 4px;
    background-color: #E4E7EB;
    border-radius: 3px;
    overflow: hidden;
    min-width: 60px;
    cursor: pointer;
}

.audio-progress-bar {
    height: 100%;
    background-color: #4B67C2;
    width: 0%;
    transition: width 0.1s ease;
}

.audio-duration {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    min-width: 35px;
    text-align: right;
}
