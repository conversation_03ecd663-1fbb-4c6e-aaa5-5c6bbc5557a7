.ivr-list-section .route-links a,
.ivr-list-section .route-links span,
.dynamic-ivr-list-section .route-links a,
.dynamic-ivr-list-section .route-links span,
.score-list-section .route-links a,
.score-list-section .route-links span,
.ivr-record-section .route-links a,
.ivr-record-section .route-links span
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

#ivrRecordingTable th:nth-child(2) {
  width: 7%;
}

/* Play button styles */
.play-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.play-button .iconify {
  font-size: 28px;
  color: #000;
}

.play-button.playing .iconify {
  color: #4B67C2;
}

.audio-player-container {
    display: flex;
    align-items: center;
    gap: 10px;
}
.audio-progress-container {
    width: 100%;
    height: 4px;
    background-color: #E4E7EB;
    border-radius: 3px;
    overflow: hidden;
    min-width: 100px;
}
.audio-progress-bar {
    width: 0%;
    height: 100%;
    background-color: #4B67C2;
    border-radius: 3px;
    transition: width 0.1s linear;
}