@font-face {
  font-family: 'Open Sans';
  src: url('/fonts/OpenSans-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Open Sans';
  src: url('/fonts/OpenSans-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Open Sans';
  src: url('/fonts/OpenSans-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Open Sans';
  src: url('/fonts/OpenSans-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

:root {
  --sans: 'Open Sans', sans-serif;

  --black: #444444;
  --gray: #94A3B8;
  --red: #E74C3C;
  --blue: #5E81F4;
  --blue2: #4B67C2;
  --green: #30CB83;
  --darkblue: #1A1F36;
  --yellow: #D4A756;
  --orange: #E3A600;
  
}

@media (max-width: 768px) {
  .internal-choose input {
    margin-right: 0 !important;
  }
  
    div.dt-container div.dt-layout-row div.dt-layout-cell.dt-layout-end {
        margin-left: 0 !important;
        margin-top: 0 !important;
  }

}

   
@media (max-width: 991px) {


  .action-buttons {
    display: none !important;
  }
  
  .responsive-menu {
    display: flex !important;
    position: relative;
  }

  .responsive-menu .iconify {
    flex-shrink: 0;
  }
  
  .menu-toggle {
    background-color: #FCFCFB;
    border: 1px solid #F3F4F6;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
  }

  .mobile-visible {
    display: flex !important;
  }
  
  .menu-toggle .iconify {
    font-size: 24px;
    color: #4B67C2;
  }
  
  .dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    width: 160px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    z-index: 1000;
    display: none;
  }
  
  .dropdown-menu.show {
    display: block;
  }
  
  .dropdown-menu-item,
  .internal-add-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .dropdown-menu-item:hover:not(.disabled),
  .internal-add-menu-item:hover:not(.disabled) {
    background-color: #f5f5f5;
  }
  
  .dropdown-menu-item .iconify,
  .internal-add-menu-item .iconify {
    margin-right: 10px;
    font-size: 18px;
  }
  
  .dropdown-menu-item span.text,
  .internal-add-menu-item span.text {
    font-size: 14px;
    color: #333;
  }
  
  .dropdown-menu-item.disabled,
  .internal-add-menu-item.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
  }


  .responsive-menu .dropdown-menu .dropdown-menu-item .iconify {
    font-size: 30px;
    padding: 6px !important;
    color: white !important;
  }  


        #addModal .modal-dialog {
            width: 95%;
            margin: 0.5rem auto;
        }
        
        /* Horizontal scrollable tabs on mobile */
        .nav-tabs {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            overflow-y: hidden;
            -webkit-overflow-scrolling: touch;
            white-space: nowrap;
        }

        .nav-tabs .nav-item {
            float: none;
            flex-shrink: 0;
        }

         .modal-body {
            max-height: 60vh !important;
         }

         div.dt-container div.dt-layout-row:not(.dt-layout-table) {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
         }

         div.dt-container div.dt-layout-row div.dt-layout-cell.dt-layout-start {
            margin-right: 0 !important;
        }

        div.dt-container div.dt-layout-row div.dt-layout-cell.dt-layout-end {
            margin-left: 0 !important;
            margin-top: 1rem;
        }

        .dt-layout-cell .dt-info {
            padding-left: 0 !important;
        }

        .dt-paging {
            padding-right: 0 !important;
        }
    }




@media (max-width: 2400px) {
  .dt-layout-table {
    overflow-x: auto !important;
    width: 100%;
    display: block !important;
  }
  .dt-layout-table > table {
    min-width: 1399px;
    width: max-content;
    display: table;
  }

  .custom-data-table-wrapper {
    overflow-x: auto !important;
    width: 100%;
  }

}


  
body, html {
    font-family: var(--sans);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    height: 100%;
    background-color: #FCFCFB;
    width: 100%;
}

.white-container{
  background-color: #FFFFFF;
  padding: 2.5rem;
  border-radius: 0.75rem;
  border: 1px solid #F3F4F6;
}

.md-text {
  font-family: var(--sans);
  font-size: 1.5rem;
}

.lg-text {
  font-family: var(--sans);
  font-size: 1.8rem; 
}

.xl-text {
  font-family: var(--sans);
  font-size: 24px; 
}

.sm-text {
  font-family: var(--sans);
  font-size: 1.2rem;
}

.xs-text {
  font-family: var(--sans);
  font-size: 0.875rem;
}

.blue-btn{
  background-color: var(--blue2);
  color: #FFFFFF;
  border-radius: 0.375rem;
  padding: 0.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--sans);
  font-weight: 600;
  border: 1px solid var(--blue2);
  font-size: 1rem;
  text-decoration: none;
}

.white-btn{
  background-color: #FFFFFF;
  color: var(--darkblue);
  border-radius: 0.375rem;
  padding: 0.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: var(--sans);
  font-weight: 600;
  border: 1px solid var(--darkblue);
  font-size: 1rem;
}

.red-btn{
  background-color: var(--red);
  color: #FFFFFF;
  border-radius: 0.375rem;
  padding: 0.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--sans);
  font-weight: 600;
  border: 1px solid var(--red);
  font-size: 1rem;
  text-decoration: none;
}

.red-btn:hover{
  background-color: #d32f2f;
  border-color: #d32f2f;
  color: #FFFFFF;
}


.disabled-btn {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.custom-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1.25rem;
  margin-bottom: 0;
}

.table-title{
  font-weight: 600;
  color: var(--black);
  font-size: 1.125rem;
  margin-bottom: 0;
}

.custom-hr {
  border-top: 1px solid #F3F4F6;
  opacity: 1;
}

.edit-btn {
  background-color: #F4BE5E;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #D4A756;
  font-size: 1rem;
  cursor: pointer;
}

.edit-btn span, 
.delete-btn span, 
.add-new-btn span, 
.dark-green-btn span, 
.callback-btn span, 
.stop-call-btn span, 
.online-listen-btn span, 
.play-btn span, 
.pause-btn span, 
.upload-data-btn span {
  font-size: 0.75rem;
  font-weight: 400;
  white-space: nowrap;
  color: white;
  margin-left: 0.3rem;
}

.delete-btn {
  background-color: #E74C3C;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #D24A3C;
  font-size: 18px;
}



.add-new-btn {
  background-color: #368359;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #30CB83;
  font-size: 18px;
}

.btn-close {
  box-shadow: none !important;
}

/* DataTable özel stilleri */
.custom-data-table thead {
  background-color: #F7F9FC;
}
.custom-data-table thead th {
  color: #94A3B8;
  font-size: 12px;
  font-weight: 600;
  text-align: left !important;
  white-space: nowrap;
}
.custom-data-table tbody td,
.dt-layout-cell .dt-info
{
  color: #444444;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}

.dt-layout-cell .dt-info {
  padding-left: 1.5rem;
}


div.dt-container .dt-paging .dt-paging-button.current
{
  background: var(--black) !important;
  padding: 0.5rem 1rem !important;
  font-size: 12px;
}

div.dt-container .dt-paging .dt-paging-button
{
  background: white !important;
  color: var(--black) !important;
  padding: 0.5rem 1rem !important;
  font-size: 12px;
  border: 1px solid #F3F4F6 !important;
}


div.dt-container .dt-paging .dt-paging-button.disabled {
  border: 1px solid #F3F4F6 !important;
  padding: 0.5rem 1rem !important;
  background-color: #F3F4F6 !important;
  font-size: 12px;
}

div.dt-container .dt-paging .dt-paging-button.disabled:hover {
  background-color: #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button:not(.disabled):hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

.dt-paging {
  padding-right: 1.5rem;
}

.status-badge {
  display: inline-block;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 12px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  margin-right: 4px;
  margin-bottom: 2px;
}
.status-badge.active {
  color: #30CB83;
  background: #30CB8340;
}
.status-badge.waiting {
  color: #9B59B6;
  background: #9B59B640;
}
.status-badge.free {
  background-color: #ABABAB40;
  color: #444444;
}

.status-badge.deactive {
  color: #E74C3C;
  background: #E74C3C40;
}

.status-badge.all-calls {
  color: #F4BE5E;
  background: #F4BE5E26
}

th.no-sort.sorting:before,
th.no-sort.sorting:after,
th.no-sort.sorting_asc:before,
th.no-sort.sorting_asc:after,
th.no-sort.sorting_desc:before,
th.no-sort.sorting_desc:after,
th.no-sort .dt-column-order {
  display: none !important;
}

div.dt-container .dt-length select, 
div.dt-container .dt-length .dt-select {
  border: 1px solid #F3F4F6 !important;
  color: #444444 !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  border-radius: 6px;
  padding: 0 4px 0 4px;
  background: #fff;
  outline: none;
  min-width: 60px;
  height: 32px;
  box-shadow: none;
}

/* DataTable body içindeki tüm yazılar sola hizalı */
table.dataTable tbody td,
table.dataTable tbody th,
table.dataTable tbody td.sorting_1 {
  text-align: left !important;
}


/* Yalnızca dikey (vertical) çizgiler, yatay çizgiler olmadan */
table.dataTable,
table.dataTable thead th,
table.dataTable tbody td {
  border-top: none !important;
  border-bottom: none !important;
}
table.dataTable thead th,
table.dataTable tbody td {
  border-right: 1px solid #94A3B840 !important;
}
table.dataTable thead th:last-child,
table.dataTable tbody td:last-child {
  border-right: none !important;
}

.custom-checkbox {
  vertical-align: middle;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}
.custom-checkbox .custom-check {
  width: 16px;
  flex-shrink: 0;
  height: 16px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 0 2px 0 #94A3B840;
  border: 1.5px solid #94A3B8;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, border 0.2s;
}
.custom-checkbox input[type="checkbox"]:checked + .custom-check {
  background: #4B67C2;
  border-color: #4B67C2;
}
.custom-checkbox .custom-check .iconify {
  display: none;
  color: #fff;
  font-size: 16px;
}
.custom-checkbox input[type="checkbox"]:checked + .custom-check .iconify {
  display: block;
}

.custom-data-table tbody tr.odd,
.custom-data-table tbody tr:nth-child(odd) {
  background-color: #F7F9FC !important;
  background: #F7F9FC !important;
}
.custom-data-table tbody tr.even,
.custom-data-table tbody tr:nth-child(even) {
  background-color: white !important;
  background: white !important;
}

/* Header hover'da border veya arka plan olmasın */
.custom-data-table thead th:hover,
.custom-data-table thead th:focus {
  background: none !important;
   border-right: 1px solid #94A3B840 !important;
  box-shadow: none !important;
  outline: none !important;
}

.dark-green-btn {
  background-color: #008C3F;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #016930;
  color: white;
  font-size: 1rem;
}


  
    /* Modal Styles */
     .modal-dialog {
        border-radius: 12px;
    }
    
     .modal-content {
        border-radius: 8px;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
     .modal-header {
        border-bottom: none;
        padding: 20px 40px 20px 40px;
        background-color: #FFFFFF;
    }
    
     .modal-title {
        font-weight: 700;
        color: #444444;
        font-size: 20px;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #F3F4F6;
        margin-inline: 20px;
    }
    
     .nav-tabs .nav-link {
        color: #94A3B8;
        font-weight: 400;
        border: none;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;
    }
    
     .nav-tabs .nav-link.active {
        color: #444444;
        font-weight: 600;
        border-bottom: 2px solid #4B67C2;
        background-color: transparent;
    }
    
     .nav-tabs .nav-link:hover:not(.active) {
        border-color: transparent;
        color: #4B67C2;
    }
    
     .modal-body {
        padding: 0;

    }
    
    .modal-footer {
        border-top: 1px solid #F3F4F6;
    }
    
   .tab-content {
        padding: 1rem 0;
        background-color: #FCFCFB;
        padding: 20px 40px;
        max-height: 450px;
        overflow-y: auto;
    }
    
    /* Tab content text size */
    .tab-pane {
        font-size: 16px;
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #F3F4F6;
    }
    
    /* Form controls */
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #444444;
    }

  .generate-password-btn {
      background-color: #30CB8340;
      color: #30CB83;
      border-radius: 6px;
      border: 0.5px solid #30CB83;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.375rem 0.75rem;
      margin-left: 10px;
      white-space: nowrap;
  }

  .generate-password-btn:hover {
      background-color: #30CB8350;
      color: #30CB83;
  }

.form-check-label {
    font-weight: 500;
    color: #444444;
    font-size: 0.875rem;
}

/* Filter button styles */
  .filter-icon-btn {
    width: 45px;
    height: 32px;
    background-color: white;
    border: 1px solid #4B67C2;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  
  .filter-icon-btn .iconify {
    color: #4B67C2;
    font-size: 20px;
  }
  
  #filterButton {
    height: 38px;
  }

   
  .dropdown-menu-item.disabled {
        opacity: 0.5;
        pointer-events: none;
        cursor: default;
    }

    .dropdown-menu-item.disabled-menu-item {
        opacity: 0.5;
        pointer-events: none;
        cursor: default;
    }

    .download-btn {
        background-color: #7461AA;
        color: #FFFFFF;
        border-radius: 6px;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.5px solid #645490;
        font-size: 1rem;
    }


.download-btn span {
  font-weight: 400;
  font-size: 0.75rem;
}

.prefix-btn {
  background-color: #008C3F33;
  border-radius: 6px;
  font-weight: 400;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #008C3F;
  font-size: 0.75rem;
  font-weight: 400;
  white-space: nowrap;
  color: #008C3F;
}

.prefix-btn span {
  font-size: 0.75rem;
  font-weight: 400;
  white-space: nowrap;
  color: #008C3F;

}

/* Disabled Menu Item */
.disabled-menu-item {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  color: #94A3B8 !important;
}

.disabled-menu-item .iconify {
  color: #94A3B8 !important;
}

.disabled-menu-item .text {
  color: #94A3B8 !important;
}

.stop-call-btn {
  background-color: #9C2828;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #772020;
  font-size: 0.75rem;
}

.online-listen-btn {
  background-color: #7CE7AC;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #68BE8F;
  font-size: 0.75rem;
}


input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url('https://api.iconify.design/hugeicons:calendar-03.svg?color=%2394A3B8') no-repeat;
    width: 14px;
    height: 14px;
    cursor: pointer;
    background-size: contain;
    border-width: thin;
}

input[type="time"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url('https://api.iconify.design/hugeicons:time-04.svg') no-repeat;
    width: 14px;
    height: 14px;
    cursor: pointer;
    background-size: contain;
    border-width: thin;
}

.play-btn, .pause-btn {
      background-color: #ABABAB;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0.5px solid #8C8C8C;
      color: white;
      font-size: 18px;
}

.upload-data-btn {
      background-color: #6149A5;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0.5px solid #4E4272;
      color: white;
      font-size: 18px;
}


.custom-data-table-wrapper th:first-child,
.custom-data-table-wrapper td.sorting_1:first-child {
  text-align: center !important;
}

.custom-data-table-wrapper th.no-sort,
.custom-data-table-wrapper td.no-sort {
  text-align: center !important;
  width: 50px !important;
  min-width: 50px !important;
  max-width: 100px !important;
}

.custom-table-footer-cell {
  margin-top: 24px !important;
  border: 0.5px solid #94A3B840 !important;
  color: #1C1D21 !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-align: left !important;
  background: #fff;
}
tfoot {
  margin-top: 12px;
}
tfoot tr {
  border: none;
}

.callback-btn {
  background-color: #008C3F;
  color: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid #30CB83;
  font-size: 0.75rem;
}