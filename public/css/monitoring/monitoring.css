@media (max-width: 1200px) {
    .mobile-call {
        flex-wrap: wrap;
    }
    .call-card {
        flex: 1 1 45%;
        min-width: 220px;
        margin-bottom: 1rem;
        height: 140px;
        border-radius: 0 !important;
    }
    .call-card:last-child {
        border-bottom-left-radius: 12px !important;
        border-bottom-right-radius: 12px !important;
    }
}

@media (max-width: 991px) {
    .mobile-call {
        flex-direction: column;
        gap: 1rem;
    }
    .call-card {
        width: 100%;
        min-width: 0;
        height: 120px;
        border-radius: 0 !important;
    }
    .call-card:first-child {
        border-top-left-radius: 12px !important;
        border-top-right-radius: 12px !important;
    }
    .call-card:last-child {
        border-bottom-left-radius: 12px !important;
        border-bottom-right-radius: 12px !important;
    }
}



.call-card{
    background-color: #FCFCFB;
    border: 1px solid #F3F4F6;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
    flex-wrap: wrap;
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
    height: 140px;
    box-sizing: border-box;
}

.call-card h3 {
    font-weight: 600;
    font-size: 1.125rem;
}

.call-card h2 {
    font-weight: 600;
    font-size: 1.125rem;
    color: #444444;
    text-align: center;
}

.call-card.call-in h3{
    color: #30CB83;
}

.call-card.call-out h3{
    color: #E74C3C;
}

.call-card.internal h3{
    color: white;
}

.call-card.connected h3{
    color: #4B67C2;
}

.call-card.waiting h3{
    color: #E3A600;
}

.call-progress {
    width: 100%;
    background: #F3F4F6;
    height: 4px;
    border-radius: 3px;
    margin-top: 12px;
    overflow: hidden;
}

.call-progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 0.4s cubic-bezier(.4,0,.2,1);
}

.call-progress-in {
    background: #30CB83;
}

.call-progress-out {
    background: #E74C3C;
}

.d-flex.align-items-center.justify-content-between {
    display: flex;
    gap: 1rem;
}

/* Varsayılan: yan yana (desktop) */
.call-card:first-child {
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
}
.call-card:last-child {
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
}

.monitoring-section .internal-card{
    border: 1px solid #F3F4F6;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.monitoring-section .icon-card{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #30CB83;
    background-color: #FCFCFB;
    padding: 10px;
    border-radius: 10px;
    margin-right: 8px;
}

.monitoring-section .icon-card .iconify {
    color: #30CB83;
    font-size: 30px;
}

.internal-card h6{
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.1rem;
}

.internal-card span{
    font-weight: 500;
    color: #94A3B8;
    font-size: 14px;
}

.call-info .iconify{
    color: #30CB83;
    font-size: 15px;
}

.call-info small{
    font-size: 14px;
    color: #94A3B8;
    font-weight: 400;
    margin-left: 6px;
}