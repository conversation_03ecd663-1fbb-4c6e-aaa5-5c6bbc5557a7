@media (max-width: 768px){
    .dashboard-header{
        flex-direction: column !important;
        row-gap: 1rem;
    }
}


@media (max-width: 1200px) {
    .mobile-call {
        flex-wrap: wrap;
    }
}

@media (max-width: 1200px) {
    .mobile-call {
        flex-direction: column;
        gap: 1rem;
    }
    .agent-card {
        width: 100%;
        min-width: 0;
    }
}


.dashboard-section .route-links a
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}

.agent-card{
    background-color: #FCFCFB;
    border: 1px solid #F3F4F6;
    padding: 10px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    flex-wrap: wrap;
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
    height: 70px;
    box-sizing: border-box;
}

.agent-card span{
    font-size: 12px;
    font-weight: 400;
    color: var(--black);
}

.agent-card b{
    font-weight: 700;
}

.agent-card b.online{
    color: #30CB83;
}

.agent-card b.offline{
    color: #E74C3C;
}

.agent-card.break{
    color: #9B59B6;
}

.agent-card.busy{
    color: #444444;
}

.agent-card.free{
    color: #E3A600;
}

.queue-card{
    border: 1px solid #F3F4F6;
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex-grow: 1;
    flex-wrap: wrap;
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
}

.queue-card span{
    font-weight: 600;
    color: var(--black);
    font-size: 1rem;
}

.queue-card .progress{
    width: 100%;
    height: 20px;
    background: #FCFCFB;
    margin-top: 12px;
    border: 1px solid #F3F4F6;
    border-radius: 0;
}

.queue-bar{
    width: 100%;
    background: var(--gray);
    height: 20px;
    border-radius: 0;
}

.queue-footer h6{
    font-weight: 500;
    color: #4B67C2;
    font-size: 1rem;
}

.dashboard-section .internal-card{
    border: 1px solid #F3F4F6;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.dashboard-section .icon-card{
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #30CB83;
    background-color: #FCFCFB;
    padding: 10px;
    border-radius: 10px;
    margin-right: 8px;
}

.dashboard-section .icon-card .iconify {
    color: #30CB83;
    font-size: 30px;
}

.internal-card h6{
    font-size: 1rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.1rem;
}

.internal-card span{
    font-weight: 500;
    color: #94A3B8;
    font-size: 0.875rem;
}

.call-info .iconify{
    color: #30CB83;
    font-size: 15px;
}

.call-info small{
    font-size: 0.875rem;
    color: #94A3B8;
    font-weight: 400;
    margin-left: 6px;
}