.customer-section .route-links a,
.customer-section .route-links span,
.customer-groups-section .route-links a,
.customer-groups-section .route-links span,
.customer-criteria-section .route-links a,
.customer-criteria-section .route-links span,
.customer-types-section .route-links a,
.customer-types-section .route-links span
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

.customer-section .tab-content {
  max-height: 100% !important;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: rgba(0, 0, 0, 0);
    opacity: 1;
    display: block;
    background: url('https://api.iconify.design/hugeicons:calendar-03.svg?color=%2394A3B8') no-repeat;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background-size: contain;
    border-width: thin;
}

.customer-list-filter {
  max-height: 100% !important;
}


.customer-list-filter .modal-body {
  overflow-y: auto;
  padding-inline: 40px;
  padding-bottom: 40px;
}