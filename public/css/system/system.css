@media (max-width: 768px) {
  .internal-choose {
    display: flex;
    flex-direction: column;
  }

  .internal-choose .form-select,
  .internal-choose .blue-btn {
    width: 100% !important;
  }
}


.call-status-section .route-links a,
.call-status-section .route-links span,
.internal-status-section .route-links a,
.internal-status-section .route-links span,
.line-status-section .route-links a,
.line-status-section .route-links span
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

.calls-filter .modal-body {
  overflow-y: auto;
  padding-inline: 40px;
}
