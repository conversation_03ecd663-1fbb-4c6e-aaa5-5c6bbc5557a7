
@media (max-width: 576px) {
  #key-settings .row.g-2.mb-3 .col-4 {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    display: block !important;
  }
}


@media(max-width: 991px) {
  #time-settings td:nth-child(2),
  #time-settings th:nth-child(2)
  {
    max-width: 100px !important;
  }

    .responsive-menu .mobile-upload-dropdown {
    background-color: #6149A5;
    border-radius: 6px;
    border: 0.5px solid #4E4272;
    min-width: 180px;
    padding: 0;
    margin-top: 6px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    width: 100%;
    left: 0;
    top: 100%;
  }
  .responsive-menu .mobile-upload-dropdown .dropdown-item {
    color: #fff;
    background-color: #6149A5;
    border: none;
    border-bottom: 1px solid #4E4272;
    font-size: 15px;
    padding: 10px 18px;
    transition: none;
    cursor: pointer;
  }
  .responsive-menu .mobile-upload-dropdown .dropdown-item:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }
  .responsive-menu .mobile-upload-dropdown .dropdown-item:last-child {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom: none;
  }
  .responsive-menu .mobile-upload-dropdown .dropdown-item:focus,
  .responsive-menu .mobile-upload-dropdown .dropdown-item:active,
  .responsive-menu .mobile-upload-dropdown .dropdown-item:hover {
    background-color: #6149A5;
    color: #fff;
  }
}

.planned-calls-section .route-links a,
.planned-calls-section .route-links span,
.call-result-types-section .route-links a,
.call-result-types-section .route-links span,
.missed-calls-section .route-links a,
.missed-calls-section .route-links span,
.callback-requests-section .route-links a,
.callback-requests-section .route-links span,
.auto-call-section .route-links a,
.auto-call-section .route-links span,
.meet-conf-section .route-links a,
.meet-conf-section .route-links span 
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

.planned-calls-filter .modal-body,
.auto-call-filter .modal-body
{
  overflow-y: auto;
  padding-inline: 40px;
  padding-bottom: 40px;
}

.missed-calls-filter .modal-body {
  overflow-y: auto;
  padding-inline: 40px;
}

.upload-data-btn.dropdown-toggle {
  background-color: #6149A5;
  color: #fff;
  border-radius: 6px;
  border: 0.5px solid #4E4272;
  font-size: 18px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-data-btn.dropdown-toggle:focus,
.upload-data-btn.dropdown-toggle:active {
  background-color: #6149A5;
  color: #fff;
  border-color: #4E4272;
  box-shadow: none;
}

.upload-data-btn.dropdown-toggle:hover {
  background-color: #6149A5;
  color: #fff;
  border-color: #4E4272;
}

.upload-data-btn + .dropdown-menu {
  background-color: #6149A5;
  border-radius: 6px;
  border: 0.5px solid #4E4272;
  min-width: 180px;
  padding: 0;
  margin-top: 6px !important;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.upload-data-btn + .dropdown-menu .dropdown-item {
  color: #fff;
  background-color: #6149A5;
  border: none;
  border-bottom: 1px solid #4E4272;
  font-size: 15px;
  padding: 10px 18px;
  transition: none;
  cursor: pointer;
}

.upload-data-btn + .dropdown-menu .dropdown-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.upload-data-btn + .dropdown-menu .dropdown-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  border-bottom: none;
}

.upload-data-btn + .dropdown-menu .dropdown-item:focus,
.upload-data-btn + .dropdown-menu .dropdown-item:active {
  background-color: #6149A5;
  color: #fff;
}

.upload-data-btn + .dropdown-menu .dropdown-item:hover {
  background-color: #6149A5;
  color: #fff;
}

#time-settings td:nth-child(2) {
 text-align: center !important;
}

.time-settings-table td,
.time-settings-table th {
  vertical-align: middle;
}

#time-settings th:nth-child(2),
#time-settings td:nth-child(2) {
  width: fit-content;
  min-width: 10px;
  max-width: 32px;
}



.icon-toggle-btn {
    background: #FCFCFB;
    border: 1px solid transparent;
    border-radius: 8px;
    padding-block: 6px;
    font-size: 12px;
    margin-left: 4px;
    transition: border 0.2s;
    display: flex;
    align-items: center;
    box-shadow: none !important;
}
.icon-toggle-btn.active {
    border-color: #4B67C2;
}
.icon-toggle-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px #4B67C233;
}
