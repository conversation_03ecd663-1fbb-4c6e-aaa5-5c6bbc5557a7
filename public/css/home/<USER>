@media (max-width: 768px) {
    .mobile-break {
        flex-direction: column;
        gap: 1rem;
    }

    .ann-table-wrapper {
        overflow-x: auto;
    }
    .ann-table {
        min-width: 768px;
    }

    .home-section .stats-header-mobile {
        flex-direction: column !important;
        align-items: flex-start !important;
    }

    .home-section #callStatistics {
        margin-left: 1.5rem !important;
        margin-bottom: 1rem;
    }

    #chartLegendContainer {
        right: -200px !important;
    }

    .admin-dropdown-menu {
        left: 0;
        right: 0;
        min-width: 100%;
        transform: none;
    }
}


@media (max-width: 1199.98px) {


  .desktop-legend {
    justify-content: center !important;
    gap: 1rem;
  }
}

@media (max-width: 1399px){
      .navbar-light {
        overflow-x: auto;
    }

    .navbar-brand {
        min-width: 1399px;
    }
    .admin-nav-links {
        overflow-x: auto;
        overflow-y: hidden !important; /* Dikey scroll'u kesinlikle engelle */
        position: relative;
        z-index: 2;
    }
    .admin-dropdown-menu {
        z-index: 9999;
        position: absolute;
        left: 0;
        right: 0;
        min-width: 100%;
        max-width: none;
        top: 100%;
    }
}


.custom-header h5 {
    font-weight: 600;
    color: var(--black);
}

.custom-header .status-pill {
    background-color: #30CB8340;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 2rem;
    color: #30CB83;
}


.form-select,
.form-control {
    border: 1px solid #F3F4F6 !important;
    box-shadow: none !important;
    border-radius: 8px !important;
    color: #94A3B8;
    font-weight: 400;
    font-size: 12px;
}



.form-control::placeholder {
    color: #94A3B8;
    font-weight: 400;
    font-size: 12px;
}

.form-select {
    color: #94A3B8;
    font-weight: 400;
    font-size: 12px;
    background-image: url("data:image/svg+xml;utf8,<svg fill='%2394A3B8' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'><path d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1.25rem 1.25rem;
}

.form-select:focus {
    border-color: #4B67C2;
    box-shadow: none;
}

.form-select option {
    color: #111827;
    font-weight: 400;
    font-size: 14px;
}

.form-select option[disabled][selected] {
    color: #94A3B8 !important;
    font-weight: 400 !important;
    font-size: 14px !important;
}


/* Mola Al butonu stili */
.break-btn {
    background-color: #4B67C2;
    border-radius: 8px;
    border: none;
    padding-block: 0.5rem;
    box-shadow: none;
    cursor: pointer;
    color: white;
    font-weight: 500;
    font-size: 14px;
}

.admin-nav-links {
    width: 100%;
    padding: 0;
    margin: 0;
}
.admin-nav-links li {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 0;
    /* border-right kaldırıldı, sadece aradakilere eklenecek */
    min-width: 110px;
}
.admin-nav-links li:not(:last-child) {
    border-right: 1px solid #E5E7EB;
}
.admin-nav-links li:last-child {
    border-right: none;
}
.admin-nav-links a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #111827;
    font-weight: 500;
    font-size: 1rem;
    transition: color 0.2s;
    height: 100%;
    width: 100%;
    padding-block: 1rem;
}
.admin-nav-links a:hover {
    color: #4B67C2;
}
.admin-nav-links .iconify {
    margin-bottom: 0.25rem;
    color: #94A3B8 !important;
    font-size: 22px !important;
}
.admin-nav-links .nav-link-label {
    font-size: 14px;
    font-weight: 400;
    color: #94A3B8;
    margin-top: 0.1rem;
    text-align: center;
    white-space: nowrap;
}
.admin-main-links {
    width: 100%;
    padding: 0;
    margin: 0;
    border-top: 1px solid #E5E7EB;
    border-bottom: 1px solid #E5E7EB;
}
.admin-link {
    padding: 1rem 0;
    border-right: 1px solid #E5E7EB;
    flex: 1;
}
.admin-link:last-child {
    border-right: none;
}
.admin-link .iconify {
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
}
.admin-link .sm-text {
    font-size: 0.875rem;
    color: #111827;
    font-weight: 500;
}

.table-light {
    --bs-table-bg: #F7F9FC;
    --bs-table-color: #94A3B8;
}

.table-light th {
    font-weight: 600;
    font-size: 12px;
}

/* Duyurular tablosu başlık ve body köşe radiusları */
.ann-table {
    border: 1px solid #F3F4F6 !important;
    border-collapse: separate !important;
    border-spacing: 0;
    border-radius: 4px !important;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;
}
.ann-table th,
.ann-table td {
    border-right: 1px solid #F3F4F6;
    border-bottom: none !important;
    width: 33.33%;
    word-break: break-word;
    text-align: left;
    padding-block: 1rem;
}
.ann-table th:last-child,
.ann-table td:last-child {
    border-right: none;
}
.ann-table thead tr th:first-child {
    border-top-left-radius: 4px;
}
.ann-table thead tr th:last-child {
    border-top-right-radius: 4px;
}
.ann-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 4px;
}
.ann-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 4px;
}
.ann-table tr {
    border-bottom: none !important;
}
.ann-table tbody tr:nth-child(odd) {
    --bs-table-bg: #fff;
   
}
.ann-table tbody tr:nth-child(even) {
    --bs-table-bg: #F7F9FC;
   
}

.ann-table td {
    font-weight: 400;
    color: #444444;
    font-size: 14px;
}

.admin-nav-links::-webkit-scrollbar {
    height: 6px;
    background: #F3F4F6;
}
.admin-nav-links::-webkit-scrollbar-thumb {
    background: #E5E7EB;
    border-radius: 4px;
}

.admin-mobile::-webkit-scrollbar {
    height: 6px;
    background: #F3F4F6;
}
.admin-mobile::-webkit-scrollbar-thumb {
    background: #E5E7EB;
    border-radius: 4px;
}


.admin-dropdown { position: relative; }
.admin-dropdown-menu {
  position: absolute;
  left: -1px;
  right: -1px;
  top: 100%;
  min-width: 100%;
  background: #F7F9FC;
  border: 1px solid #E5E7EB;
  border-radius: 0 0 0.5rem 0.5rem;
  z-index: 10;
  margin-top: 0; /* boşluk kaldırıldı */
}

.admin-dropdown-menu li {
  border-right: none !important;
  padding: 0 !important;
}

.admin-dropdown-menu li:not(:first-child) {
  border-top: 1px solid #E5E7EB;
  border-radius: 0;
}


.admin-dropdown-menu li a {
  display: block;
  color: #94A3B8;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  padding: 1rem;
  transition: background 0.2s;
}

.admin-dropdown-menu li a:hover {
  background: #F1F5F9;
  color: #111;
}

.admin-dropdown-menu li:last-child a:hover {
  border-bottom-left-radius: 0.35rem;
  border-bottom-right-radius: 0.35rem;
}

/* Portallanan dropdown menü için aynı stil */
.admin-dropdown-menu-portal {
  background: #F7F9FC;
  border: 1px solid #E5E7EB;
  border-radius: 0 0 0.5rem 0.5rem;
  z-index: 99999;
  margin-top: 0;
  box-shadow: 0 4px 16px rgba(0,0,0,0.06);
  padding: 0;
  /* min-width, left, right kaldırıldı, JS ile ayarlanacak */
  width: unset;
  min-width: unset;
  max-width: unset;
}
.admin-dropdown-menu-portal li {
  border-right: none !important;
  padding: 0 !important;
}
.admin-dropdown-menu-portal li:not(:first-child) {
  border-top: 1px solid #E5E7EB;
  border-radius: 0;
}
.admin-dropdown-menu-portal li a {
  display: block;
  color: #94A3B8;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  text-decoration: none;
  padding: 1rem;
  transition: background 0.2s;
}
.admin-dropdown-menu-portal li a:hover {
  background: #F1F5F9;
  color: #111;
}
.admin-dropdown-menu-portal li:last-child a:hover {
  border-bottom-left-radius: 0.35rem;
  border-bottom-right-radius: 0.35rem;
}

#agentReportsDropdown .admin-dropdown-menu{
    right: -20px !important;
}

.chart-legend {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
  flex-shrink: 0;
}
.chart-legend-text {
  color: #94A3B8;
  font-size: 10px;
  font-weight: 500;
}
.chart-center-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: 500;
  color: #444444;
}

.desktop-legend {
 justify-content: space-between;
}