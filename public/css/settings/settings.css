.external-lines-section .route-links a,
.external-lines-section .route-links span,
.shift-template-section .route-links a,
.shift-template-section .route-links span,
.blacklist-section .route-links a,
.blacklist-section .route-links span,
.call-auth-section .route-links a,
.call-auth-section .route-links span
{
  font-size: 14px;
  font-weight: 400;
  color: var(--gray);
  text-decoration: none;
}


div.dt-container .dt-paging .dt-paging-button.current {
  color: #FFFFFF !important;
  border: 1px solid #F3F4F6 !important;
}

div.dt-container .dt-paging .dt-paging-button.current:hover {
  background: var(--black) !important;
  color: #FFFFFF !important;
}

div.dt-container .dt-paging .dt-paging-button:hover {
  background: white !important;
  color: var(--black) !important;
}

div.dt-container .dt-paging .dt-paging-button:focus {
  box-shadow: none !important;
}

/* Table header width styles for blacklist table */
#blacklistTable th:first-child {  /* ID header */
  width: 5% !important;
}