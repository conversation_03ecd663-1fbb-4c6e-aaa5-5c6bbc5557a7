/**
 * Customer List Page JavaScript
 * Handles customer CRUD operations for the customer list page
 */

// Import customer modules (Note: This will need to be bundled or loaded as modules)
// For now, we'll create a global customer manager that can be used directly

class CustomerListManager {
    constructor() {
        this.baseURL = '/api/customers';
        this.dataTable = null;
        this.currentModal = null;
        this.currentCustomerId = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * Initialize the customer list manager
     */
    init() {
        this.initializeDataTable();
        this.bindEvents();
        this.loadCustomers();
        this.loadDropdownData();
    }

    /**
     * Initialize DataTable
     */
    initializeDataTable() {
        const tableElement = document.querySelector('#customerListTable');
        if (tableElement && window.$ && window.$.fn.DataTable) {
            this.dataTable = $('#customerListTable').DataTable({
                searching: false,
                pageLength: 10,
                language: {
                    lengthMenu: '_MENU_',
                    info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
                },
                columnDefs: [
                    { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
                ],
                drawCallback: function(settings) {
                    document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                        btn.style.color = '#FFFFFF';
                        btn.querySelectorAll('*').forEach(function(child) {
                            child.style.color = '#FFFFFF';
                        });
                    });
                }
            });

            // Move dt-length before dt-paging
            const length = $(this.dataTable.table().container()).find('.dt-length');
            const paging = $(this.dataTable.table().container()).find('.dt-paging');
            paging.before(length);
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add button
        const addButton = document.querySelector('.add-new-btn');
        if (addButton) {
            addButton.addEventListener('click', () => this.showCreateModal());
        }

        // Delete button
        const deleteButton = document.querySelector('.delete-btn');
        if (deleteButton) {
            deleteButton.addEventListener('click', () => this.handleBulkDelete());
        }

        // Form submission
        const submitButton = document.querySelector('#stepNextBtn');
        if (submitButton) {
            submitButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }

        // Edit buttons (delegated event)
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-customer-btn')) {
                const customerId = e.target.closest('.edit-customer-btn').dataset.customerId;
                this.showEditModal(customerId);
            }
        });

        // Checkbox events for delete button state
        this.setupCheckboxEvents();

        // Setup tab navigation
        this.setupTabNavigation();
    }

    /**
     * Setup checkbox events for enabling/disabling delete button
     */
    setupCheckboxEvents() {
        const deleteBtn = document.querySelector('.delete-btn');
        if (!deleteBtn) return;

        deleteBtn.disabled = true;
        deleteBtn.classList.add('disabled-btn');

        // Function to update button states
        const updateButtonStates = () => {
            const checkedBoxes = document.querySelectorAll('#customerListTable tbody input[type="checkbox"]:checked');
            
            if (checkedBoxes.length > 0) {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('disabled-btn');
            } else {
                deleteBtn.disabled = true;
                deleteBtn.classList.add('disabled-btn');
            }
        };

        // Select all checkbox
        const checkAllBox = document.querySelector('#checkAll');
        if (checkAllBox) {
            checkAllBox.addEventListener('change', function() {
                const checked = this.checked;
                document.querySelectorAll('.row-check').forEach(checkbox => {
                    checkbox.checked = checked;
                });
                updateButtonStates();
            });
        }

        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-check')) {
                updateButtonStates();
                
                // Update select all checkbox
                if (checkAllBox) {
                    const allCheckboxes = document.querySelectorAll('#customerListTable tbody input[type="checkbox"]');
                    const checkedCheckboxes = document.querySelectorAll('#customerListTable tbody input[type="checkbox"]:checked');
                    checkAllBox.checked = allCheckboxes.length === checkedCheckboxes.length;
                }
            }
        });
    }

    /**
     * Setup tab navigation for the modal
     */
    setupTabNavigation() {
        const tabIds = ['general-tab', 'contact-tab', 'access-tab', 'finance-tab'];
        const nextBtn = document.getElementById('stepNextBtn');

        if (!nextBtn) return;

        const tabs = tabIds.map(id => document.getElementById(id)).filter(tab => tab !== null);
        let currentIndex = 0;

        const updateButton = () => {
            if (this.currentCustomerId) {
                // Edit mode - always show "Güncelle"
                nextBtn.textContent = 'Güncelle';
            } else {
                // Create mode - show "Devam Et" or "Kaydet"
                if (currentIndex < tabs.length - 1) {
                    nextBtn.textContent = 'Devam Et';
                } else {
                    nextBtn.textContent = 'Kaydet';
                }
            }
        };

        const goToTab = (index) => {
            if (index < 0 || index >= tabs.length) return;
            tabs[index].click();
            currentIndex = index;
            updateButton();
        };

        // Tab change event listeners
        tabs.forEach((tab, idx) => {
            if (tab) {
                tab.addEventListener('shown.bs.tab', function() {
                    currentIndex = idx;
                    updateButton();
                });
            }
        });

        // Override the form submit to handle tab navigation
        const originalHandleFormSubmit = this.handleFormSubmit.bind(this);
        this.handleFormSubmit = async function() {
            if (this.currentCustomerId) {
                // Edit mode - always submit
                return await originalHandleFormSubmit();
            } else {
                // Create mode - navigate tabs or submit
                if (currentIndex < tabs.length - 1) {
                    goToTab(currentIndex + 1);
                } else {
                    return await originalHandleFormSubmit();
                }
            }
        };

        // Initialize button text
        updateButton();
    }

    /**
     * Load dropdown data from APIs
     */
    async loadDropdownData() {
        try {
            // Load all dropdown data in parallel
            await Promise.allSettled([
                this.loadCustomerGroups(),
                this.loadCustomerTypes(),
                this.loadCustomerCriteria(),
                this.loadAgents()
            ]);
            console.log('✅ All dropdown data loaded successfully');
        } catch (error) {
            console.error('Error loading dropdown data:', error);
        }
    }

    /**
     * Load customer groups for dropdowns
     */
    async loadCustomerGroups() {
        try {
            const response = await window.axios.get('/api/customer-groups');
            const groups = response.data.data || response.data;

            // Populate all group dropdowns
            const groupSelectors = ['#chooseGroup', '#customerGroup', '#modalGroup'];
            groupSelectors.forEach(selector => {
                this.populateDropdown(selector, groups, {
                    placeholder: 'Grup Seçin...',
                    valueField: 'id',
                    textField: 'title'
                });
            });
        } catch (error) {
            console.error('Error loading customer groups:', error);
            this.showDropdownError(['#chooseGroup', '#customerGroup', '#modalGroup'], 'Grup yükleme hatası');
        }
    }

    /**
     * Load customer types for dropdowns
     */
    async loadCustomerTypes() {
        try {
            const response = await window.axios.get('/api/customer-types');
            const types = response.data.data || response.data;

            // Populate all type dropdowns
            const typeSelectors = ['#chooseType', '#customerType', '#modalType'];
            typeSelectors.forEach(selector => {
                this.populateDropdown(selector, types, {
                    placeholder: 'Tip Seçin...',
                    valueField: 'id',
                    textField: 'title'
                });
            });
        } catch (error) {
            console.error('Error loading customer types:', error);
            this.showDropdownError(['#chooseType', '#customerType', '#modalType'], 'Tip yükleme hatası');
        }
    }

    /**
     * Load customer criteria for dropdowns
     */
    async loadCustomerCriteria() {
        try {
            const response = await window.axios.get('/api/customer-criteria');
            const criteria = response.data.data || response.data;

            // Populate all criteria dropdowns
            const criteriaSelectors = ['#chooseCriteria', '#customerCriteria', '#modalCriteria'];
            criteriaSelectors.forEach(selector => {
                this.populateDropdown(selector, criteria, {
                    placeholder: 'Kriter Seçin...',
                    valueField: 'id',
                    textField: 'title'
                });
            });
        } catch (error) {
            console.error('Error loading customer criteria:', error);
            this.showDropdownError(['#chooseCriteria', '#customerCriteria', '#modalCriteria'], 'Kriter yükleme hatası');
        }
    }

    /**
     * Load agents for dropdowns
     */
    async loadAgents() {
        try {
            const response = await window.axios.get('/api/users/agents');
            const agents = response.data.data || response.data;

            // Populate all agent dropdowns
            const agentSelectors = ['#chooseAgent', '#officialAgent', '#modalAgent'];
            agentSelectors.forEach(selector => {
                this.populateDropdown(selector, agents, {
                    placeholder: 'Y.Agent Seçin...',
                    valueField: 'id',
                    textField: 'name'
                });
            });
        } catch (error) {
            console.error('Error loading agents:', error);
            this.showDropdownError(['#chooseAgent', '#officialAgent', '#modalAgent'], 'Agent yükleme hatası');
        }
    }

    /**
     * Populate a dropdown with data
     */
    populateDropdown(selector, data, options = {}) {
        const dropdown = document.querySelector(selector);
        if (!dropdown) return;

        const config = {
            valueField: 'id',
            textField: 'title',
            placeholder: 'Seçiniz...',
            ...options
        };

        // Clear existing options except the first placeholder
        dropdown.innerHTML = `<option disabled selected>${config.placeholder}</option>`;

        // Add data options
        if (Array.isArray(data)) {
            data.forEach(item => {
                const option = document.createElement('option');
                option.value = item[config.valueField];
                option.textContent = item[config.textField];
                dropdown.appendChild(option);
            });
        }
    }

    /**
     * Show error state for dropdowns
     */
    showDropdownError(selectors, errorText) {
        selectors.forEach(selector => {
            const dropdown = document.querySelector(selector);
            if (dropdown) {
                dropdown.innerHTML = `<option disabled selected>${errorText}</option>`;
            }
        });
    }

    /**
     * Refresh dropdown data for modal forms
     */
    async refreshModalDropdowns() {
        try {
            // Load fresh data for modal dropdowns
            await Promise.allSettled([
                this.loadCustomerGroupsForModal(),
                this.loadCustomerTypesForModal(),
                this.loadCustomerCriteriaForModal(),
                this.loadAgentsForModal()
            ]);
        } catch (error) {
            console.error('Error refreshing modal dropdowns:', error);
        }
    }

    /**
     * Load customer groups specifically for modal
     */
    async loadCustomerGroupsForModal() {
        try {
            const response = await window.axios.get('/api/customer-groups');
            const groups = response.data.data || response.data;
            this.populateDropdown('#customerGroup', groups, {
                placeholder: 'Seçiniz...',
                valueField: 'id',
                textField: 'title'
            });
        } catch (error) {
            console.error('Error loading customer groups for modal:', error);
        }
    }

    /**
     * Load customer types specifically for modal
     */
    async loadCustomerTypesForModal() {
        try {
            const response = await window.axios.get('/api/customer-types');
            const types = response.data.data || response.data;
            this.populateDropdown('#customerType', types, {
                placeholder: 'Seçiniz...',
                valueField: 'id',
                textField: 'title'
            });
        } catch (error) {
            console.error('Error loading customer types for modal:', error);
        }
    }

    /**
     * Load customer criteria specifically for modal
     */
    async loadCustomerCriteriaForModal() {
        try {
            const response = await window.axios.get('/api/customer-criteria');
            const criteria = response.data.data || response.data;
            this.populateDropdown('#customerCriteria', criteria, {
                placeholder: 'Seçiniz...',
                valueField: 'id',
                textField: 'title'
            });
        } catch (error) {
            console.error('Error loading customer criteria for modal:', error);
        }
    }

    /**
     * Load agents specifically for modal
     */
    async loadAgentsForModal() {
        try {
            const response = await window.axios.get('/api/users/agents');
            const agents = response.data.data || response.data;
            this.populateDropdown('#officialAgent', agents, {
                placeholder: 'Seçiniz...',
                valueField: 'id',
                textField: 'name'
            });
        } catch (error) {
            console.error('Error loading agents for modal:', error);
        }
    }

    /**
     * Load customers from API
     */
    async loadCustomers() {
        try {
            if (!window.axios) {
                console.error('Axios is not available');
                this.showNotification('Axios library not loaded. Customer data cannot be loaded.', 'error');
                return;
            }

            const response = await window.axios.get(this.baseURL);
            if (response.data) {
                this.updateTable(response.data);
                console.log('✅ Customer data loaded successfully');
            }
        } catch (error) {
            console.error('Error loading customers:', error);
            this.showNotification('Müşteri listesi yüklenirken bir hata oluştu.', 'error');
        }
    }

    /**
     * Update table with customer data
     */
    updateTable(customers) {
        if (!this.dataTable) return;

        // Clear existing data
        this.dataTable.clear();

        // Add new data
        if (Array.isArray(customers)) {
            customers.forEach(customer => {
                const row = [
                    `<input type="checkbox" class="row-check" data-customer-id="${customer.id}">`,
                    customer.id,
                    customer.first_name || '',
                    customer.last_name || '',
                    customer.phone || customer.gsm || '',
                    customer.oid || '',
                    customer.customer_type?.title || '',
                    customer.customer_group?.title || '',
                    customer.customer_criteria?.title || '',
                    `<button class="btn btn-sm btn-primary edit-customer-btn" data-customer-id="${customer.id}" title="Düzenle">
                        <span class="iconify" data-icon="hugeicons:edit-02"></span>
                    </button>`
                ];
                this.dataTable.row.add(row);
            });
        }

        // Redraw table
        this.dataTable.draw();
    }

    /**
     * Show create modal
     */
    async showCreateModal() {
        this.currentCustomerId = null;
        const modal = document.getElementById('addModal');
        if (modal) {
            // Update modal title
            const title = modal.querySelector('#addModalLabel');
            if (title) {
                title.textContent = 'Yeni Müşteri Kartı';
            }

            // Reset form
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
                this.clearFormErrors(form);
            }

            // Reset to first tab
            const firstTab = modal.querySelector('#general-tab');
            if (firstTab) {
                firstTab.click();
            }

            // Update button text
            const submitBtn = modal.querySelector('#stepNextBtn');
            if (submitBtn) {
                submitBtn.textContent = 'Devam Et';
            }

            // Refresh dropdown data for modal
            await this.refreshModalDropdowns();

            this.currentModal = new bootstrap.Modal(modal);
            this.currentModal.show();
        }
    }

    /**
     * Show edit modal
     */
    async showEditModal(customerId) {
        this.currentCustomerId = customerId;
        const modal = document.getElementById('addModal');
        if (modal) {
            // Update modal title
            const title = modal.querySelector('#addModalLabel');
            if (title) {
                title.textContent = 'Müşteri Düzenle';
            }

            // Reset to first tab
            const firstTab = modal.querySelector('#general-tab');
            if (firstTab) {
                firstTab.click();
            }

            // Update button text
            const submitBtn = modal.querySelector('#stepNextBtn');
            if (submitBtn) {
                submitBtn.textContent = 'Güncelle';
            }

            try {
                // Refresh dropdown data for modal first
                await this.refreshModalDropdowns();

                // Load customer data
                const response = await window.axios.get(`${this.baseURL}/${customerId}`);
                if (response.data) {
                    this.populateForm(response.data);
                    this.clearFormErrors(modal.querySelector('form'));
                    this.currentModal = new bootstrap.Modal(modal);
                    this.currentModal.show();
                }
            } catch (error) {
                console.error('Error loading customer:', error);
                this.showNotification('Müşteri bilgileri yüklenirken bir hata oluştu.', 'error');
            }
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit() {
        const form = document.querySelector('#addModal form');
        if (!form) return;

        const submitBtn = document.querySelector('#stepNextBtn');
        const originalText = submitBtn.textContent;
        
        try {
            // Show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Kaydediliyor...';

            // Collect form data
            const formData = this.collectFormData(form);

            // Debug: Log the collected form data
            console.log('Collected form data:', formData);

            // Clear previous errors
            this.clearFormErrors(form);

            let response;
            if (this.currentCustomerId) {
                // Update
                response = await window.axios.put(`${this.baseURL}/${this.currentCustomerId}`, formData);
            } else {
                // Create
                response = await window.axios.post(this.baseURL, formData);
            }

            if (response.data) {
                const operation = this.currentCustomerId ? 'güncellendi' : 'oluşturuldu';
                this.showNotification(`Müşteri başarıyla ${operation}.`, 'success');
                
                // Close modal and reload data
                if (this.currentModal) {
                    this.currentModal.hide();
                }
                this.loadCustomers();
            }

        } catch (error) {
            console.error('Form submission error:', error);

            if (error.response && error.response.data && error.response.data.errors) {
                // Display server-side validation errors from Laravel
                this.displayFormErrors(error.response.data.errors, form);
                const message = error.response.data.message || 'Lütfen form hatalarını düzeltin.';
                this.showNotification(message, 'error');
            } else {
                const message = error.response?.data?.message || 'İşlem sırasında bir hata oluştu.';
                this.showNotification(message, 'error');
            }
        } finally {
            // Hide loading
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    /**
     * Handle bulk delete
     */
    async handleBulkDelete() {
        const checkedBoxes = document.querySelectorAll('#customerListTable tbody input[type="checkbox"]:checked');
        const customerIds = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.customerId);

        if (customerIds.length === 0) {
            this.showNotification('Lütfen silinecek müşterileri seçin.', 'warning');
            return;
        }

        // Show confirmation modal
        const deleteModal = document.getElementById('deleteConfirmModal');
        if (deleteModal) {
            const messageElement = deleteModal.querySelector('.modal-body h5');
            if (messageElement) {
                messageElement.textContent = `${customerIds.length} müşteriyi silmek istediğinize emin misiniz?`;
            }

            const confirmBtn = deleteModal.querySelector('#confirmDeleteBtn');
            if (confirmBtn) {
                // Remove existing event listeners
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
                
                // Add new event listener
                newConfirmBtn.addEventListener('click', async () => {
                    try {
                        await window.axios.delete(this.baseURL, {
                            data: { ids: customerIds }
                        });
                        
                        this.showNotification('Seçilen müşteriler başarıyla silindi.', 'success');
                        this.loadCustomers();
                        
                        // Close modal
                        bootstrap.Modal.getInstance(deleteModal).hide();
                    } catch (error) {
                        console.error('Delete error:', error);
                        const message = error.response?.data?.message || 'Müşteriler silinirken bir hata oluştu.';
                        this.showNotification(message, 'error');
                    }
                });
            }

            const bootstrapModal = new bootstrap.Modal(deleteModal);
            bootstrapModal.show();
        }
    }

    /**
     * Collect form data
     */
    collectFormData(form) {
        const formData = new FormData(form);
        const data = {};

        // Field mapping
        const fieldMapping = {
            'customerName': 'first_name',
            'customerSurname': 'last_name',
            'customerPhone': 'phone',
            'customerIdentity': 'identity_no',
            'customerBirthDate': 'birth_day',
            'customerBirthPlace': 'birth_place',
            'customerCompany': 'company_id',
            'customerGroup': 'customer_group_id',
            'customerType': 'customer_type_id',
            'customerCriteria': 'customer_criteria_id',
            'officialAgent': 'agent_id',
            'customerStatus': 'status',
            'externalCustomerId': 'oid',
            'ivrTarget': 'dynamic_ivr_id',
            'customerDescription': 'description',
            'customerLandline': 'gsm',
            'customerFax': 'fax',
            'customerMail': 'email',
            'customerAddress': 'address',
            'customerCity': 'city',
            'customerDistrict': 'neighborhood',
            'customerCountry': 'country',
            'customerTaxOffice': 'tax_office',
            'customerTaxNumber': 'tax_no'
        };

        // Collect regular form fields
        for (const [key, value] of formData.entries()) {
            const apiFieldName = fieldMapping[key] || key;
            data[apiFieldName] = value === '' ? null : value;
        }

        // Handle checkboxes separately (they won't be in FormData if unchecked)
        const callCheckbox = form.querySelector('input[name="customerCallAble"]');
        if (callCheckbox) {
            data['can_receive_call'] = callCheckbox.checked ? 1 : 0;
        }

        const smsCheckbox = form.querySelector('input[name="customerSmsAble"]');
        if (smsCheckbox) {
            data['can_receive_sms'] = smsCheckbox.checked ? 1 : 0;
        }

        // Convert numeric fields
        const numericFields = ['company_id', 'customer_group_id', 'customer_type_id', 'customer_criteria_id', 'agent_id', 'dynamic_ivr_id', 'status'];
        numericFields.forEach(field => {
            if (data[field] !== null && data[field] !== undefined && data[field] !== '') {
                const numValue = parseInt(data[field]);
                data[field] = isNaN(numValue) ? null : numValue;
            } else if (data[field] === '') {
                data[field] = null;
            }
        });

        return data;
    }

    /**
     * Populate form with customer data
     */
    populateForm(customerData) {
        const form = document.querySelector('#addModal form');
        if (!form) return;

        // Field mapping (reverse)
        const fieldMapping = {
            'first_name': 'customerName',
            'last_name': 'customerSurname',
            'phone': 'customerPhone',
            'identity_no': 'customerIdentity',
            'birth_day': 'customerBirthDate',
            'birth_place': 'customerBirthPlace',
            'company_id': 'customerCompany',
            'customer_group_id': 'customerGroup',
            'customer_type_id': 'customerType',
            'customer_criteria_id': 'customerCriteria',
            'agent_id': 'officialAgent',
            'status': 'customerStatus',
            'oid': 'externalCustomerId',
            'dynamic_ivr_id': 'ivrTarget',
            'description': 'customerDescription',
            'gsm': 'customerLandline',
            'fax': 'customerFax',
            'email': 'customerMail',
            'address': 'customerAddress',
            'city': 'customerCity',
            'neighborhood': 'customerDistrict',
            'country': 'customerCountry',
            'tax_office': 'customerTaxOffice',
            'tax_no': 'customerTaxNumber'
        };

        // Populate form fields
        Object.entries(customerData).forEach(([apiField, value]) => {
            const formFieldName = fieldMapping[apiField] || apiField;
            const field = form.querySelector(`[name="${formFieldName}"]`);

            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(value);
                } else {
                    field.value = value || '';
                }
            }
        });

        // Handle special checkboxes - convert 1/0 to boolean
        const callCheckbox = form.querySelector('[name="customerCallAble"]');
        if (callCheckbox) {
            callCheckbox.checked = customerData.can_receive_call == 1 || customerData.can_receive_call === true;
        }

        const smsCheckbox = form.querySelector('[name="customerSmsAble"]');
        if (smsCheckbox) {
            smsCheckbox.checked = customerData.can_receive_sms == 1 || customerData.can_receive_sms === true;
        }
    }

    // Client-side validation removed - validation is now handled exclusively by server-side Laravel validation

    /**
     * Display server-side validation errors from Laravel API response
     */
    displayFormErrors(errors, form) {
        // Clear previous errors
        this.clearFormErrors(form);

        const fieldMapping = {
            'first_name': 'customerName',
            'last_name': 'customerSurname',
            'phone': 'customerPhone',
            'email': 'customerMail',
            'identity_no': 'customerIdentity',
            'gsm': 'customerLandline',
            'fax': 'customerFax',
            'address': 'customerAddress',
            'city': 'customerCity',
            'neighborhood': 'customerDistrict',
            'country': 'customerCountry',
            'tax_office': 'customerTaxOffice',
            'tax_no': 'customerTaxNumber'
        };

        Object.entries(errors).forEach(([field, fieldErrors]) => {
            const formFieldName = fieldMapping[field] || field;
            const fieldElement = form.querySelector(`[name="${formFieldName}"]`);

            if (fieldElement) {
                fieldElement.classList.add('is-invalid');

                // Create error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = Array.isArray(fieldErrors) ? fieldErrors[0] : fieldErrors;

                // Insert after field
                fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
            }
        });
    }

    /**
     * Clear form errors
     */
    clearFormErrors(form) {
        // Remove error classes
        form.querySelectorAll('.is-invalid').forEach(element => {
            element.classList.remove('is-invalid');
        });

        // Remove error messages
        form.querySelectorAll('.invalid-feedback').forEach(element => {
            element.remove();
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification container if it doesn't exist
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }

        // Create notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.cssText = 'margin-bottom: 10px;';

        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
    }
}

window.customerListManager = new CustomerListManager();
