function init_agent_status() {
    var table = $('#agentStatusTable').DataTable({
        searching: false,
        pageLength: 10,
        language: {
            lengthMenu: '_MENU_',
            info: '_START_ ile _END_ arası gö<PERSON>iliyor, toplam _TOTAL_ kayıt.'
        },
        drawCallback: function (settings) {
            document.querySelectorAll('.dt-paging-button.current').forEach(function (btn) {
                btn.style.color = '#FFFFFF';
                btn.querySelectorAll('*').forEach(function (child) {
                    child.style.color = '#FFFFFF';
                });
            });
        }
    });

    var length = $(table.table().container()).find('.dt-length');
    var paging = $(table.table().container()).find('.dt-paging');
    paging.before(length);
}
