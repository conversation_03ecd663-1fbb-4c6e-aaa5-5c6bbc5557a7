function init_planned_calls() {
    var table = $('#plannedCallsTable').DataTable({
        searching: false,
        pageLength: 10,
        language: {
            lengthMenu: '_MENU_',
            info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
        },
        columnDefs: [
            {orderable: false, targets: 0} // İlk sütunda sıralama kapalı
        ],
        drawCallback: function (settings) {
            document.querySelectorAll('.dt-paging-button.current').forEach(function (btn) {
                btn.style.color = '#FFFFFF';
                btn.querySelectorAll('*').forEach(function (child) {
                    child.style.color = '#FFFFFF';
                });
            });
        }
    });
    // dt-length'i dt-paging'in soluna taşı
    var length = $(table.table().container()).find('.dt-length');
    var paging = $(table.table().container()).find('.dt-paging');
    paging.before(length);

    // Edit butonu başlangıçta disabled yap
    var editBtn = document.querySelector('.edit-btn');
    var deleteBtn = document.querySelector('.delete-btn');
    editBtn.disabled = true;
    deleteBtn.disabled = true;
    editBtn.classList.add('disabled-btn');
    deleteBtn.classList.add('disabled-btn');

    // Global değişkenleri tanımla
    window.editBtn = editBtn;
    window.deleteBtn = deleteBtn;

    // Seçim durumunu kontrol et ve butonları güncelle
    function updateButtonStates() {
        var checkedBoxes = document.querySelectorAll('#plannedCallsTable tbody input[type="checkbox"]:checked');

        // Edit butonu sadece bir öğe seçiliyse aktif olacak
        if (checkedBoxes.length === 1) {
            editBtn.disabled = false;
            editBtn.classList.remove('disabled-btn');
        } else {
            editBtn.disabled = true;
            editBtn.classList.add('disabled-btn');
        }

        // Delete butonu en az bir öğe seçiliyse aktif olacak
        if (checkedBoxes.length > 0) {
            deleteBtn.disabled = false;
            deleteBtn.classList.remove('disabled-btn');
        } else {
            deleteBtn.disabled = true;
            deleteBtn.classList.add('disabled-btn');
        }

        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
    }

    // Tümünü seç/deselect
    $('#checkAll').on('change', function () {
        var checked = this.checked;
        $('.row-check').prop('checked', checked);
        updateButtonStates();
    });

    // Herhangi bir checkbox değiştiğinde
    $(document).on('change', '#plannedCallsTable tbody input[type="checkbox"]', function () {
        updateButtonStates();

        // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
        if (!this.checked) {
            $('#checkAll').prop('checked', false);
        } else {
            // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
            var allChecked = $('#plannedCallsTable tbody input[type="checkbox"]').length ===
                $('#plannedCallsTable tbody input[type="checkbox"]:checked').length;
            $('#checkAll').prop('checked', allChecked);
        }
    });

    // Edit butonuna tıklandığında
    editBtn.addEventListener('click', function () {
        if (!this.disabled) {
            var checkedRow = document.querySelector('#plannedCallsTable tbody input[type="checkbox"]:checked').closest('tr');
            var rowData = table.row(checkedRow).data();


            // Modal'ı aç
            var editModal = new bootstrap.Modal(document.getElementById('addModal'));
            // Başlığı değiştir
            document.getElementById('addModalLabel').textContent = 'Planlı Arama Düzenle';
            editModal.show();
        }
    });

    // Add new button modal functionality
    document.querySelector('.add-new-btn').addEventListener('click', function () {
        // Modalı açmadan önce başlığı Grup Ekle olarak ayarla
        document.getElementById('addModalLabel').textContent = 'Planlı Arama Ekle';


        var addModal = new bootstrap.Modal(document.getElementById('addModal'));
        addModal.show();
    });


    // Delete butonuna tıklandığında
    deleteBtn.addEventListener('click', function () {
        if (!this.disabled) {
            var checkedBoxes = document.querySelectorAll('#plannedCallsTable tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length > 0) {
                // Global silme modalını göster
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                deleteModal.show();

                // Silme onaylandığında yapılacak işlemler
                document.getElementById('confirmDeleteBtn').onclick = function () {
                    // Burada seçilen satırları silme işlemi yapılacak
                    // Şimdilik sadece modalı kapatıyoruz
                    deleteModal.hide();
                };
            }
        }
    });

    updateButtonStates();

}
