function init_calls() {
    // Aktif tab bilgisini globalde tut
    var currentActiveTab = $('.tab-pane.active').attr('id'); // <PERSON><PERSON> yüklenince aktif tabı ata

    // Tabloları ve ilgili butonları dinamik olarak işle
    var tableIds = ['recentCallsTable', 'incomingCallsTable', 'outgoingCallsTable', 'missedCallsTable', 'missedQueueCallsTable', 'callbackRequestsTable'];
    var dataTables = {};
    tableIds.forEach(function (tableId) {
        if (document.getElementById(tableId)) {
            dataTables[tableId] = $('#' + tableId).DataTable({
                searching: false,
                pageLength: 10,
                language: {
                    lengthMenu: '_MENU_',
                    info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
                },
                columnDefs: [
                    {orderable: false, targets: 0}
                ],
                drawCallback: function (settings) {
                    // Paging button stillemesi
                    document.querySelectorAll('#' + tableId + ' .dt-paging-button.current').forEach(function (btn) {
                        btn.style.color = '#FFFFFF';
                        btn.querySelectorAll('*').forEach(function (child) {
                            child.style.color = '#FFFFFF';
                        });
                    });
                }
            });
            // dt-length'i dt-paging'in soluna taşı
            var length = $(dataTables[tableId].table().container()).find('.dt-length');
            var paging = $(dataTables[tableId].table().container()).find('.dt-paging');
            paging.before(length);
        }
    });

    // Her tablo için butonları ve checkboxları işle
    tableIds.forEach(function (tableId) {

        var $table = $('#' + tableId);
        var container = $table.closest('.white-container');
        // Tümünü seç/deselect
        $table.on('change', 'thead input[type="checkbox"]', function () {
            var checked = this.checked;
            $table.find('tbody input[type="checkbox"]').prop('checked', checked);
        });

    });

    // Responsive ve mobil menü işlemleri (varsa)
    if (typeof window.updateMobileMenuItems === 'function') {
        window.updateMobileMenuItems();
    }
    window.addEventListener('resize', function () {
        if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
    });


    // Tab değişikliğini dinle ve aktif tabı güncelle
    $('a[data-bs-toggle="tab"], button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        const targetTab = $(e.target).attr('data-bs-target') || $(e.target).attr('href');
        if (targetTab && targetTab.startsWith('#')) {
            currentActiveTab = targetTab.replace('#', '');
        }

        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;

            if (currentButton) {
                currentButton.html('<span class="iconify" data-icon="hugeicons:play-circle"></span>');
            }

            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }

            $('.audio-progress-bar').css('width', '0%');
            $('.audio-duration').text('--:--');

            currentAudio = null;
            currentButton = null;
        }
    });

    // Filtre ikonuna tıklanınca sadece aktif tab bilgisini güncelle
    // Debugging: Check if the click event listener is being attached
    console.log('Attaching click event listener to .filter-icon-btn');
    $(document).on('click', '.filter-icon-btn', function () {
        console.log('Delegated: Filter icon button clicked.');
        currentActiveTab = $('.tab-pane.active').attr('id');
    });

    // Modal açılırken içeriği güncelle
    $('#filterModal').on('show.bs.modal', function () {
        if (currentActiveTab) {
            updateFilterModalContent(currentActiveTab);
        }
    });

    // Modal içeriğini aktif tab'a göre güncelle
    function updateFilterModalContent(activeTabId) {
        // Modal içeriğini temizle
        $('#filterModal .modal-body').empty();

        // Aktif tab'deki input ve select alanlarını kopyala
        const filterFields = $('#' + activeTabId + ' .internal-choose .col');

        // Her bir sütundaki input ve select alanlarını modalda göster
        filterFields.each(function (colIndex) {
            const formElements = $(this).find('input, select'); // Daha genel bir selector

            formElements.each(function (elementIndex) {
                const element = $(this);
                const elementType = element.prop('tagName').toLowerCase();
                let elementClone;

                if (elementType === 'input' || elementType === 'select') {
                    const formGroup = $('<div class="col-12 mb-3"></div>');

                    // Etiket metnini belirle
                    let labelText = '';
                    if (element.attr('type') === 'date') {
                        // Tarih alanları için özel etiketler
                        if (colIndex === 0 && elementIndex === 0) {
                            labelText = 'Başlangıç Tarihi';
                        } else if (colIndex === 0 && elementIndex === 1) {
                            labelText = 'Bitiş Tarihi';
                        } else {
                            labelText = 'Bitiş Tarihi';
                        }
                    } else {
                        labelText = element.attr('placeholder') || element.find('option:selected').text() || 'Seçenek';
                    }

                    const label = $('<label class="form-label"></label>').text(labelText);
                    formGroup.append(label);

                    elementClone = element.clone().addClass('form-control');
                    elementClone.removeClass('w-auto');
                    formGroup.append(elementClone);

                    $('#filterModal .modal-body').append(formGroup);
                }
            });
        });
    }

    // Modal değerlerini form alanlarına geri aktar
    $('#applyFilters').on('click', function () {
        if (currentActiveTab) {
            transferModalValuesToForm(currentActiveTab);
        }
        $('#filterModal').modal('hide');
    });

    function transferModalValuesToForm(activeTabId) {
        $('#filterModal .modal-body input, #filterModal .modal-body select').each(function (index) {
            const modalValue = $(this).val();
            const formElements = $('#' + activeTabId + ' .internal-choose .d-md-flex').find('input, select').not('button');

            if (formElements.length > index) {
                $(formElements[index]).val(modalValue);
            }
        });
    }

    // Enhance audio player functionality
    let currentAudio = null;
    let currentButton = null;
    let progressInterval = null;

    $(document).on('click', '.play-button', function () {
        const button = $(this);
        const audioUrl = button.data('audio-url');
        const audioId = button.data('audio-id');
        const progressBar = $(`.audio-progress-bar[data-audio-id="${audioId}"]`);
        const durationLabel = button.siblings('.audio-duration');

        // Stop currently playing audio if exists
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;

            if (currentButton) {
                currentButton.html('<span class="iconify" data-icon="hugeicons:play-circle"></span>');
            }

            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }

            progressBar.css('width', '0%');
            durationLabel.text('--:--');

            // If the same button is clicked, stop and return
            if (currentButton && currentButton.is(button)) {
                currentAudio = null;
                currentButton = null;
                return;
            }
        }

        // Create new audio instance
        if (audioUrl) {
            const audio = new Audio(audioUrl);
            currentAudio = audio;
            currentButton = button;

            audio.addEventListener('loadedmetadata', () => {
                const duration = Math.floor(audio.duration);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;
                durationLabel.text(`${minutes}:${seconds.toString().padStart(2, '0')}`);
            });

            audio.addEventListener('timeupdate', () => {
                const progress = (audio.currentTime / audio.duration) * 100;
                progressBar.css('width', `${progress}%`);
            });

            audio.addEventListener('ended', () => {
                button.html('<span class="iconify" data-icon="hugeicons:play-circle"></span>');
                progressBar.css('width', '0%');
                durationLabel.text('--:--');
                clearInterval(progressInterval);
                progressInterval = null;
                currentAudio = null;
                currentButton = null;
            });

            audio.play().then(() => {
                button.html('<span class="iconify" data-icon="hugeicons:pause-circle"></span>');
            }).catch((error) => {
                console.error('Error playing audio:', error);
            });

            // Update progress bar every 100ms
            progressInterval = setInterval(() => {
                if (audio.duration) {
                    const progress = (audio.currentTime / audio.duration) * 100;
                    progressBar.css('width', `${progress}%`);
                }
            }, 100);
        } else {
            console.error('No audio URL found for this button.');
        }
    });

    $(document).on('click', '.audio-progress-container', function (e) {
        if (!currentAudio) return;

        const progressContainer = $(this);
        const rect = progressContainer[0].getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const width = rect.width;
        const percentage = clickX / width;

        if (currentAudio.duration) {
            currentAudio.currentTime = percentage * currentAudio.duration;
        }
    });
}
