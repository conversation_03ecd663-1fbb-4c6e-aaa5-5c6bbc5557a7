function init_customer_list() {
    var table = $('#customerListTable').DataTable({
        searching: false,
        pageLength: 10,
        language: {
            lengthMenu: '_MENU_',
            info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
        },
        columnDefs: [
            {targets: 0, orderable: false}
        ],
        drawCallback: function (settings) {
            document.querySelectorAll('.dt-paging-button.current').forEach(function (btn) {
                btn.style.color = '#FFFFFF';
                btn.querySelectorAll('*').forEach(function (child) {
                    child.style.color = '#FFFFFF';
                });
            });
        }
    });
// dt-length'i dt-paging'in soluna taşı
    var length = $(table.table().container()).find('.dt-length');
    var paging = $(table.table().container()).find('.dt-paging');
    paging.before(length);


    document.querySelectorAll('.blue-btn.p-1').forEach(function (button) {
        button.addEventListener('click', function () {
            var customerListInfo = new bootstrap.Modal(document.getElementById('customerListInfo'));
            customerListInfo.show();
        });
    });
}
