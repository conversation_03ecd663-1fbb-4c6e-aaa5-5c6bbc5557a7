/**
 * Console Phone Call Utility
 * Allows programmatic phone calls through the dialpad from browser console
 *
 * Usage from console:
 * - call<PERSON><PERSON>("1234567890") - Fill dialpad and initiate actual phone call
 * - makeCall("1234567890") - Alternative function name
 * - dialAndCall("1234567890") - Descriptive function name
 */

(function() {
    'use strict';

    /**
     * Main function to fill dialpad and initiate phone call
     * @param {string} phoneNumber - The phone number to call
     * @param {Object} options - Optional configuration
     * @returns {Promise<boolean>} - Success status
     */
    async function fillDialpadAndMakeCall(phoneNumber, options = {}) {
        const config = {
            // Delay between filling input and clicking call button (ms)
            delay: 100,
            // Whether to show console logs
            verbose: true,
            // Whether to open dialpad if not visible
            openDialpad: true,
            // Whether to validate SIP connection before calling
            checkSIP: true,
            ...options
        };

        try {
            // Validate phone number
            if (!phoneNumber || typeof phoneNumber !== 'string') {
                throw new Error('Phone number must be a non-empty string');
            }

            // Clean phone number (remove spaces, dashes, parentheses, but keep + for international)
            const cleanPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

            if (!/^[\d+]+$/.test(cleanPhone)) {
                throw new Error('Phone number must contain only digits and + (spaces, dashes, parentheses are allowed)');
            }

            if (config.verbose) {
                console.log(`📞 Initiating call to: ${cleanPhone}`);
            }

            // Check if dialpad is available
            const dialpadPanel = document.getElementById('dialpadPanel');
            if (!dialpadPanel) {
                throw new Error('Dialpad panel not found. Make sure you are on a page with dialpad functionality.');
            }

            // Open dialpad if not visible and openDialpad is enabled
            if (config.openDialpad && dialpadPanel.classList.contains('hide')) {
                if (config.verbose) {
                    console.log('📱 Opening dialpad...');
                }

                // Try to find and click dialpad trigger button
                const dialpadTrigger = document.querySelector('[data-target="dialpad"]') ||
                                     document.querySelector('.dial-bottom-item[data-target="dialpad"]');

                if (dialpadTrigger) {
                    dialpadTrigger.click();
                    // Wait for dialpad to open
                    await new Promise(resolve => setTimeout(resolve, 300));
                } else {
                    // Manually show dialpad
                    dialpadPanel.classList.remove('hide');
                }
            }

            // Find dialpad input field
            const dialedNumber = document.getElementById('dialedNumber');
            if (!dialedNumber) {
                throw new Error('Dialpad input field (#dialedNumber) not found');
            }

            // Find call button
            const callBtn = document.querySelector('.call-btn');
            if (!callBtn) {
                throw new Error('Call button (.call-btn) not found');
            }

            // Check SIP connection if enabled
            if (config.checkSIP) {
                // Check if sipManager is available and ready
                if (typeof window.sipManager === 'undefined' || !window.sipManager) {
                    console.warn('⚠️ SIP Manager not found. Call may not work properly.');
                } else if (!window.sipManager.isReady()) {
                    console.warn('⚠️ SIP connection not ready. Call may fail.');
                    if (config.verbose) {
                        console.log('💡 Tip: Wait for SIP connection to be established or disable checkSIP option');
                    }
                }
            }

            // Clear existing number and fill with new number
            dialedNumber.value = '';
            dialedNumber.value = cleanPhone;

            // Dispatch input events to ensure any listeners are triggered
            dialedNumber.dispatchEvent(new Event('input', { bubbles: true }));
            dialedNumber.dispatchEvent(new Event('change', { bubbles: true }));

            if (config.verbose) {
                console.log(`✅ Dialpad filled with: ${cleanPhone}`);
            }

            // Function to trigger the call
            const initiateCall = () => {
                if (config.verbose) {
                    console.log('🚀 Clicking call button to initiate call...');
                }

                // Click the call button - this will trigger the same logic as manual click
                callBtn.click();

                if (config.verbose) {
                    console.log('📞 Call initiated! Check the call UI for status.');
                }

                return true;
            };

            // Trigger call after a short delay to ensure input is processed
            setTimeout(initiateCall, config.delay);

            return true;

        } catch (error) {
            console.error('❌ Error initiating call:', error.message);
            return false;
        }
    }

    /**
     * Enhanced call function with additional features
     */
    async function enhancedPhoneCall(phoneNumber, options = {}) {
        const success = await fillDialpadAndMakeCall(phoneNumber, options);

        // Additional features can be added here
        if (success && options.verbose !== false) {
            console.log('💡 Call initiated successfully. Monitor the call UI for status updates.');
            console.log('💡 Use endCall() to terminate the call if needed.');
        }

        return success;
    }

    /**
     * Function to end current call
     */
    function endCurrentCall() {
        try {
            const endCallBtn = document.querySelector('.end-call');
            if (endCallBtn && endCallBtn.offsetParent !== null) {
                endCallBtn.click();
                console.log('📞 Call ended');
                return true;
            } else {
                console.warn('⚠️ End call button not found or not visible');
                return false;
            }
        } catch (error) {
            console.error('❌ Error ending call:', error.message);
            return false;
        }
    }

    /**
     * Function to show dialpad status and available elements
     */
    function showDialpadStatus() {
        console.log('📱 Dialpad Status:');

        const dialpadPanel = document.getElementById('dialpadPanel');
        const dialedNumber = document.getElementById('dialedNumber');
        const callBtn = document.querySelector('.call-btn');
        const activeCallContent = document.getElementById('activeCallContent');
        const incomingCallContent = document.getElementById('incomingCallContent');

        console.log(`  • Dialpad Panel: ${dialpadPanel ? (dialpadPanel.classList.contains('hide') ? 'Hidden' : 'Visible') : 'Not Found'}`);
        console.log(`  • Dialed Number Input: ${dialedNumber ? `Found (value: "${dialedNumber.value}")` : 'Not Found'}`);
        console.log(`  • Call Button: ${callBtn ? 'Found' : 'Not Found'}`);
        console.log(`  • Active Call UI: ${activeCallContent ? (activeCallContent.classList.contains('d-none') ? 'Hidden' : 'Visible') : 'Not Found'}`);
        console.log(`  • Incoming Call UI: ${incomingCallContent ? (incomingCallContent.classList.contains('d-none') ? 'Hidden' : 'Visible') : 'Not Found'}`);

        // Check SIP Manager status
        if (typeof window.sipManager !== 'undefined' && window.sipManager) {
            console.log(`  • SIP Manager: Available (Ready: ${window.sipManager.isReady ? window.sipManager.isReady() : 'Unknown'})`);
        } else {
            console.log('  • SIP Manager: Not Available');
        }

        // Show current call status if any
        if (window.sipManager && window.sipManager.currentSession) {
            console.log('  • Current Call: Active');
        } else {
            console.log('  • Current Call: None');
        }
    }

    /**
     * Function to open dialpad manually
     */
    function openDialpad() {
        try {
            const dialpadPanel = document.getElementById('dialpadPanel');
            if (!dialpadPanel) {
                console.error('❌ Dialpad panel not found');
                return false;
            }

            if (!dialpadPanel.classList.contains('hide')) {
                console.log('📱 Dialpad is already open');
                return true;
            }

            // Try to find and click dialpad trigger
            const dialpadTrigger = document.querySelector('[data-target="dialpad"]') ||
                                 document.querySelector('.dial-bottom-item[data-target="dialpad"]');

            if (dialpadTrigger) {
                dialpadTrigger.click();
                console.log('📱 Dialpad opened');
                return true;
            } else {
                // Manually show dialpad
                dialpadPanel.classList.remove('hide');
                console.log('📱 Dialpad opened manually');
                return true;
            }
        } catch (error) {
            console.error('❌ Error opening dialpad:', error.message);
            return false;
        }
    }

    // Expose functions to global scope for console access
    window.callPhone = enhancedPhoneCall;
    window.makeCall = enhancedPhoneCall;
    window.dialAndCall = fillDialpadAndMakeCall;
    window.endCall = endCurrentCall;
    window.showDialpadStatus = showDialpadStatus;
    window.openDialpad = openDialpad;

    // Log availability message
    console.log('📞 Phone call functions loaded! Available commands:');
    console.log('  • callPhone("1234567890") - Fill dialpad and initiate phone call');
    console.log('  • makeCall("1234567890") - Same as callPhone');
    console.log('  • dialAndCall("1234567890") - Basic version');
    console.log('  • endCall() - End current call');
    console.log('  • showDialpadStatus() - Show dialpad and call status');
    console.log('  • openDialpad() - Open dialpad manually');

})();
