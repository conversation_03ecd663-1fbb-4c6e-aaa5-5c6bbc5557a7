/**
 * Generic Manager Module
 * Main module that coordinates all CRUD operations for any entity
 */

import GenericCRUD from './GenericCRUD.js';
import GenericNotifications from './GenericNotifications.js';

class GenericManager {
    constructor(config = {}) {
        this.config = {
            entityName: 'entity',
            entityNamePlural: 'entities',
            selectors: {
                form: '#entityForm',
                addButton: '#addEntityBtn',
                editButton: '#editEntityBtn',
                deleteButton: '#deleteSelectedBtn',
                dataTable: '#entityTable',
                modal: '#entityModal',
                errorContainer: '#errorContainer'
            },
            dataTable: {
                columns: [
                    { name: 'checkbox', orderable: false },
                    { name: 'title', orderable: true },
                    { name: 'description', orderable: true },
                    { name: 'created_at', orderable: true },
                    { name: 'customers_count', orderable: false },
                    { name: 'actions', orderable: false }
                ],
                checkboxName: 'entityIds[]'
            },
            ...config
        };
        
        this.crud = new GenericCRUD(this.config);
        this.notifications = new GenericNotifications(this.config);
        this.dataTable = null;
        this.currentModal = null;
        this.loadData = null;
        
        // Set up CRUD callbacks
        this.crud.setSuccessCallback((operation, response) => {
            this.handleSuccess(operation, response);
        });
        
        this.crud.setErrorCallback((operation, error) => {
            this.handleError(operation, error);
        });
    }

    /**
     * Initialize the manager
     * @param {Object} options - Configuration options
     */
    init(options = {}) {
        // Merge options with default selectors
        this.options = {
            ...this.config.selectors,
            ...options
        };

        this.bindEvents();
        // DataTable initialization is handled in the template to avoid conflicts
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Add button
        const addButton = document.querySelector(this.options.addButton);
        if (addButton) {
            addButton.addEventListener('click', () => this.showCreateModal());
        }

        // Edit button
        const editButton = document.querySelector(this.options.editButton);
        if (editButton) {
            editButton.addEventListener('click', () => this.handleEdit());
        }

        // Delete button
        const deleteButton = document.querySelector(this.options.deleteButton);
        if (deleteButton) {
            deleteButton.addEventListener('click', () => this.handleBulkDelete());
        }

        // Form submission
        const form = document.querySelector(this.options.form);
        if (form) {
            this.crud.init(form);
            
            // Handle form submission
            const submitButton = form.querySelector('#stepNextBtn');
            if (submitButton) {
                submitButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleFormSubmit();
                });
            }
        }

        // Individual edit/delete buttons (delegated event handling)
        document.addEventListener('click', (e) => {
            const editBtn = e.target.closest(`.${this.config.buttonClasses.edit}`);
            const deleteBtn = e.target.closest(`.${this.config.buttonClasses.delete}`);

            if (editBtn) {
                const entityId = editBtn.getAttribute('data-id');
                if (entityId) {
                    this.showEditModal(entityId);
                }
            }

            if (deleteBtn) {
                const entityId = deleteBtn.getAttribute('data-id');
                if (entityId) {
                    this.handleSingleDelete(entityId);
                }
            }
        });
    }

    /**
     * Initialize DataTable (only if not already initialized)
     */
    initializeDataTable() {
        const tableElement = document.querySelector(this.options.dataTable);
        if (tableElement && typeof $ !== 'undefined' && $.fn.DataTable) {
            // Check if DataTable is already initialized
            if (!$.fn.DataTable.isDataTable(tableElement)) {
                this.dataTable = $(tableElement).DataTable({
                    searching: false,
                    pageLength: 10,
                    language: {
                        lengthMenu: '_MENU_',
                        info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.',
                        emptyTable: 'Tabloda herhangi bir veri mevcut değil',
                        zeroRecords: 'Eşleşen kayıt bulunamadı'
                    },
                    columnDefs: [
                        { orderable: false, targets: [0, this.config.dataTable.columns.length - 1] }
                    ]
                });
            } else {
                // DataTable already exists, just get the reference
                this.dataTable = $(tableElement).DataTable();
            }
        }
    }

    /**
     * Show create modal
     */
    showCreateModal() {
        this.crud.setCreateMode();
        this._showModal();
        this._updateModalTitle(`Yeni ${this.config.entityName} Ekle`);
    }

    /**
     * Show edit modal
     * @param {string|number} entityId - Entity ID to edit
     */
    async showEditModal(entityId) {
        try {
            const response = await this.crud.loadForEdit(entityId);
            if (response.success) {
                this._showModal();
                this._updateModalTitle(`${this.config.entityName} Düzenle`);
            } else {
                this.notifications.showError(response.message);
            }
        } catch (error) {
            console.error('Error showing edit modal:', error);
            this.notifications.showError(`${this.config.entityName} düzenleme verisi yüklenirken hata oluştu.`);
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit() {
        try {
            const submitButton = document.querySelector('#stepNextBtn');
            if (submitButton && this.notifications && typeof this.notifications.showLoading === 'function') {
                this.notifications.showLoading(submitButton, 'Kaydediliyor...');
            }

            const response = await this.crud.save();
            
            if (submitButton && this.notifications && typeof this.notifications.hideLoading === 'function') {
                this.notifications.hideLoading(submitButton);
            }

            if (response.success) {
                // Close modal
                if (this.currentModal) {
                    const modal = bootstrap.Modal.getInstance(this.currentModal);
                    if (modal) {
                        modal.hide();
                    }
                }
                
                // Refresh DataTable
                this.refreshDataTable();
            }
        } catch (error) {
            const submitButton = document.querySelector('#stepNextBtn');
            if (submitButton && this.notifications && typeof this.notifications.hideLoading === 'function') {
                this.notifications.hideLoading(submitButton);
            }
            console.error('Form submission error:', error);
        }
    }

    /**
     * Handle edit button click
     */
    handleEdit() {
        const selectedIds = this.getSelectedIds();
        if (selectedIds.length === 1) {
            this.showEditModal(selectedIds[0]);
        } else if (selectedIds.length === 0) {
            this.notifications.showWarning('Lütfen düzenlemek için bir kayıt seçin.');
        } else {
            this.notifications.showWarning('Düzenleme için sadece bir kayıt seçin.');
        }
    }

    /**
     * Handle single entity deletion
     * @param {string|number} entityId - Entity ID to delete
     */
    handleSingleDelete(entityId) {
        const message = `Bu ${this.config.entityName.toLowerCase()}u silmek istediğinizden emin misiniz?`;
        this.notifications.showConfirmation(
            message,
            async () => {
                const response = await this.crud.delete(entityId);
                if (response.success) {
                    this.refreshDataTable();
                }
            }
        );
    }

    /**
     * Handle bulk deletion
     */
    handleBulkDelete() {
        const selectedIds = this.getSelectedIds();
        if (selectedIds.length === 0) {
            this.notifications.showWarning('Lütfen silmek için en az bir kayıt seçin.');
            return;
        }

        const message = `${selectedIds.length} ${this.config.entityName.toLowerCase()}u silmek istediğinizden emin misiniz?`;
        this.notifications.showConfirmation(
            message,
            async () => {
                const response = await this.crud.deleteMultiple(selectedIds);
                if (response.success) {
                    this.refreshDataTable();
                    this.clearSelection();
                }
            }
        );
    }

    /**
     * Get selected row IDs from DataTable
     * @returns {Array} Array of selected IDs
     */
    getSelectedIds() {
        const selectedIds = [];
        const checkboxes = document.querySelectorAll(`input[name="${this.config.dataTable.checkboxName}"]:checked`);
        checkboxes.forEach(checkbox => {
            selectedIds.push(checkbox.value);
        });
        return selectedIds;
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        const checkboxes = document.querySelectorAll(`input[name="${this.config.dataTable.checkboxName}"]`);
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    /**
     * Refresh DataTable
     */
    refreshDataTable() {
        if (this.loadData && typeof this.loadData === 'function') {
            this.loadData();
        } else if (this.dataTable) {
            // Fallback: reload the page
            window.location.reload();
        }
    }

    /**
     * Handle successful operations
     * @param {string} operation - Operation type
     * @param {Object} response - API response
     */
    handleSuccess(operation, response) {
        let message;
        switch (operation) {
            case 'create':
                message = this.notifications.getMessage('createSuccess');
                break;
            case 'update':
                message = this.notifications.getMessage('updateSuccess');
                break;
            case 'delete':
                message = this.notifications.getMessage('deleteSuccess');
                break;
            case 'deleteMultiple':
                message = this.notifications.getMessage('deleteMultipleSuccess', { count: response.data?.count || 'Birden fazla' });
                break;
            default:
                message = response.message || 'İşlem başarılı.';
        }
        
        this.notifications.showSuccess(message);
    }

    /**
     * Handle operation errors
     * @param {string} operation - Operation type
     * @param {Object} error - Error object
     */
    handleError(operation, error) {
        let message;
        switch (operation) {
            case 'create':
                message = this.notifications.getMessage('createError');
                break;
            case 'update':
                message = this.notifications.getMessage('updateError');
                break;
            case 'delete':
                message = this.notifications.getMessage('deleteError');
                break;
            case 'deleteMultiple':
                message = this.notifications.getMessage('deleteError');
                break;
            default:
                message = error.message || this.notifications.getMessage('unknownError');
        }
        
        this.notifications.showError(message);
    }

    /**
     * Show modal
     * @private
     */
    _showModal() {
        const modal = document.querySelector(this.options.modal);
        if (modal) {
            this.currentModal = modal;
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }

    /**
     * Update modal title
     * @param {string} title - New title
     * @private
     */
    _updateModalTitle(title) {
        if (this.currentModal) {
            const titleElement = this.currentModal.querySelector('.modal-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }
    }
}

export default GenericManager;
