/**
 * Generic Notifications Module
 * Handles all notification types for any entity with configurable messages
 */

class GenericNotifications {
    constructor(config = {}) {
        this.config = {
            entityName: 'entity',
            entityNamePlural: 'entities',
            messages: {
                createSuccess: '{entityName} başarıyla oluşturuldu.',
                updateSuccess: '{entityName} başarıyla güncellendi.',
                deleteSuccess: '{entityName} başarıyla silindi.',
                deleteMultipleSuccess: '{count} {entityNamePlural} başarıyla silindi.',
                createError: '{entityName} oluşturulurken bir hata oluştu.',
                updateError: '{entityName} güncellenirken bir hata oluştu.',
                deleteError: '{entityName} silinirken bir hata oluştu.',
                loadError: '{entityNamePlural} yüklenirken bir hata oluştu.',
                validationError: 'Lütfen formu doğru şekilde doldurun.',
                networkError: 'Ağ bağlantısı hatası. Lütfen tekrar deneyin.',
                unknownError: 'Bilinmeyen bir hata oluştu.'
            },
            ...config
        };
    }

    /**
     * Show success notification
     * @param {string} message - Success message
     * @param {number} duration - Display duration in milliseconds
     */
    showSuccess(message, duration = 5000) {
        this._showNotification(message, 'success', duration);
    }

    /**
     * Show error notification
     * @param {string} message - Error message
     * @param {number} duration - Display duration in milliseconds
     */
    showError(message, duration = 8000) {
        this._showNotification(message, 'error', duration);
    }

    /**
     * Show warning notification
     * @param {string} message - Warning message
     * @param {number} duration - Display duration in milliseconds
     */
    showWarning(message, duration = 6000) {
        this._showNotification(message, 'warning', duration);
    }

    /**
     * Show info notification
     * @param {string} message - Info message
     * @param {number} duration - Display duration in milliseconds
     */
    showInfo(message, duration = 5000) {
        this._showNotification(message, 'info', duration);
    }

    /**
     * Show loading state on element
     * @param {HTMLElement} element - Element to show loading on
     * @param {string} message - Loading message
     */
    showLoading(element, message = 'Yükleniyor...') {
        if (!element) return;

        element.disabled = true;
        const originalText = element.textContent;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            ${message}
        `;
    }

    /**
     * Hide loading state from element
     * @param {HTMLElement} element - Element to hide loading from
     */
    hideLoading(element) {
        if (!element) return;

        element.disabled = false;
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.textContent = originalText;
            element.removeAttribute('data-original-text');
        }
    }

    /**
     * Show confirmation dialog
     * @param {string} message - Confirmation message
     * @param {Function} onConfirm - Callback for confirmation
     * @param {Function} onCancel - Callback for cancellation
     */
    showConfirmation(message, onConfirm, onCancel) {
        // Use the global delete confirmation modal
        const modal = document.getElementById('deleteConfirmModal');
        if (!modal) {
            console.error('Delete confirmation modal not found');
            return;
        }

        // Update modal content if needed
        const messageElement = modal.querySelector('.modal-body p');
        if (messageElement) {
            messageElement.textContent = message;
        }

        // Set up event handlers
        const confirmBtn = modal.querySelector('#confirmDeleteBtn');
        if (confirmBtn && onConfirm) {
            // Remove existing event listeners
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            
            newConfirmBtn.addEventListener('click', () => {
                onConfirm();
                bootstrap.Modal.getInstance(modal).hide();
            });
        }

        const cancelBtn = modal.querySelector('[data-bs-dismiss="modal"]');
        if (cancelBtn && onCancel) {
            cancelBtn.addEventListener('click', onCancel, { once: true });
        }

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    /**
     * Get formatted message with entity name replacement
     * @param {string} messageKey - Message key from config
     * @param {Object} replacements - Additional replacements
     * @returns {string} Formatted message
     */
    getMessage(messageKey, replacements = {}) {
        let message = this.config.messages[messageKey] || messageKey;
        
        // Replace entity name placeholders
        message = message.replace('{entityName}', this.config.entityName);
        message = message.replace('{entityNamePlural}', this.config.entityNamePlural);
        
        // Replace additional placeholders
        Object.keys(replacements).forEach(key => {
            message = message.replace(`{${key}}`, replacements[key]);
        });
        
        return message;
    }

    /**
     * Show notification with specific type
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Display duration in milliseconds
     * @private
     */
    _showNotification(message, type, duration) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            max-width: 400px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;

        // Set background color based on type
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add message
        notification.textContent = message;

        // Add to DOM
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto hide
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);

        // Click to dismiss
        notification.addEventListener('click', () => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }
}

export default GenericNotifications;
