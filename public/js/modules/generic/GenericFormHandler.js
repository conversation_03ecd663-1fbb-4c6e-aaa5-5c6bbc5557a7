/**
 * Generic Form Handler Module
 * Handles form validation and data extraction for any entity
 */

class GenericFormHandler {
    constructor(config = {}) {
        this.config = {
            entityName: 'entity',
            fields: {
                title: { required: true, type: 'text' },
                description: { required: false, type: 'text' }
            },
            validationRules: {},
            ...config
        };
        
        this.form = null;
        this.errors = {};
    }

    /**
     * Initialize form handler with form element
     * @param {HTMLElement} formElement - Form element
     */
    init(formElement) {
        this.form = formElement;
        this.bindValidationEvents();
    }

    /**
     * Extract form data
     * @returns {Object} Form data object
     */
    getFormData() {
        if (!this.form) {
            console.error('Form not initialized');
            return {};
        }

        const formData = {};
        const formDataObj = new FormData(this.form);

        // Extract data based on field configuration
        Object.keys(this.config.fields).forEach(fieldName => {
            const fieldConfig = this.config.fields[fieldName];
            let value = formDataObj.get(fieldName);

            // Type conversion
            if (fieldConfig.type === 'number' && value) {
                value = Number(value);
            } else if (fieldConfig.type === 'boolean') {
                value = Boolean(value);
            } else if (fieldConfig.type === 'array' && value) {
                value = Array.isArray(value) ? value : [value];
            }

            formData[fieldName] = value;
        });

        return formData;
    }

    /**
     * Populate form with data
     * @param {Object} data - Data to populate form with
     */
    populateForm(data) {
        if (!this.form || !data) return;

        Object.keys(this.config.fields).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field && data[fieldName] !== undefined) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(data[fieldName]);
                } else if (field.type === 'radio') {
                    const radioButton = this.form.querySelector(`[name="${fieldName}"][value="${data[fieldName]}"]`);
                    if (radioButton) radioButton.checked = true;
                } else {
                    field.value = data[fieldName] || '';
                }
            }
        });
    }

    /**
     * Clear form data
     */
    clearForm() {
        if (!this.form) return;

        this.form.reset();
        this.clearErrors();
    }

    /**
     * Validate form data
     * @param {Object} data - Data to validate
     * @returns {boolean} True if valid, false otherwise
     */
    validateForm(data = null) {
        if (!data) {
            data = this.getFormData();
        }

        this.errors = {};
        let isValid = true;

        // Validate each field
        Object.keys(this.config.fields).forEach(fieldName => {
            const fieldConfig = this.config.fields[fieldName];
            const value = data[fieldName];

            // Required field validation
            if (fieldConfig.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
                this.errors[fieldName] = [`${fieldName} alanı zorunludur.`];
                isValid = false;
            }

            // Type validation
            if (value && fieldConfig.type === 'email' && !this._isValidEmail(value)) {
                this.errors[fieldName] = this.errors[fieldName] || [];
                this.errors[fieldName].push('Geçerli bir e-posta adresi girin.');
                isValid = false;
            }

            // Length validation
            if (value && fieldConfig.minLength && value.length < fieldConfig.minLength) {
                this.errors[fieldName] = this.errors[fieldName] || [];
                this.errors[fieldName].push(`En az ${fieldConfig.minLength} karakter olmalıdır.`);
                isValid = false;
            }

            if (value && fieldConfig.maxLength && value.length > fieldConfig.maxLength) {
                this.errors[fieldName] = this.errors[fieldName] || [];
                this.errors[fieldName].push(`En fazla ${fieldConfig.maxLength} karakter olmalıdır.`);
                isValid = false;
            }

            // Custom validation rules
            if (fieldConfig.customValidation && typeof fieldConfig.customValidation === 'function') {
                const customResult = fieldConfig.customValidation(value, data);
                if (customResult !== true) {
                    this.errors[fieldName] = this.errors[fieldName] || [];
                    this.errors[fieldName].push(customResult);
                    isValid = false;
                }
            }
        });

        return isValid;
    }

    /**
     * Display validation errors
     * @param {Object} errors - Error object from server or validation
     */
    displayErrors(errors = null) {
        if (!errors) {
            errors = this.errors;
        }

        // Clear previous errors
        this.clearErrors();

        // Display new errors
        Object.keys(errors).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                const errorMessages = Array.isArray(errors[fieldName]) ? errors[fieldName] : [errors[fieldName]];
                this._showFieldError(field, errorMessages);
            }
        });
    }

    /**
     * Clear all validation errors
     */
    clearErrors() {
        if (!this.form) return;

        // Remove error classes and messages
        this.form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });

        this.form.querySelectorAll('.invalid-feedback').forEach(errorElement => {
            errorElement.remove();
        });
    }

    /**
     * Bind validation events to form fields
     * @private
     */
    bindValidationEvents() {
        if (!this.form) return;

        Object.keys(this.config.fields).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.addEventListener('blur', () => {
                    this._validateField(fieldName);
                });

                field.addEventListener('input', () => {
                    // Clear error on input
                    this._clearFieldError(field);
                });
            }
        });
    }

    /**
     * Validate a single field
     * @param {string} fieldName - Field name to validate
     * @private
     */
    _validateField(fieldName) {
        const data = this.getFormData();
        const fieldConfig = this.config.fields[fieldName];
        const value = data[fieldName];
        const field = this.form.querySelector(`[name="${fieldName}"]`);

        if (!field || !fieldConfig) return;

        const errors = [];

        // Required validation
        if (fieldConfig.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
            errors.push(`${fieldName} alanı zorunludur.`);
        }

        // Show errors if any
        if (errors.length > 0) {
            this._showFieldError(field, errors);
        } else {
            this._clearFieldError(field);
        }
    }

    /**
     * Show error for a specific field
     * @param {HTMLElement} field - Field element
     * @param {Array} errorMessages - Array of error messages
     * @private
     */
    _showFieldError(field, errorMessages) {
        field.classList.add('is-invalid');

        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback';
        errorElement.textContent = errorMessages[0]; // Show first error
        field.parentNode.appendChild(errorElement);
    }

    /**
     * Clear error for a specific field
     * @param {HTMLElement} field - Field element
     * @private
     */
    _clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorElement = field.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} True if valid email
     * @private
     */
    _isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

export default GenericFormHandler;
