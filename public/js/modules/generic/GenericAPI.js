/**
 * Generic API Service Module
 * Handles all HTTP requests for any entity with configurable endpoints
 */

class GenericAPI {
    constructor(config) {
        this.config = {
            baseURL: '/api/entities',
            entityName: 'entity',
            endpoints: {
                getAll: '',
                getById: '/{id}',
                create: '',
                update: '/{id}',
                delete: '/{id}',
                deleteMultiple: ''
            },
            ...config
        };
        
        this.baseURL = this.config.baseURL;
        this.axios = window.axios;
        
        // Set up default headers
        this.axios.defaults.headers.common['Accept'] = 'application/json';
        this.axios.defaults.headers.common['Content-Type'] = 'application/json';
        
        // Add CSRF token if available
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            this.axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }
    }

    /**
     * Get all entities
     * @returns {Promise<Object>} API response
     */
    async getAll() {
        try {
            const url = this.baseURL + this.config.endpoints.getAll;
            const response = await this.axios.get(url);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Get a single entity by ID
     * @param {string|number} id - Entity ID
     * @returns {Promise<Object>} API response
     */
    async getById(id) {
        try {
            const url = this.baseURL + this.config.endpoints.getById.replace('{id}', id);
            const response = await this.axios.get(url);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Create a new entity
     * @param {Object} data - Entity data
     * @returns {Promise<Object>} API response
     */
    async create(data) {
        try {
            const url = this.baseURL + this.config.endpoints.create;
            const response = await this.axios.post(url, data);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Update an existing entity
     * @param {string|number} id - Entity ID
     * @param {Object} data - Updated entity data
     * @returns {Promise<Object>} API response
     */
    async update(id, data) {
        try {
            const url = this.baseURL + this.config.endpoints.update.replace('{id}', id);
            const response = await this.axios.put(url, data);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Delete an entity
     * @param {string|number} id - Entity ID
     * @returns {Promise<Object>} API response
     */
    async delete(id) {
        try {
            const url = this.baseURL + this.config.endpoints.delete.replace('{id}', id);
            const response = await this.axios.delete(url);
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Delete multiple entities
     * @param {Array} ids - Array of entity IDs
     * @returns {Promise<Object>} API response
     */
    async deleteMultiple(ids) {
        try {
            const url = this.baseURL + this.config.endpoints.deleteMultiple;
            const response = await this.axios.delete(url, { data: { ids } });
            return this._handleResponse(response);
        } catch (error) {
            return this._handleError(error);
        }
    }

    /**
     * Handle successful API response
     * @param {Object} response - Axios response object
     * @returns {Object} Formatted response
     * @private
     */
    _handleResponse(response) {
        return {
            success: true,
            data: response.data,
            status: response.status,
            message: response.data.message || `${this.config.entityName} işlemi başarılı.`
        };
    }

    /**
     * Handle API error
     * @param {Object} error - Axios error object
     * @returns {Object} Formatted error response
     * @private
     */
    _handleError(error) {
        console.error(`${this.config.entityName} API Error:`, error);
        
        if (error.response) {
            // Server responded with error status
            const { status, data } = error.response;
            return {
                success: false,
                status,
                message: data.message || `${this.config.entityName} işleminde hata oluştu.`,
                errors: data.errors || {},
                data: data
            };
        } else if (error.request) {
            // Request was made but no response received
            return {
                success: false,
                status: 0,
                message: 'Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.',
                errors: {},
                data: null
            };
        } else {
            // Something else happened
            return {
                success: false,
                status: 0,
                message: 'Beklenmeyen bir hata oluştu.',
                errors: {},
                data: null
            };
        }
    }
}

export default GenericAPI;
