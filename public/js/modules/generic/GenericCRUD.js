/**
 * Generic CRUD Operations Module
 * Handles Create, Read, Update, Delete operations for any entity
 */

import GenericAPI from './GenericAPI.js';
import GenericFormHandler from './GenericFormHandler.js';

class GenericCRUD {
    constructor(config = {}) {
        this.config = {
            entityName: 'entity',
            ...config
        };
        
        this.api = new GenericAPI(this.config);
        this.formHandler = new GenericFormHandler(this.config);
        this.currentId = null;
        this.isEditMode = false;
        
        // Callbacks
        this.successCallback = null;
        this.errorCallback = null;
    }

    /**
     * Initialize CRUD with form element
     * @param {HTMLElement} formElement - Form element
     */
    init(formElement) {
        this.formHandler.init(formElement);
    }

    /**
     * Set success callback
     * @param {Function} callback - Success callback function
     */
    setSuccessCallback(callback) {
        this.successCallback = callback;
    }

    /**
     * Set error callback
     * @param {Function} callback - Error callback function
     */
    setErrorCallback(callback) {
        this.errorCallback = callback;
    }

    /**
     * Get all entities
     * @returns {Promise<Object>} API response
     */
    async getAll() {
        try {
            const response = await this.api.getAll();
            if (response.success && this.successCallback) {
                this.successCallback('read', response);
            }
            return response;
        } catch (error) {
            console.error('Error in getAll:', error);
            if (this.errorCallback) {
                this.errorCallback('read', error);
            }
            return { success: false, message: 'Veri yüklenirken hata oluştu.' };
        }
    }

    /**
     * Get entity by ID
     * @param {string|number} id - Entity ID
     * @returns {Promise<Object>} API response
     */
    async getById(id) {
        try {
            const response = await this.api.getById(id);
            if (response.success && this.successCallback) {
                this.successCallback('read', response);
            }
            return response;
        } catch (error) {
            console.error('Error in getById:', error);
            if (this.errorCallback) {
                this.errorCallback('read', error);
            }
            return { success: false, message: 'Veri yüklenirken hata oluştu.' };
        }
    }

    /**
     * Save entity (create or update based on current mode)
     * @param {Object} data - Optional data to save (if not provided, will extract from form)
     * @returns {Promise<Object>} API response
     */
    async save(data = null) {
        try {
            // Get form data if not provided
            if (!data) {
                data = this.formHandler.getFormData();
            }

            // Validate form data
            if (!this.formHandler.validateForm(data)) {
                this.formHandler.displayErrors();
                return {
                    success: false,
                    message: 'Form doğrulama hatası.',
                    errors: this.formHandler.errors
                };
            }

            let response;
            let operation;

            if (this.isEditMode && this.currentId) {
                // Update existing entity
                response = await this.api.update(this.currentId, data);
                operation = 'update';
            } else {
                // Create new entity
                response = await this.api.create(data);
                operation = 'create';
            }

            if (response.success) {
                // Clear form on success
                this.formHandler.clearForm();
                this.resetMode();
                
                if (this.successCallback) {
                    this.successCallback(operation, response);
                }
            } else {
                // Display server validation errors
                if (response.errors) {
                    this.formHandler.displayErrors(response.errors);
                }
                
                if (this.errorCallback) {
                    this.errorCallback(operation, response);
                }
            }

            return response;
        } catch (error) {
            console.error('Error in save:', error);
            const operation = this.isEditMode ? 'update' : 'create';
            if (this.errorCallback) {
                this.errorCallback(operation, error);
            }
            return { success: false, message: 'Kaydetme işleminde hata oluştu.' };
        }
    }

    /**
     * Delete entity by ID
     * @param {string|number} id - Entity ID
     * @returns {Promise<Object>} API response
     */
    async delete(id) {
        try {
            const response = await this.api.delete(id);
            
            if (response.success && this.successCallback) {
                this.successCallback('delete', response);
            } else if (!response.success && this.errorCallback) {
                this.errorCallback('delete', response);
            }
            
            return response;
        } catch (error) {
            console.error('Error in delete:', error);
            if (this.errorCallback) {
                this.errorCallback('delete', error);
            }
            return { success: false, message: 'Silme işleminde hata oluştu.' };
        }
    }

    /**
     * Delete multiple entities
     * @param {Array} ids - Array of entity IDs
     * @returns {Promise<Object>} API response
     */
    async deleteMultiple(ids) {
        try {
            const response = await this.api.deleteMultiple(ids);
            
            if (response.success && this.successCallback) {
                this.successCallback('deleteMultiple', response);
            } else if (!response.success && this.errorCallback) {
                this.errorCallback('deleteMultiple', response);
            }
            
            return response;
        } catch (error) {
            console.error('Error in deleteMultiple:', error);
            if (this.errorCallback) {
                this.errorCallback('deleteMultiple', error);
            }
            return { success: false, message: 'Toplu silme işleminde hata oluştu.' };
        }
    }

    /**
     * Set edit mode with entity ID
     * @param {string|number} id - Entity ID to edit
     */
    setEditMode(id) {
        this.isEditMode = true;
        this.currentId = id;
    }

    /**
     * Set create mode
     */
    setCreateMode() {
        this.isEditMode = false;
        this.currentId = null;
        this.formHandler.clearForm();
    }

    /**
     * Reset mode to default
     */
    resetMode() {
        this.isEditMode = false;
        this.currentId = null;
    }

    /**
     * Load entity data for editing
     * @param {string|number} id - Entity ID
     * @returns {Promise<Object>} API response
     */
    async loadForEdit(id) {
        try {
            this.setEditMode(id);
            const response = await this.getById(id);
            
            if (response.success && response.data) {
                this.formHandler.populateForm(response.data);
            }
            
            return response;
        } catch (error) {
            console.error('Error in loadForEdit:', error);
            return { success: false, message: 'Düzenleme verisi yüklenirken hata oluştu.' };
        }
    }

    /**
     * Check if currently in edit mode
     * @returns {boolean} True if in edit mode
     */
    isEditing() {
        return this.isEditMode;
    }

    /**
     * Get current entity ID (if in edit mode)
     * @returns {string|number|null} Current entity ID
     */
    getCurrentId() {
        return this.currentId;
    }
}

export default GenericCRUD;
