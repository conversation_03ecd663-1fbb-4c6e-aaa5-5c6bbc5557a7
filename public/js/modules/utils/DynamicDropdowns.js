/**
 * Dynamic Dropdowns Utility Module
 * Handles loading and populating dropdowns with data from APIs
 */

class DynamicDropdowns {
    constructor() {
        this.cache = new Map();
        this.loadingStates = new Map();
    }

    /**
     * Load data for a dropdown from API
     * @param {string} apiUrl - API endpoint URL
     * @param {string} cacheKey - Cache key for storing data
     * @returns {Promise<Array>} Array of dropdown data
     */
    async loadData(apiUrl, cacheKey = null) {
        const key = cacheKey || apiUrl;
        
        // Return cached data if available
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }

        // Prevent multiple simultaneous requests
        if (this.loadingStates.has(key)) {
            return this.loadingStates.get(key);
        }

        try {
            const loadPromise = this._fetchData(apiUrl);
            this.loadingStates.set(key, loadPromise);
            
            const data = await loadPromise;
            
            // Cache the data
            this.cache.set(key, data);
            this.loadingStates.delete(key);
            
            return data;
        } catch (error) {
            this.loadingStates.delete(key);
            throw error;
        }
    }

    /**
     * Populate a dropdown with data
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {Array} data - Array of data objects
     * @param {Object} options - Configuration options
     */
    populateDropdown(selector, data, options = {}) {
        const dropdown = typeof selector === 'string' ? document.querySelector(selector) : selector;
        if (!dropdown) {
            console.warn(`Dropdown not found: ${selector}`);
            return;
        }

        const config = {
            valueField: 'id',
            textField: 'title',
            placeholder: 'Seçiniz...',
            clearExisting: true,
            addEmptyOption: true,
            ...options
        };

        // Clear existing options if requested
        if (config.clearExisting) {
            dropdown.innerHTML = '';
        }

        // Add placeholder option
        if (config.addEmptyOption) {
            const placeholderOption = document.createElement('option');
            placeholderOption.value = '';
            placeholderOption.textContent = config.placeholder;
            placeholderOption.disabled = true;
            placeholderOption.selected = true;
            dropdown.appendChild(placeholderOption);
        }

        // Add data options
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item[config.valueField];
            option.textContent = item[config.textField] || item.name || item.title;
            dropdown.appendChild(option);
        });

        // Trigger change event
        dropdown.dispatchEvent(new Event('change'));
    }

    /**
     * Show loading state for dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {string} loadingText - Loading text to display
     */
    showLoading(selector, loadingText = 'Yükleniyor...') {
        const dropdown = typeof selector === 'string' ? document.querySelector(selector) : selector;
        if (!dropdown) return;

        dropdown.disabled = true;
        dropdown.innerHTML = `<option selected disabled>${loadingText}</option>`;
    }

    /**
     * Show error state for dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {string} errorText - Error text to display
     */
    showError(selector, errorText = 'Yükleme hatası') {
        const dropdown = typeof selector === 'string' ? document.querySelector(selector) : selector;
        if (!dropdown) return;

        dropdown.disabled = false;
        dropdown.innerHTML = `<option selected disabled>${errorText}</option>`;
    }

    /**
     * Load and populate customer groups dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {Object} options - Configuration options
     */
    async loadCustomerGroups(selector, options = {}) {
        try {
            this.showLoading(selector, 'Gruplar yükleniyor...');
            const data = await this.loadData('/api/customer-groups', 'customer-groups');
            this.populateDropdown(selector, data, {
                placeholder: 'Grup Seçin...',
                ...options
            });
        } catch (error) {
            console.error('Error loading customer groups:', error);
            this.showError(selector, 'Grup yükleme hatası');
        }
    }

    /**
     * Load and populate customer types dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {Object} options - Configuration options
     */
    async loadCustomerTypes(selector, options = {}) {
        try {
            this.showLoading(selector, 'Tipler yükleniyor...');
            const data = await this.loadData('/api/customer-types', 'customer-types');
            this.populateDropdown(selector, data, {
                placeholder: 'Tip Seçin...',
                ...options
            });
        } catch (error) {
            console.error('Error loading customer types:', error);
            this.showError(selector, 'Tip yükleme hatası');
        }
    }

    /**
     * Load and populate customer criteria dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {Object} options - Configuration options
     */
    async loadCustomerCriteria(selector, options = {}) {
        try {
            this.showLoading(selector, 'Kriterler yükleniyor...');
            const data = await this.loadData('/api/customer-criteria', 'customer-criteria');
            this.populateDropdown(selector, data, {
                placeholder: 'Kriter Seçin...',
                ...options
            });
        } catch (error) {
            console.error('Error loading customer criteria:', error);
            this.showError(selector, 'Kriter yükleme hatası');
        }
    }

    /**
     * Load and populate agents dropdown
     * @param {string|HTMLElement} selector - Dropdown selector or element
     * @param {Object} options - Configuration options
     */
    async loadAgents(selector, options = {}) {
        try {
            this.showLoading(selector, 'Agentlar yükleniyor...');
            const data = await this.loadData('/api/users/agents', 'agents');
            this.populateDropdown(selector, data, {
                textField: 'name',
                placeholder: 'Y.Agent Seçin...',
                ...options
            });
        } catch (error) {
            console.error('Error loading agents:', error);
            this.showError(selector, 'Agent yükleme hatası');
        }
    }

    /**
     * Load all customer-related dropdowns at once
     * @param {Object} selectors - Object containing dropdown selectors
     */
    async loadAllCustomerDropdowns(selectors = {}) {
        const defaultSelectors = {
            groups: '#chooseGroup, #customerGroup, #modalGroup',
            types: '#chooseType, #customerType, #modalType',
            criteria: '#chooseCriteria, #customerCriteria, #modalCriteria',
            agents: '#chooseAgent, #officialAgent, #modalAgent'
        };

        const finalSelectors = { ...defaultSelectors, ...selectors };

        // Load all dropdowns in parallel
        const promises = [];

        if (finalSelectors.groups) {
            const groupElements = document.querySelectorAll(finalSelectors.groups);
            groupElements.forEach(element => {
                promises.push(this.loadCustomerGroups(element));
            });
        }

        if (finalSelectors.types) {
            const typeElements = document.querySelectorAll(finalSelectors.types);
            typeElements.forEach(element => {
                promises.push(this.loadCustomerTypes(element));
            });
        }

        if (finalSelectors.criteria) {
            const criteriaElements = document.querySelectorAll(finalSelectors.criteria);
            criteriaElements.forEach(element => {
                promises.push(this.loadCustomerCriteria(element));
            });
        }

        if (finalSelectors.agents) {
            const agentElements = document.querySelectorAll(finalSelectors.agents);
            agentElements.forEach(element => {
                promises.push(this.loadAgents(element));
            });
        }

        try {
            await Promise.allSettled(promises);
            console.log('All customer dropdowns loaded');
        } catch (error) {
            console.error('Error loading some dropdowns:', error);
        }
    }

    /**
     * Clear cache for specific key or all cache
     * @param {string} key - Cache key to clear (optional)
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * Fetch data from API
     * @param {string} apiUrl - API endpoint URL
     * @returns {Promise<Array>} Array of data
     * @private
     */
    async _fetchData(apiUrl) {
        const response = await window.axios.get(apiUrl);
        
        if (response.data.success) {
            return response.data.data || [];
        } else {
            throw new Error(response.data.message || 'API request failed');
        }
    }
}

// Export as singleton
export default new DynamicDropdowns();
