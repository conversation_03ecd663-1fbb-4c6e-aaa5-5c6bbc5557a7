/**
 * Customer Notifications and Error Handling Module
 * Handles user-friendly error display and success notifications
 */

class CustomerNotifications {
    constructor() {
        this.notificationContainer = null;
        this.defaultDuration = 5000; // 5 seconds
        this.init();
    }

    /**
     * Initialize notification system
     */
    init() {
        this.createNotificationContainer();
    }

    /**
     * Create notification container if it doesn't exist
     */
    createNotificationContainer() {
        let container = document.getElementById('customer-notifications');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'customer-notifications';
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                width: 100%;
            `;
            document.body.appendChild(container);
        }
        
        this.notificationContainer = container;
    }

    /**
     * Show success notification
     * @param {string} message - Success message
     * @param {number} duration - Duration in milliseconds
     */
    showSuccess(message, duration = this.defaultDuration) {
        this.showNotification(message, 'success', duration);
    }

    /**
     * Show error notification
     * @param {string} message - Error message
     * @param {number} duration - Duration in milliseconds
     */
    showError(message, duration = this.defaultDuration) {
        this.showNotification(message, 'error', duration);
    }

    /**
     * Show warning notification
     * @param {string} message - Warning message
     * @param {number} duration - Duration in milliseconds
     */
    showWarning(message, duration = this.defaultDuration) {
        this.showNotification(message, 'warning', duration);
    }

    /**
     * Show info notification
     * @param {string} message - Info message
     * @param {number} duration - Duration in milliseconds
     */
    showInfo(message, duration = this.defaultDuration) {
        this.showNotification(message, 'info', duration);
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showNotification(message, type = 'info', duration = this.defaultDuration) {
        const notification = this.createNotificationElement(message, type);
        this.notificationContainer.appendChild(notification);

        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    /**
     * Create notification element
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     * @returns {HTMLElement} Notification element
     */
    createNotificationElement(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724', icon: 'hugeicons:checkmark-circle-02' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24', icon: 'hugeicons:cancel-circle' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404', icon: 'hugeicons:alert-triangle' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460', icon: 'hugeicons:information-circle' }
        };

        const color = colors[type] || colors.info;

        notification.style.cssText = `
            background-color: ${color.bg};
            border: 1px solid ${color.border};
            color: ${color.text};
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            font-family: 'Open Sans', sans-serif;
            font-size: 14px;
            line-height: 1.4;
        `;

        notification.innerHTML = `
            <span class="iconify notification-icon" data-icon="${color.icon}" style="font-size: 20px; margin-right: 10px; flex-shrink: 0;"></span>
            <span class="notification-message" style="flex: 1;">${message}</span>
            <button class="notification-close" style="background: none; border: none; color: ${color.text}; font-size: 18px; cursor: pointer; margin-left: 10px; padding: 0; line-height: 1;">
                <span class="iconify" data-icon="hugeicons:cancel-01"></span>
            </button>
        `;

        // Add close functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // Add show class for animation
        notification.classList.add('notification-hidden');

        return notification;
    }

    /**
     * Remove notification
     * @param {HTMLElement} notification - Notification element to remove
     */
    removeNotification(notification) {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * Clear all notifications
     */
    clearAll() {
        const notifications = this.notificationContainer.querySelectorAll('.notification');
        notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }

    /**
     * Handle CRUD operation response
     * @param {string} operation - Operation type (create, update, delete, etc.)
     * @param {Object} response - API response
     */
    handleCRUDResponse(operation, response) {
        const operationMessages = {
            create: {
                success: 'Müşteri başarıyla oluşturuldu.',
                error: 'Müşteri oluşturulurken bir hata oluştu.'
            },
            update: {
                success: 'Müşteri başarıyla güncellendi.',
                error: 'Müşteri güncellenirken bir hata oluştu.'
            },
            delete: {
                success: 'Müşteri başarıyla silindi.',
                error: 'Müşteri silinirken bir hata oluştu.'
            },
            deleteMultiple: {
                success: 'Seçilen müşteriler başarıyla silindi.',
                error: 'Müşteriler silinirken bir hata oluştu.'
            },
            read: {
                error: 'Müşteri bilgileri alınırken bir hata oluştu.'
            },
            readAll: {
                error: 'Müşteri listesi alınırken bir hata oluştu.'
            }
        };

        const messages = operationMessages[operation] || {};

        if (response.success) {
            const message = response.message || messages.success || 'İşlem başarılı.';
            this.showSuccess(message);
        } else {
            const message = response.message || messages.error || 'Bir hata oluştu.';
            this.showError(message);
        }
    }

    /**
     * Display validation errors in a modal or container
     * @param {Object} errors - Validation errors object
     * @param {HTMLElement} container - Container to display errors (optional)
     */
    displayValidationErrors(errors, container = null) {
        if (!errors || Object.keys(errors).length === 0) {
            return;
        }

        let errorContainer = container;
        
        if (!errorContainer) {
            // Create a temporary modal for errors
            errorContainer = this.createErrorModal();
        }

        const errorList = document.createElement('div');
        errorList.className = 'validation-errors';
        errorList.innerHTML = '<h6 class="text-danger mb-3">Lütfen aşağıdaki hataları düzeltin:</h6>';

        const list = document.createElement('ul');
        list.className = 'list-unstyled';

        Object.entries(errors).forEach(([field, fieldErrors]) => {
            const errorArray = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];
            errorArray.forEach(error => {
                const li = document.createElement('li');
                li.className = 'text-danger mb-1';
                li.innerHTML = `<span class="iconify" data-icon="hugeicons:cancel-circle" style="margin-right: 5px;"></span>${error}`;
                list.appendChild(li);
            });
        });

        errorList.appendChild(list);
        errorContainer.innerHTML = '';
        errorContainer.appendChild(errorList);

        if (!container) {
            // Show the modal
            const modal = new bootstrap.Modal(errorContainer.closest('.modal'));
            modal.show();
        }
    }

    /**
     * Create error modal
     * @returns {HTMLElement} Error modal container
     */
    createErrorModal() {
        let modal = document.getElementById('customer-error-modal');
        
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'customer-error-modal';
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Doğrulama Hataları</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="customer-error-content">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tamam</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        return modal.querySelector('#customer-error-content');
    }

    /**
     * Show loading state
     * @param {HTMLElement} element - Element to show loading on
     * @param {string} message - Loading message
     */
    showLoading(element, message = 'Yükleniyor...') {
        if (!element) return;

        element.disabled = true;
        const originalText = element.textContent;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            ${message}
        `;
    }

    /**
     * Hide loading state
     * @param {HTMLElement} element - Element to hide loading from
     */
    hideLoading(element) {
        if (!element) return;

        element.disabled = false;
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.textContent = originalText;
            element.removeAttribute('data-original-text');
        }
    }

    /**
     * Show confirmation dialog
     * @param {string} message - Confirmation message
     * @param {Function} onConfirm - Callback for confirmation
     * @param {Function} onCancel - Callback for cancellation
     */
    showConfirmation(message, onConfirm, onCancel = null) {
        // Use the existing global delete confirmation modal
        const modal = document.getElementById('deleteConfirmModal');
        if (modal) {
            const messageElement = modal.querySelector('.modal-body h5');
            if (messageElement) {
                messageElement.textContent = message;
            }

            const confirmBtn = modal.querySelector('#confirmDeleteBtn');
            if (confirmBtn) {
                // Remove existing event listeners
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
                
                // Add new event listener
                newConfirmBtn.addEventListener('click', () => {
                    if (onConfirm) onConfirm();
                    bootstrap.Modal.getInstance(modal).hide();
                });
            }

            const cancelBtn = modal.querySelector('[data-bs-dismiss="modal"]');
            if (cancelBtn && onCancel) {
                cancelBtn.addEventListener('click', onCancel, { once: true });
            }

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }
}

// Add CSS for show animation
const style = document.createElement('style');
style.textContent = `
    .notification.show {
        opacity: 1 !important;
        transform: translateX(0) !important;
    }
`;
document.head.appendChild(style);

// Export as singleton
export default new CustomerNotifications();
