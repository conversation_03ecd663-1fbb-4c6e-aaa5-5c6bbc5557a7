/**
 * Customer Form Handler Module
 * Handles form data collection and error display
 * Validation is handled exclusively by server-side Laravel validation
 */

class CustomerFormHandler {
    constructor() {
        this.currentForm = null;
        this.errorContainer = null;
    }

    /**
     * Initialize form handler with a specific form
     * @param {HTMLFormElement|string} form - Form element or selector
     * @param {HTMLElement|string} errorContainer - Error container element or selector
     */
    init(form, errorContainer = null) {
        this.currentForm = typeof form === 'string' ? document.querySelector(form) : form;
        this.errorContainer = errorContainer ? 
            (typeof errorContainer === 'string' ? document.querySelector(errorContainer) : errorContainer) : 
            null;

        if (!this.currentForm) {
            throw new Error('Form element not found');
        }

        // Real-time validation removed - validation is now handled exclusively by server-side Laravel validation
    }

    /**
     * Collect form data
     * @param {HTMLFormElement} form - Form to collect data from (optional, uses current form if not provided)
     * @returns {Object} Form data object
     */
    collectFormData(form = null) {
        const targetForm = form || this.currentForm;
        if (!targetForm) {
            throw new Error('No form available to collect data from');
        }

        const formData = new FormData(targetForm);
        const data = {};

        // Map form field names to API field names
        const fieldMapping = {
            'customerName': 'first_name',
            'customerSurname': 'last_name',
            'customerPhone': 'phone',
            'customerIdentity': 'identity_no',
            'customerBirthDate': 'birth_day',
            'customerBirthPlace': 'birth_place',
            'customerCompany': 'company_id',
            'customerGroup': 'customer_group_id',
            'customerType': 'customer_type_id',
            'customerCriteria': 'customer_criteria_id',
            'officialAgent': 'agent_id',
            'customerStatus': 'status',
            'externalCustomerId': 'oid',
            'ivrTarget': 'dynamic_ivr_id',
            'customerDescription': 'description',
            'customerLandline': 'gsm',
            'customerFax': 'fax',
            'customerMail': 'email',
            'customerAddress': 'address',
            'customerCity': 'city',
            'customerDistrict': 'neighborhood',
            'customerCountry': 'country',
            'customerTaxOffice': 'tax_office',
            'customerTaxNumber': 'tax_no'
        };

        // Collect regular form fields
        for (const [key, value] of formData.entries()) {
            const apiFieldName = fieldMapping[key] || key;
            data[apiFieldName] = value === '' ? null : value;
        }

        // Handle checkboxes separately (they won't be in FormData if unchecked)
        const callCheckbox = targetForm.querySelector('input[name="customerCallAble"]');
        if (callCheckbox) {
            data['can_receive_call'] = callCheckbox.checked ? 1 : 0;
        }

        const smsCheckbox = targetForm.querySelector('input[name="customerSmsAble"]');
        if (smsCheckbox) {
            data['can_receive_sms'] = smsCheckbox.checked ? 1 : 0;
        }

        // Convert numeric fields
        const numericFields = ['company_id', 'customer_group_id', 'customer_type_id', 'customer_criteria_id', 'agent_id', 'dynamic_ivr_id', 'status'];
        numericFields.forEach(field => {
            if (data[field] !== null && data[field] !== undefined && data[field] !== '') {
                const numValue = parseInt(data[field]);
                data[field] = isNaN(numValue) ? null : numValue;
            } else if (data[field] === '') {
                data[field] = null;
            }
        });

        return data;
    }

    /**
     * Populate form with customer data
     * @param {Object} customerData - Customer data to populate
     * @param {HTMLFormElement} form - Form to populate (optional, uses current form if not provided)
     */
    populateForm(customerData, form = null) {
        const targetForm = form || this.currentForm;
        if (!targetForm) {
            throw new Error('No form available to populate');
        }

        // Reverse field mapping for population
        const fieldMapping = {
            'first_name': 'customerName',
            'last_name': 'customerSurname',
            'phone': 'customerPhone',
            'identity_no': 'customerIdentity',
            'birth_day': 'customerBirthDate',
            'birth_place': 'customerBirthPlace',
            'company_id': 'customerCompany',
            'customer_group_id': 'customerGroup',
            'customer_type_id': 'customerType',
            'customer_criteria_id': 'customerCriteria',
            'agent_id': 'officialAgent',
            'status': 'customerStatus',
            'oid': 'externalCustomerId',
            'dynamic_ivr_id': 'ivrTarget',
            'description': 'customerDescription',
            'gsm': 'customerLandline',
            'fax': 'customerFax',
            'email': 'customerMail',
            'address': 'customerAddress',
            'city': 'customerCity',
            'neighborhood': 'customerDistrict',
            'country': 'customerCountry',
            'tax_office': 'customerTaxOffice',
            'tax_no': 'customerTaxNumber'
        };

        // Populate form fields
        Object.entries(customerData).forEach(([apiField, value]) => {
            const formFieldName = fieldMapping[apiField] || apiField;
            const field = targetForm.querySelector(`[name="${formFieldName}"]`);

            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = value == 1 || value === true;
                } else {
                    field.value = value || '';
                }
            }
        });

        // Handle special checkboxes explicitly
        const callCheckbox = targetForm.querySelector('[name="customerCallAble"]');
        if (callCheckbox) {
            callCheckbox.checked = customerData.can_receive_call == 1 || customerData.can_receive_call === true;
        }

        const smsCheckbox = targetForm.querySelector('[name="customerSmsAble"]');
        if (smsCheckbox) {
            smsCheckbox.checked = customerData.can_receive_sms == 1 || customerData.can_receive_sms === true;
        }
    }

    // Validation methods removed - validation is now handled exclusively by server-side Laravel validation

    /**
     * Display validation errors
     * @param {Object} errors - Validation errors object
     * @param {HTMLFormElement} form - Form to display errors on (optional, uses current form if not provided)
     */
    displayErrors(errors, form = null) {
        const targetForm = form || this.currentForm;
        if (!targetForm) return;

        // Clear previous errors
        this.clearErrors(targetForm);

        // Field mapping for error display
        const fieldMapping = {
            'first_name': 'customerName',
            'last_name': 'customerSurname',
            'phone': 'customerPhone',
            'identity_no': 'customerIdentity',
            'birth_day': 'customerBirthDate',
            'company_id': 'customerCompany',
            'customer_group_id': 'customerGroup',
            'customer_type_id': 'customerType',
            'customer_criteria_id': 'customerCriteria',
            'agent_id': 'officialAgent',
            'status': 'customerStatus',
            'oid': 'externalCustomerId',
            'dynamic_ivr_id': 'ivrTarget',
            'description': 'customerDescription',
            'gsm': 'customerLandline',
            'fax': 'customerFax',
            'email': 'customerMail',
            'address': 'customerAddress',
            'city': 'customerCity',
            'neighborhood': 'customerDistrict',
            'country': 'customerCountry',
            'tax_office': 'customerTaxOffice',
            'tax_no': 'customerTaxNumber'
        };

        // Display field-specific errors
        Object.entries(errors).forEach(([field, fieldErrors]) => {
            const formFieldName = fieldMapping[field] || field;
            const fieldElement = targetForm.querySelector(`[name="${formFieldName}"]`);
            
            if (fieldElement) {
                this._displayFieldError(fieldElement, fieldErrors);
            }
        });

        // Display general errors in error container if available
        if (this.errorContainer) {
            this._displayGeneralErrors(errors);
        }
    }

    /**
     * Clear all validation errors
     * @param {HTMLFormElement} form - Form to clear errors from (optional, uses current form if not provided)
     */
    clearErrors(form = null) {
        const targetForm = form || this.currentForm;
        if (!targetForm) return;

        // Remove error classes and messages
        const errorElements = targetForm.querySelectorAll('.is-invalid, .error-message');
        errorElements.forEach(element => {
            element.classList.remove('is-invalid');
            if (element.classList.contains('error-message')) {
                element.remove();
            }
        });

        // Clear error container
        if (this.errorContainer) {
            this.errorContainer.innerHTML = '';
            this.errorContainer.style.display = 'none';
        }
    }

    /**
     * Reset form to initial state
     * @param {HTMLFormElement} form - Form to reset (optional, uses current form if not provided)
     */
    resetForm(form = null) {
        const targetForm = form || this.currentForm;
        if (!targetForm) return;

        targetForm.reset();
        this.clearErrors(targetForm);
    }

    // Real-time validation methods removed - validation is now handled exclusively by server-side Laravel validation

    /**
     * Display error for a specific field
     * @private
     * @param {HTMLElement} field - Field element
     * @param {Array} errors - Array of error messages
     */
    _displayFieldError(field, errors) {
        field.classList.add('is-invalid');
        
        // Create error message element
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-danger small mt-1';
        errorDiv.textContent = Array.isArray(errors) ? errors[0] : errors;
        
        // Insert error message after the field
        field.parentNode.insertBefore(errorDiv, field.nextSibling);
    }

    /**
     * Clear error for a specific field
     * @private
     * @param {HTMLElement} field - Field element
     */
    _clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    /**
     * Display general errors in error container
     * @private
     * @param {Object} errors - Errors object
     */
    _displayGeneralErrors(errors) {
        if (!this.errorContainer) return;

        const errorList = document.createElement('ul');
        errorList.className = 'list-unstyled mb-0';

        Object.values(errors).forEach(fieldErrors => {
            const errorArray = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];
            errorArray.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
        });

        this.errorContainer.innerHTML = '';
        this.errorContainer.appendChild(errorList);
        this.errorContainer.style.display = 'block';
    }
}

export default CustomerFormHandler;
