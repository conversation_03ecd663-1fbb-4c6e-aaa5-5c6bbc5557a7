/**
 * Customer Module Index
 * Exports all customer-related modules
 * Note: CustomerValidator has been removed - validation is handled exclusively by server-side Laravel validation
 */

import CustomerAPI from './api.js';
import CustomerFormHandler from './formHandler.js';
import CustomerCRUD from './crud.js';
import CustomerNotifications from './notifications.js';
import CustomerManager from './customerManager.js';

// Export individual modules
export {
    CustomerAPI,
    CustomerFormHandler,
    CustomerCRUD,
    CustomerNotifications,
    CustomerManager
};

// Export default manager for easy use
export default CustomerManager;
