/**
 * Entity Configurations Index
 * Exports all entity configurations
 */

import customerGroupConfig from './customerGroupConfig.js';
import customerTypeConfig from './customerTypeConfig.js';
import customerCriteriaConfig from './customerCriteriaConfig.js';

export {
    customerGroupConfig,
    customerTypeConfig,
    customerCriteriaConfig
};

// Export configurations by entity name for easy access
export const configs = {
    'customer-groups': customerGroupConfig,
    'customer-types': customerTypeConfig,
    'customer-criteria': customerCriteriaConfig
};

export default configs;
