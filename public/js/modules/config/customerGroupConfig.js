/**
 * Customer Group Configuration
 * Configuration object for customer group CRUD operations
 */

const customerGroupConfig = {
    // Entity information
    entityName: 'Müşteri Grubu',
    entityNamePlural: 'Müşteri Grupları',
    
    // API configuration
    baseURL: '/api/customer-groups',
    endpoints: {
        getAll: '',
        getById: '/{id}',
        create: '',
        update: '/{id}',
        delete: '/{id}',
        deleteMultiple: ''
    },
    
    // Form field configuration
    fields: {
        title: {
            required: true,
            type: 'text',
            minLength: 2,
            maxLength: 255
        },
        description: {
            required: false,
            type: 'text',
            maxLength: 500
        }
    },
    
    // UI selectors
    selectors: {
        form: '#customerGroupForm',
        addButton: '#addCustomerGroupBtn',
        editButton: '#editCustomerGroupBtn',
        deleteButton: '#deleteSelectedBtn',
        dataTable: '#customerGroupTable',
        modal: '#customerGroupModal',
        errorContainer: '#errorContainer'
    },
    
    // DataTable configuration
    dataTable: {
        columns: [
            { name: 'checkbox', orderable: false },
            { name: 'title', orderable: true },
            { name: 'description', orderable: true },
            { name: 'created_at', orderable: true },
            { name: 'customers_count', orderable: false },
            { name: 'actions', orderable: false }
        ],
        checkboxName: 'customerGroupIds[]'
    },

    // CSS class patterns for buttons
    buttonClasses: {
        edit: 'edit-customer-group-btn',
        delete: 'delete-customer-group-btn'
    },
    
    // Custom messages
    messages: {
        createSuccess: 'Müşteri grubu başarıyla oluşturuldu.',
        updateSuccess: 'Müşteri grubu başarıyla güncellendi.',
        deleteSuccess: 'Müşteri grubu başarıyla silindi.',
        deleteMultipleSuccess: '{count} müşteri grubu başarıyla silindi.',
        createError: 'Müşteri grubu oluşturulurken bir hata oluştu.',
        updateError: 'Müşteri grubu güncellenirken bir hata oluştu.',
        deleteError: 'Müşteri grubu silinirken bir hata oluştu.',
        loadError: 'Müşteri grupları yüklenirken bir hata oluştu.',
        validationError: 'Lütfen formu doğru şekilde doldurun.',
        networkError: 'Ağ bağlantısı hatası. Lütfen tekrar deneyin.',
        unknownError: 'Bilinmeyen bir hata oluştu.'
    },
    
    // Data formatting functions
    formatters: {
        /**
         * Format customer group data for DataTable row
         * @param {Object} group - Customer group data
         * @returns {Array} DataTable row array
         */
        formatTableRow: (group) => [
            `<input type="checkbox" name="customerGroupIds[]" value="${group.id}" class="row-check">`,
            group.title || '',
            group.description || '',
            group.created_at ? new Date(group.created_at).toLocaleString('tr-TR') : '',
            group.customers_count || '0',
            `<button class="btn btn-sm btn-outline-primary edit-customer-group-btn" data-id="${group.id}">
                <span class="iconify" data-icon="hugeicons:edit-02"></span>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-customer-group-btn" data-id="${group.id}">
                <span class="iconify" data-icon="hugeicons:delete-03"></span>
            </button>`
        ]
    }
};

export default customerGroupConfig;
