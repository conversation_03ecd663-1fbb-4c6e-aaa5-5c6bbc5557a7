/**
 * Customer Criteria Configuration
 * Configuration object for customer criteria CRUD operations
 */

const customerCriteriaConfig = {
    // Entity information
    entityName: 'Müşteri Kriteri',
    entityNamePlural: 'Müşteri Kriterleri',
    
    // API configuration
    baseURL: '/api/customer-criteria',
    endpoints: {
        getAll: '',
        getById: '/{id}',
        create: '',
        update: '/{id}',
        delete: '/{id}',
        deleteMultiple: ''
    },
    
    // Form field configuration
    fields: {
        title: {
            required: true,
            type: 'text',
            minLength: 2,
            maxLength: 255
        },
        description: {
            required: false,
            type: 'text',
            maxLength: 500
        }
    },
    
    // UI selectors
    selectors: {
        form: '#customerCriteriaForm',
        addButton: '#addCustomerCriteriaBtn',
        editButton: '#editCustomerCriteriaBtn',
        deleteButton: '#deleteSelectedBtn',
        dataTable: '#customerCriteriaTable',
        modal: '#customerCriteriaModal',
        errorContainer: '#errorContainer'
    },
    
    // DataTable configuration
    dataTable: {
        columns: [
            { name: 'checkbox', orderable: false },
            { name: 'title', orderable: true },
            { name: 'description', orderable: true },
            { name: 'created_at', orderable: true },
            { name: 'customers_count', orderable: false },
            { name: 'actions', orderable: false }
        ],
        checkboxName: 'customerCriteriaIds[]'
    },

    // CSS class patterns for buttons
    buttonClasses: {
        edit: 'edit-customer-criteria-btn',
        delete: 'delete-customer-criteria-btn'
    },
    
    // Custom messages
    messages: {
        createSuccess: 'Müşteri kriteri başarıyla oluşturuldu.',
        updateSuccess: 'Müşteri kriteri başarıyla güncellendi.',
        deleteSuccess: 'Müşteri kriteri başarıyla silindi.',
        deleteMultipleSuccess: '{count} müşteri kriteri başarıyla silindi.',
        createError: 'Müşteri kriteri oluşturulurken bir hata oluştu.',
        updateError: 'Müşteri kriteri güncellenirken bir hata oluştu.',
        deleteError: 'Müşteri kriteri silinirken bir hata oluştu.',
        loadError: 'Müşteri kriterleri yüklenirken bir hata oluştu.',
        validationError: 'Lütfen formu doğru şekilde doldurun.',
        networkError: 'Ağ bağlantısı hatası. Lütfen tekrar deneyin.',
        unknownError: 'Bilinmeyen bir hata oluştu.'
    },
    
    // Data formatting functions
    formatters: {
        /**
         * Format customer criteria data for DataTable row
         * @param {Object} criteria - Customer criteria data
         * @returns {Array} DataTable row array
         */
        formatTableRow: (criteria) => [
            `<input type="checkbox" name="customerCriteriaIds[]" value="${criteria.id}" class="row-check">`,
            criteria.title || '',
            criteria.description || '',
            criteria.created_at ? new Date(criteria.created_at).toLocaleString('tr-TR') : '',
            criteria.customers_count || '0',
            `<button class="btn btn-sm btn-outline-primary edit-customer-criteria-btn" data-id="${criteria.id}">
                <span class="iconify" data-icon="hugeicons:edit-02"></span>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-customer-criteria-btn" data-id="${criteria.id}">
                <span class="iconify" data-icon="hugeicons:delete-03"></span>
            </button>`
        ]
    }
};

export default customerCriteriaConfig;
