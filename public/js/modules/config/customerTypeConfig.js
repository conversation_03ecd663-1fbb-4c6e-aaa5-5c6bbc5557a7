/**
 * Customer Type Configuration
 * Configuration object for customer type CRUD operations
 */

const customerTypeConfig = {
    // Entity information
    entityName: 'Müşteri Tipi',
    entityNamePlural: 'Müşteri Tipleri',
    
    // API configuration
    baseURL: '/api/customer-types',
    endpoints: {
        getAll: '',
        getById: '/{id}',
        create: '',
        update: '/{id}',
        delete: '/{id}',
        deleteMultiple: ''
    },
    
    // Form field configuration
    fields: {
        title: {
            required: true,
            type: 'text',
            minLength: 2,
            maxLength: 255
        },
        description: {
            required: false,
            type: 'text',
            maxLength: 500
        }
    },
    
    // UI selectors
    selectors: {
        form: '#customerTypeForm',
        addButton: '#addCustomerTypeBtn',
        editButton: '#editCustomerTypeBtn',
        deleteButton: '#deleteSelectedBtn',
        dataTable: '#customerTypeTable',
        modal: '#customerTypeModal',
        errorContainer: '#errorContainer'
    },
    
    // DataTable configuration
    dataTable: {
        columns: [
            { name: 'checkbox', orderable: false },
            { name: 'title', orderable: true },
            { name: 'description', orderable: true },
            { name: 'created_at', orderable: true },
            { name: 'customers_count', orderable: false },
            { name: 'actions', orderable: false }
        ],
        checkboxName: 'customerTypeIds[]'
    },

    // CSS class patterns for buttons
    buttonClasses: {
        edit: 'edit-customer-type-btn',
        delete: 'delete-customer-type-btn'
    },
    
    // Custom messages
    messages: {
        createSuccess: 'Müşteri tipi başarıyla oluşturuldu.',
        updateSuccess: 'Müşteri tipi başarıyla güncellendi.',
        deleteSuccess: 'Müşteri tipi başarıyla silindi.',
        deleteMultipleSuccess: '{count} müşteri tipi başarıyla silindi.',
        createError: 'Müşteri tipi oluşturulurken bir hata oluştu.',
        updateError: 'Müşteri tipi güncellenirken bir hata oluştu.',
        deleteError: 'Müşteri tipi silinirken bir hata oluştu.',
        loadError: 'Müşteri tipleri yüklenirken bir hata oluştu.',
        validationError: 'Lütfen formu doğru şekilde doldurun.',
        networkError: 'Ağ bağlantısı hatası. Lütfen tekrar deneyin.',
        unknownError: 'Bilinmeyen bir hata oluştu.'
    },
    
    // Data formatting functions
    formatters: {
        /**
         * Format customer type data for DataTable row
         * @param {Object} type - Customer type data
         * @returns {Array} DataTable row array
         */
        formatTableRow: (type) => [
            `<input type="checkbox" name="customerTypeIds[]" value="${type.id}" class="row-check">`,
            type.title || '',
            type.description || '',
            type.created_at ? new Date(type.created_at).toLocaleString('tr-TR') : '',
            type.customers_count || '0',
            `<button class="btn btn-sm btn-outline-primary edit-customer-type-btn" data-id="${type.id}">
                <span class="iconify" data-icon="hugeicons:edit-02"></span>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-customer-type-btn" data-id="${type.id}">
                <span class="iconify" data-icon="hugeicons:delete-03"></span>
            </button>`
        ]
    }
};

export default customerTypeConfig;
