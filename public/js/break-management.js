/**
 * Break Management System
 * Handles AJAX calls for starting and ending breaks
 */

class BreakManager {
    constructor() {
        this.baseURL = '/api/breaks';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        this.init();
    }

    init() {
        // Set up axios defaults
        if (window.axios) {
            window.axios.defaults.headers.common['Accept'] = 'application/json';
            window.axios.defaults.headers.common['Content-Type'] = 'application/json';
            if (this.csrfToken) {
                window.axios.defaults.headers.common['X-CSRF-TOKEN'] = this.csrfToken;
            }
        }
    }

    /**
     * Start a new break
     * @param {string} breakType - Type of break (mola, toplanti, egitim, diger)
     * @param {string} breakDescription - Optional description
     */
    async startBreak(breakType, breakDescription = '') {
        try {
            this.showLoading('<PERSON>la başlatılıyor...');

            const response = await this.makeRequest('POST', '/start', {
                break_type: breakType,
                break_description: breakDescription
            });

            if (response.success) {
                this.showSuccess(response.message || 'Mola başarıyla başlatıldı.');
                this.refreshPage();
            } else {
                this.showError(response.message || 'Mola başlatılamadı.');
            }

        } catch (error) {
            console.error('Break start error:', error);
            this.showError('Mola başlatılırken bir hata oluştu.');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * End the current active break
     */
    async endBreak() {
        try {
            this.showLoading('Mola sonlandırılıyor...');

            const response = await this.makeRequest('POST', '/end');

            if (response.success) {
                this.showSuccess(response.message || 'Mola başarıyla sonlandırıldı.');
                this.refreshPage();
            } else {
                this.showError(response.message || 'Mola sonlandırılamadı.');
            }

        } catch (error) {
            console.error('Break end error:', error);
            this.showError('Mola sonlandırılırken bir hata oluştu.');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Get current break status
     */
    async getBreakStatus() {
        try {
            const response = await this.makeRequest('GET', '/status');
            return response;
        } catch (error) {
            console.error('Break status error:', error);
            return { success: false, data: { has_active_break: false } };
        }
    }

    /**
     * Make HTTP request
     * @param {string} method - HTTP method
     * @param {string} endpoint - API endpoint
     * @param {object} data - Request data
     */
    async makeRequest(method, endpoint, data = null) {
        const url = this.baseURL + endpoint;
        
        if (window.axios) {
            const config = {
                method: method.toLowerCase(),
                url: url
            };

            if (data && (method === 'POST' || method === 'PUT')) {
                config.data = data;
            }

            const response = await window.axios(config);
            return response.data;
        } else {
            // Fallback to fetch if axios is not available
            const config = {
                method: method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            };

            if (this.csrfToken) {
                config.headers['X-CSRF-TOKEN'] = this.csrfToken;
            }

            if (data && (method === 'POST' || method === 'PUT')) {
                config.body = JSON.stringify(data);
            }

            const response = await fetch(url, config);
            return await response.json();
        }
    }

    /**
     * Show loading message
     * @param {string} message 
     */
    showLoading(message = 'İşlem yapılıyor...') {
        // Remove existing loading if any
        this.hideLoading();

        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'break-loading';
        loadingDiv.innerHTML = `
            <div class="break-loading-overlay">
                <div class="break-loading-content">
                    <div class="break-loading-spinner"></div>
                    <p>${message}</p>
                </div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .break-loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
            .break-loading-content {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
            .break-loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #4B67C2;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 10px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        loadingDiv.appendChild(style);

        document.body.appendChild(loadingDiv);
    }

    /**
     * Hide loading message
     */
    hideLoading() {
        const loading = document.getElementById('break-loading');
        if (loading) {
            loading.remove();
        }
    }

    /**
     * Show success message
     * @param {string} message 
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     * @param {string} message 
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show notification
     * @param {string} message 
     * @param {string} type - success, error, info
     */
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelectorAll('.break-notification');
        existing.forEach(el => el.remove());

        const notification = document.createElement('div');
        notification.className = `break-notification break-notification-${type}`;
        notification.innerHTML = `
            <div class="break-notification-content">
                <span class="break-notification-message">${message}</span>
                <button class="break-notification-close">&times;</button>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .break-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9998;
                max-width: 400px;
                animation: slideInRight 0.3s ease-out;
            }
            .break-notification-content {
                padding: 12px 16px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            .break-notification-success .break-notification-content {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .break-notification-error .break-notification-content {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .break-notification-info .break-notification-content {
                background: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
            .break-notification-message {
                flex: 1;
                font-size: 14px;
                font-weight: 500;
            }
            .break-notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                margin-left: 10px;
                opacity: 0.7;
            }
            .break-notification-close:hover {
                opacity: 1;
            }
        `;
        notification.appendChild(style);

        // Add close functionality
        const closeBtn = notification.querySelector('.break-notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        });

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Refresh the current page to update break status
     */
    refreshPage() {
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    }
}

// Initialize BreakManager when script loads
window.BreakManager = new BreakManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BreakManager;
}
