/**
 * SIP Manager Class
 * Handles SIP calling functionality using SIP.js library
 */
class SIPManager {
    constructor() {
        this.userAgent = null;
        this.currentSession = null;
        this.isRegistered = false;
        this.isInitialized = false;
        this.remoteAudio = null; // Audio element for remote audio

        // Configuration
        this.config = {
            uri: 'sip:' + window.sipConfig.authorizationUsername + '@sip.odyssey-cap.com',
            transportOptions: {
                server: 'wss://sip.odyssey-cap.com:8089/ws',
                keepAliveInterval: 30000,  // her 30 saniyede bir ping at
                connectionTimeout: 10000
            },
            authorizationUsername: window.sipConfig.authorizationUsername,
            authorizationPassword: window.sipConfig.authorizationPassword,
            registerOptions: {
                expires: 300                  // 5 dakika kayıt süresi
            },
            sessionDescriptionHandlerFactoryOptions: {
                constraints: { audio: true, video: false },
                peerConnectionOptions: {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' }
                    ]
                }
            }
        };
        console.log('SIPManager initialized with config:', this.config);

        // Event callbacks
        this.onCallStarted = null;
        this.onCallEstablished = null;
        this.onCallEnded = null;
        this.onCallFailed = null;
        this.onIncomingCall = null;
        this.onIncomingCallTerminated = null; // New callback for when incoming call is terminated before being answered
        this.onRegistered = null;
        this.onUnregistered = null;
        this.onError = null;

        // Create remote audio element
        this.createRemoteAudioElement();
    }

    /**
     * Send WebSocket notification for call state changes
     * @param {string} callState - 'on_call' or 'off_call'
     */
    saveCall(phoneNumber, type, callId) {
            window.axios.post('/api/calls', {
                caller_id: phoneNumber,
                status: 'ON_CALL', 
                duration: 0, 
                type: type, 
                hash: callId,
            })
    }
    sendCallStateNotification(callState) {
        try {
            // Check if WebSocket is available and connected
            if (typeof window !== 'undefined' && window.ws && window.ws.readyState === WebSocket.OPEN) {
                // Check if authentication parameters are available
                if (window.currentCompanyId && window.currentUserName && window.currentUserId) {
                    const message = {
                        type: callState,
                        companyId: window.currentCompanyId,
                        userName: window.currentUserName,
                        userId: window.currentUserId
                    };

                    window.ws.send(JSON.stringify(message));
                    console.log(`WebSocket call state notification sent: ${callState}`, message);
                } else {
                    console.warn('WebSocket call state notification failed: Missing authentication parameters');
                    console.log('Available parameters:', {
                        companyId: window.currentCompanyId,
                        userName: window.currentUserName,
                        userId: window.currentUserId
                    });
                }
            } else {
                console.warn('WebSocket call state notification failed: WebSocket not available or not connected');
                console.log('WebSocket state:', window.ws ? window.ws.readyState : 'undefined');
            }
        } catch (error) {
            console.error('Error sending WebSocket call state notification:', error);
        }
    }

    /**
     * Create and configure remote audio element for incoming audio
     */
    createRemoteAudioElement() {
        this.remoteAudio = document.createElement('audio');
        this.remoteAudio.autoplay = true;
        this.remoteAudio.controls = false;
        this.remoteAudio.style.display = 'none';

        // Add to document body
        document.body.appendChild(this.remoteAudio);

        console.log('Remote audio element created');
    }

    /**
     * Test audio permissions and device access
     */
    async testAudioPermissions() {
        try {
            console.log('Testing audio permissions...');

            // Request microphone access
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            console.log('Audio permissions granted');

            // Stop the test stream
            stream.getTracks().forEach(track => track.stop());

            return true;
        } catch (error) {
            console.error('Audio permission test failed:', error);
            return false;
        }
    }

    /**
     * Initialize the SIP UserAgent
     */
    async initialize() {
        try {
            if (typeof SIP === 'undefined') {
                throw new Error('SIP.js library not loaded');
            }

            // Test audio permissions first
            const audioPermissions = await this.testAudioPermissions();
            if (!audioPermissions) {
                console.warn('Audio permissions not granted, calls may have audio issues');
            }

            // Create UserAgent with the provided configuration
            this.userAgent = new SIP.UserAgent({
                uri: SIP.UserAgent.makeURI(this.config.uri),
                transportOptions: this.config.transportOptions,
                authorizationUsername: this.config.authorizationUsername,
                authorizationPassword: this.config.authorizationPassword,
                sessionDescriptionHandlerFactoryOptions: this.config.sessionDescriptionHandlerFactoryOptions
            });

            // Set up event listeners
            this.setupEventListeners();

            // Start the UserAgent
            await this.userAgent.start();

            this.registerer = new SIP.Registerer(this.userAgent, {
                expires: 600
            });

            try {
                await this.registerer.register();
                console.log('SIP REGISTER gönderildi');
            } catch (registerError) {
                console.error('SIP REGISTER başarısız:', registerError);
                if (this.onError) {
                    this.onError('register_failed', registerError.message);
                }
            }

            this.isInitialized = true;
            console.log('SIP UserAgent initialized successfully');

            return true;
        } catch (error) {
            console.error('Failed to initialize SIP UserAgent:', error);
            if (this.onError) {
                this.onError('initialization_failed', error.message);
            }
            return false;
        }
    }

    /**
     * Set up event listeners for the UserAgent
     */
    setupEventListeners() {
        if (!this.userAgent) return;

        // Registration events
        this.userAgent.delegate = {
            onConnect: () => {
                console.log('SIP transport connected');
                this.isRegistered = true;
                if (this.onRegistered) {
                    this.onRegistered();
                }
            },
            onDisconnect: (error) => {
                console.log('SIP transport disconnected', error);
                this.isRegistered = false;
                if (this.onUnregistered) {
                    this.onUnregistered();
                }
            },
            onInvite: (invitation) => {
                console.log('Incoming call received');
                // Handle incoming calls if needed
                this.handleIncomingCall(invitation);
            }
        };
    }

    /**
     * Make a call to the specified number
     */
    async makeCall(phoneNumber) {
        try {
            if (!this.isInitialized) {
                throw new Error('SIP UserAgent not initialized');
            }

            if (!this.isRegistered) {
                throw new Error('SIP UserAgent not registered');
            }

            if (this.currentSession) {
                throw new Error('Another call is already in progress');
            }

            // Clean the phone number (remove any non-digit characters except +)
            const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
            
            if (!cleanNumber) {
                throw new Error('Invalid phone number');
            }

            // Create the target URI
            const targetURI = SIP.UserAgent.makeURI(`sip:${cleanNumber}@sip.odyssey-cap.com`);
            
            if (!targetURI) {
                throw new Error('Failed to create target URI');
            }

            // Create inviter options with enhanced audio configuration
            const inviterOptions = {
                sessionDescriptionHandlerOptions: {
                    ...this.config.sessionDescriptionHandlerFactoryOptions,
                    // Ensure audio constraints are properly set
                    constraints: {
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        },
                        video: false
                    }
                }
            };

            // Create the inviter (outgoing call)
            const inviter = new SIP.Inviter(this.userAgent, targetURI, inviterOptions);
            this.currentSession = inviter;

            // Set up session event listeners
            this.setupSessionEventListeners(inviter);

            // Send the INVITE
            await inviter.invite();

            const callId = inviter.request.callId;
            this.saveCall(cleanNumber, 'OUTGOING', callId);
            // Set up additional audio handling after invite
            this.setupPostInviteAudioHandling(inviter);

            console.log(`Call initiated to ${cleanNumber}`);
            
            if (this.onCallStarted) {
                this.onCallStarted(cleanNumber);
            }

            return true;
        } catch (error) {
            console.error('Failed to make call:', error);
            this.currentSession = null;
            
            if (this.onCallFailed) {
                this.onCallFailed(error.message);
            }
            
            return false;
        }
    }

    /**
     * End the current call
     */
    async endCall() {
        try {
            if (!this.currentSession) {
                console.log('No active call to end');
                return true;
            }

            console.log('Ending call, current state:', this.currentSession.state);

            // Store reference to session for cleanup
            const sessionToEnd = this.currentSession;

            // Immediately clean up our state
            this.currentSession = null;
            this.cleanupAudio();

            // Send WebSocket notification that user is no longer on a call
            this.sendCallStateNotification('off_call');

            // Trigger onCallEnded callback immediately for UI responsiveness
            if (this.onCallEnded) {
                this.onCallEnded();
            }

            // End the session based on its current state with timeout
            const terminationPromise = (async () => {
                if (sessionToEnd.state === SIP.SessionState.Established) {
                    console.log('Sending BYE for established call');
                    return await sessionToEnd.bye();
                } else if (sessionToEnd.state === SIP.SessionState.Establishing) {
                    console.log('Sending CANCEL for establishing call');
                    return await sessionToEnd.cancel();
                } else if (sessionToEnd.state === SIP.SessionState.Initial) {
                    console.log('Canceling initial call');
                    return await sessionToEnd.cancel();
                } else {
                    console.log('Call in state:', sessionToEnd.state, '- attempting to dispose');
                    // For any other state, try to dispose the session
                    if (sessionToEnd.dispose) {
                        sessionToEnd.dispose();
                    }
                    return Promise.resolve();
                }
            })();

            // Add timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Call termination timeout')), 5000);
            });

            try {
                await Promise.race([terminationPromise, timeoutPromise]);
            } catch (timeoutError) {
                console.warn('Call termination timed out, forcing cleanup:', timeoutError.message);
                // Force dispose if timeout
                if (sessionToEnd.dispose) {
                    sessionToEnd.dispose();
                }
            }

            console.log('Call termination initiated successfully');
            return true;
        } catch (error) {
            console.error('Failed to end call:', error);

            // Even if termination fails, ensure cleanup
            this.currentSession = null;
            this.cleanupAudio();

            // Send WebSocket notification that user is no longer on a call
            this.sendCallStateNotification('off_call');

            // Still trigger callback for UI cleanup
            if (this.onCallEnded) {
                this.onCallEnded();
            }

            return false;
        }
    }

    /**
     * Set up additional audio handling after invite is sent
     */
    setupPostInviteAudioHandling(session) {
        // Monitor for when the session description handler becomes available
        const checkForSDH = () => {
            if (session.sessionDescriptionHandler) {
                console.log('Session description handler is now available');
                this.setupPeerConnectionHandlers(session.sessionDescriptionHandler.peerConnection);
            } else {
                // Check again in a short while
                setTimeout(checkForSDH, 100);
            }
        };

        // Start checking
        setTimeout(checkForSDH, 100);
    }

    /**
     * Set up event listeners for a session
     */
    setupSessionEventListeners(session) {
        session.delegate = {
            onBye: () => {
                console.log('Call ended by remote party (BYE received)');

                // Always cleanup and trigger callback for this session if it's our current session
                // or if we don't have a current session (edge case)
                if (this.currentSession === session || !this.currentSession) {
                    console.log('Cleaning up session after remote BYE');
                    this.cleanupAudio();
                    this.currentSession = null;

                    // Send WebSocket notification that user is no longer on a call
                    this.sendCallStateNotification('off_call');

                    if (this.onCallEnded) {
                        this.onCallEnded();
                    }
                } else {
                    console.log('BYE received for non-current session, ignoring');
                }
            },
            onSessionDescriptionHandler: (sdh) => {
                console.log('Session description handler created');

                // Set up peer connection event handlers
                sdh.peerConnectionDelegate = {
                    onconnectionstatechange: (event) => {
                        console.log('Connection state changed:', event.target.connectionState);
                        if (event.target.connectionState === 'connected') {
                            // Double-check for remote streams when connected
                            setTimeout(() => this.checkForRemoteStreams(event.target), 500);
                        }
                    },
                    ontrack: (event) => {
                        console.log('Remote track received via delegate:', event.track.kind);
                        this.handleRemoteTrack(event);
                    },
                    onaddstream: (event) => {
                        console.log('Remote stream added via delegate:', event.stream);
                        this.attachRemoteStream(event.stream);
                    }
                };

                // Handle when the peer connection is created
                if (sdh.peerConnection) {
                    this.setupPeerConnectionHandlers(sdh.peerConnection);
                } else {
                    // Wait for peer connection to be created
                    const waitForPC = () => {
                        if (sdh.peerConnection) {
                            this.setupPeerConnectionHandlers(sdh.peerConnection);
                        } else {
                            setTimeout(waitForPC, 100);
                        }
                    };
                    setTimeout(waitForPC, 100);
                }
            }
        };

        // Handle state changes
        session.stateChange.addListener((state) => {
            console.log('Session state changed:', state);

            switch (state) {
                case SIP.SessionState.Established:
                    console.log('Call established');
                    this.handleCallEstablished(session);

                    // Send WebSocket notification that user is now on a call
                    this.sendCallStateNotification('on_call');

                    // Trigger onCallEstablished callback
                    if (this.onCallEstablished) {
                        const phoneNumber = session.remoteIdentity ? session.remoteIdentity.uri.user : 'Unknown';
                        this.onCallEstablished(phoneNumber);
                    }
                    break;
                case SIP.SessionState.Terminated:
                    console.log('Session state changed to Terminated');

                    // Always cleanup and trigger callback for this session if it's our current session
                    // or if we don't have a current session (edge case)
                    if (this.currentSession === session || !this.currentSession) {
                        console.log('Cleaning up session after termination');
                        this.cleanupAudio();
                        this.currentSession = null;

                        // Send WebSocket notification that user is no longer on a call
                        this.sendCallStateNotification('off_call');

                        if (this.onCallEnded) {
                            this.onCallEnded();
                        }
                    } else {
                        console.log('Session terminated for non-current session, ignoring');
                    }
                    break;
            }
        });
    }

    /**
     * Handle when call is established - set up audio streams
     */
    handleCallEstablished(session) {
        console.log('Setting up audio streams for established call');

        // Get the session description handler
        const sdh = session.sessionDescriptionHandler;
        if (sdh && sdh.peerConnection) {
            this.setupPeerConnectionHandlers(sdh.peerConnection);
        }
    }

    /**
     * Set up peer connection handlers for audio streams
     */
    setupPeerConnectionHandlers(peerConnection) {
        if (!peerConnection) {
            console.warn('No peer connection provided');
            return;
        }

        console.log('Setting up peer connection handlers');
        console.log('Peer connection state:', peerConnection.connectionState);
        console.log('Peer connection signaling state:', peerConnection.signalingState);

        // Handle remote streams
        peerConnection.ontrack = (event) => {
            console.log('Remote track received:', event.track.kind, event.track.id);
            console.log('Event streams:', event.streams.length);
            this.handleRemoteTrack(event);
        };

        // Handle connection state changes
        peerConnection.onconnectionstatechange = () => {
            console.log('Peer connection state changed to:', peerConnection.connectionState);
            if (peerConnection.connectionState === 'connected') {
                // Try to get remote streams when connected
                this.checkForRemoteStreams(peerConnection);
            }
        };

        // Also check for existing remote streams
        this.checkForRemoteStreams(peerConnection);
    }

    /**
     * Check for and handle existing remote streams
     */
    checkForRemoteStreams(peerConnection) {
        // Method 1: getRemoteStreams (deprecated but still works in some browsers)
        if (peerConnection.getRemoteStreams) {
            const remoteStreams = peerConnection.getRemoteStreams();
            if (remoteStreams && remoteStreams.length > 0) {
                console.log('Found existing remote streams (getRemoteStreams):', remoteStreams.length);
                remoteStreams.forEach(stream => {
                    this.attachRemoteStream(stream);
                });
                return;
            }
        }

        // Method 2: getReceivers (modern approach)
        const receivers = peerConnection.getReceivers();
        console.log('Found receivers:', receivers.length);

        receivers.forEach(receiver => {
            if (receiver.track && receiver.track.kind === 'audio') {
                console.log('Found audio receiver track:', receiver.track.id);

                // Create a MediaStream from the track
                const stream = new MediaStream([receiver.track]);
                this.attachRemoteStream(stream);
            }
        });
    }

    /**
     * Handle remote track events
     */
    handleRemoteTrack(event) {
        console.log('Handling remote track:', event.track.kind);

        if (event.streams && event.streams.length > 0) {
            const remoteStream = event.streams[0];
            this.attachRemoteStream(remoteStream);
        }
    }

    /**
     * Attach remote stream to audio element
     */
    attachRemoteStream(stream) {
        console.log('Attaching remote stream to audio element');
        console.log('Stream details:', {
            id: stream.id,
            active: stream.active,
            audioTracks: stream.getAudioTracks().length,
            videoTracks: stream.getVideoTracks().length
        });

        if (!this.remoteAudio) {
            console.error('Remote audio element not available');
            return;
        }

        if (!stream) {
            console.error('No stream provided');
            return;
        }

        if (stream.getAudioTracks().length === 0) {
            console.warn('Stream has no audio tracks');
            return;
        }

        // Set the stream
        this.remoteAudio.srcObject = stream;

        // Set audio properties
        this.remoteAudio.volume = 1.0;
        this.remoteAudio.muted = false;

        console.log('Remote audio element configured:', {
            srcObject: !!this.remoteAudio.srcObject,
            volume: this.remoteAudio.volume,
            muted: this.remoteAudio.muted,
            autoplay: this.remoteAudio.autoplay
        });

        // Ensure audio plays
        this.remoteAudio.play().then(() => {
            console.log('Remote audio playback started successfully');
        }).catch(error => {
            console.error('Failed to start remote audio playback:', error);
            console.error('Error details:', error.name, error.message);

            // Try to enable audio with user interaction
            this.enableAudioWithUserInteraction();
        });
    }

    /**
     * Enable audio playback with user interaction (for browser autoplay policies)
     */
    enableAudioWithUserInteraction() {
        const enableAudio = () => {
            if (this.remoteAudio) {
                this.remoteAudio.play().then(() => {
                    console.log('Remote audio enabled after user interaction');
                    document.removeEventListener('click', enableAudio);
                    document.removeEventListener('touchstart', enableAudio);
                }).catch(error => {
                    console.error('Still failed to enable audio:', error);
                });
            }
        };

        document.addEventListener('click', enableAudio, { once: true });
        document.addEventListener('touchstart', enableAudio, { once: true });

        console.log('Audio will be enabled on next user interaction');
    }

    /**
     * Clean up audio resources
     */
    cleanupAudio() {
        if (this.remoteAudio) {
            this.remoteAudio.pause();
            this.remoteAudio.srcObject = null;
            console.log('Audio resources cleaned up');
        }
    }

    /**
     * Handle incoming calls
     */
    handleIncomingCall(invitation) {
        const incomingCallPhoneNumber = invitation.remoteIdentity.uri.user;
        console.log('Incoming call invitation received from:', incomingCallPhoneNumber);

        // Check if we already have an active call
        if (this.currentSession) {
            console.log('Already in a call, rejecting incoming call');
            invitation.reject();
            return;
        }

        // Set up basic event listeners for the invitation to detect if caller hangs up
        this.setupIncomingCallEventListeners(invitation);

        // Trigger incoming call callback to show UI
        if (this.onIncomingCall) {
            this.onIncomingCall(invitation);
        } else {
            // Fallback: auto-reject if no handler is set
            console.warn('No incoming call handler set, rejecting call');
            invitation.reject();
        }
    }

    /**
     * Set up basic event listeners for incoming call invitation
     * This allows us to detect if the caller hangs up before the call is answered
     */
    setupIncomingCallEventListeners(invitation) {
        console.log('Setting up event listeners for incoming call invitation');

        // Handle session state changes for unanswered incoming calls
        invitation.stateChange.addListener((state) => {
            console.log('Incoming call invitation state changed:', state);

            switch (state) {
                case SIP.SessionState.Terminated:
                    console.log('Incoming call terminated by remote party before being answered');

                    // Trigger callback to hide incoming call UI
                    if (this.onIncomingCallTerminated) {
                        const callerNumber = invitation.remoteIdentity.uri.user;
                        this.onIncomingCallTerminated(callerNumber);
                    }
                    break;
            }
        });

        // Handle BYE messages for unanswered incoming calls
        invitation.delegate = {
            onBye: () => {
                console.log('BYE received for unanswered incoming call');

                // Trigger callback to hide incoming call UI
                if (this.onIncomingCallTerminated) {
                    const callerNumber = invitation.remoteIdentity.uri.user;
                    this.onIncomingCallTerminated(callerNumber);
                }
            }
        };
    }

    /**
     * Accept an incoming call
     */
    async acceptIncomingCall(invitation) {
        try {
            console.log('Accepting incoming call');

            // Set this as the current session
            this.currentSession = invitation;

            // Set up session event handlers
            this.setupSessionEventListeners(invitation);

            // Accept the invitation
            await invitation.accept();

            console.log('Incoming call accepted successfully');

            // Trigger call started callback
            if (this.onCallStarted) {
                const callerNumber = invitation.remoteIdentity.uri.user;
                this.onCallStarted(callerNumber);
            }

        } catch (error) {
            console.error('Failed to accept incoming call:', error);
            this.currentSession = null;

            if (this.onCallFailed) {
                this.onCallFailed('Failed to accept call: ' + error.message);
            }
        }
    }

    /**
     * Reject an incoming call
     */
    rejectIncomingCall(invitation) {
        try {
            console.log('Rejecting incoming call');
            invitation.reject();
            console.log('Incoming call rejected successfully');
        } catch (error) {
            console.error('Failed to reject incoming call:', error);
        }
    }

    /**
     * Manually check and fix audio issues (for debugging)
     */
    checkAudioSetup() {
        console.log('=== Audio Setup Debug Info ===');
        console.log('Remote audio element:', this.remoteAudio);
        console.log('Remote audio src:', this.remoteAudio ? this.remoteAudio.srcObject : 'null');
        console.log('Current session:', this.currentSession);

        if (this.currentSession && this.currentSession.sessionDescriptionHandler) {
            const sdh = this.currentSession.sessionDescriptionHandler;
            console.log('Session description handler:', sdh);
            console.log('Peer connection:', sdh.peerConnection);

            if (sdh.peerConnection) {
                const pc = sdh.peerConnection;
                console.log('Connection state:', pc.connectionState);
                console.log('Signaling state:', pc.signalingState);
                console.log('Receivers:', pc.getReceivers().length);

                // Try to re-setup audio
                this.checkForRemoteStreams(pc);
            }
        }

        console.log('=== End Audio Debug Info ===');
    }

    /**
     * Force terminate any active session (for debugging/emergency use)
     */
    forceTerminate() {
        console.log('Force terminating any active session');

        if (this.currentSession) {
            try {
                // Try multiple termination methods
                if (this.currentSession.bye) {
                    this.currentSession.bye().catch(e => console.log('BYE failed:', e));
                }
                if (this.currentSession.cancel) {
                    this.currentSession.cancel().catch(e => console.log('CANCEL failed:', e));
                }
                if (this.currentSession.dispose) {
                    this.currentSession.dispose();
                }
            } catch (error) {
                console.error('Error in force terminate:', error);
            }
        }

        // Force cleanup regardless
        this.currentSession = null;
        this.cleanupAudio();

        // Send WebSocket notification that user is no longer on a call
        this.sendCallStateNotification('off_call');

        if (this.onCallEnded) {
            this.onCallEnded();
        }

        console.log('Force termination completed');
    }

    /**
     * Debug function to test WebSocket call state notifications
     * @param {string} state - 'on_call' or 'off_call'
     */
    testCallStateNotification(state = 'on_call') {
        console.log(`Testing WebSocket call state notification: ${state}`);
        this.sendCallStateNotification(state);
    }

    /**
     * Debug function to check WebSocket connection status
     */
    checkWebSocketStatus() {
        console.log('=== WebSocket Status Debug ===');
        console.log('window.ws available:', typeof window !== 'undefined' && !!window.ws);
        console.log('WebSocket ready state:', window.ws ? window.ws.readyState : 'N/A');
        console.log('WebSocket OPEN constant:', WebSocket.OPEN);
        console.log('currentCompanyId:', window.currentCompanyId);
        console.log('currentUserName:', window.currentUserName);
        console.log('currentUserId:', window.currentUserId);
        console.log('=== End WebSocket Status Debug ===');
    }

    /**
     * Check if SIP is ready for calls
     */
    isReady() {
        return this.isInitialized && this.isRegistered && !this.currentSession;
    }

    /**
     * Get current call status
     */
    getCallStatus() {
        if (!this.currentSession) {
            return 'idle';
        }
        
        switch (this.currentSession.state) {
            case SIP.SessionState.Initial:
                return 'initiating';
            case SIP.SessionState.Establishing:
                return 'connecting';
            case SIP.SessionState.Established:
                return 'connected';
            case SIP.SessionState.Terminating:
                return 'ending';
            case SIP.SessionState.Terminated:
                return 'ended';
            default:
                return 'unknown';
        }
    }

    /**
     * Cleanup and stop the UserAgent
     */
    async cleanup() {
        try {
            if (this.currentSession) {
                await this.endCall();
            }

            // Clean up audio resources
            this.cleanupAudio();

            // Remove remote audio element
            if (this.remoteAudio && this.remoteAudio.parentNode) {
                this.remoteAudio.parentNode.removeChild(this.remoteAudio);
                this.remoteAudio = null;
            }

            if (this.userAgent) {
                await this.userAgent.stop();
            }

            this.isInitialized = false;
            this.isRegistered = false;
            console.log('SIP UserAgent cleaned up');
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

// Export for use in other scripts
window.SIPManager = SIPManager;
