/**
 * AJAX Navigation Handler for Sidebar
 * Implements single-page application behavior for sidebar navigation
 */
class AjaxNavigation {
    constructor() {
        // First try to find existing content container
        this.contentContainer = document.getElementById('content');

        // If not found, look for content-wrapper and create content container inside it
        if (!this.contentContainer) {
            const contentWrapper = document.querySelector('.content-wrapper');
            if (contentWrapper) {
                // Check if there's already content in content-wrapper that needs to be preserved
                const existingContent = contentWrapper.innerHTML.trim();

                // Create the content container
                this.contentContainer = document.createElement('div');
                this.contentContainer.id = 'content';

                // Clear content-wrapper and add our content container
                contentWrapper.innerHTML = '';
                contentWrapper.appendChild(this.contentContainer);

                // If there was existing content, put it in the content container
                if (existingContent && !existingContent.includes('id="content"')) {
                    this.contentContainer.innerHTML = existingContent;
                }
            } else {
                console.error("Neither content container nor content-wrapper found!");
                return;
            }
        }

        this.sidebar = document.getElementById('sidebar');
        if (!this.sidebar) {
            console.error("Sidebar not found!");
        }
        this.loadingClass = 'loading';
        this.init();
    }

    init() {
        this.bindSidebarEvents();
        this.setupLoadingIndicator();

        // Load initial content if content area is empty or if we're on a non-home page
        if (this.contentContainer) {
            const currentPath = window.location.pathname;
            const isEmpty = this.contentContainer.innerHTML.trim() === '';

            // Load content if container is empty or if we're not on the home page
            if (isEmpty && currentPath !== '/') {
                this.loadInitialContent();
            }
        }

        // runPageInitFunction
        const currentPath = window.location.pathname;
        const route = this.getRouteFromUrl(currentPath);
        if (!window.LoadedScripts) window.LoadedScripts = {};
        $.getScript('/js/pages/' + route + '.js', function () {
            window.LoadedScripts[route.replace(/-/g, '_')] = true;

            // Fonksiyonu çağır
            runPageInitFunction(route.replace(/-/g, '_'));
        });


    }

    bindSidebarEvents() {
        if (!this.sidebar) {
            return;
        }

        // Bind click events to all navigation links
        const links = this.sidebar.querySelectorAll('a[href]');

        links.forEach(link => {
            if (!link.getAttribute('href').includes('logout')) {
                link.addEventListener('click', (e) => {
                    if (this.shouldHandleAjax(link)) {
                        e.preventDefault();
                        this.handleNavigation(link);
                    } else {
                        const href = link.getAttribute('href');
                        const currentPath = window.location.pathname;
                        const normalizedCurrentPath = currentPath.replace(/\/$/, '') || '/';
                        const normalizedLinkPath = href.replace(/\/$/, '') || '/';

                        if (normalizedCurrentPath === normalizedLinkPath) {
                            e.preventDefault();
                        }
                    }
                });
            }
        });
    }

    shouldHandleAjax(link) {
        const href = link.getAttribute('href');
        const route = link.getAttribute('data-route');

        // Skip if it's logout or doesn't have a route
        if (!route || href.includes('logout')) {
            return false;
        }

        return true;
    }

    async handleNavigation(link) {
        const route = link.getAttribute('data-route');
        const title = link.getAttribute('data-title') || link.textContent.trim();
        const url = link.getAttribute('href') || this.getUrlFromRoute(route);

        console.log('Navigating to:', url, 'with route:', route);


        try {
            // Update active state immediately for better UX
            this.updateActiveState(link);

            // Show loading state
            this.showLoading();

            // Make AJAX request
            const content = await this.fetchContent(url);

            // Update content
            this.updateContent(content, route);

            // Update URL and title
            this.updateUrl(url, title);

            // Hide loading state
            this.hideLoading();

            console.log('Navigation completed successfully');
            switch(route) {
                case 'agent-status':
                    console.log('Navigating to agent status');
                    // send message to websocket
                    if (window.ws && window.ws.readyState === WebSocket.OPEN) {
                        console.log('Requesting agent status via WebSocket');
                        window.ws.send(JSON.stringify({
                            type: 'auth',
                            companyId: window.currentCompanyId,
                            userName: window.currentUserName,
                            userId: window.currentUserId
                        }));
                    }
                    break;
                case 'calls':
            }


        } catch (error) {
            console.error('Navigation error:', error);
            this.handleError(error, url);
        }
    }

    getRouteFromUrl(url) {
        const routeMap = {
            '/': 'home',
            '/cagrilar': 'calls',
            '/planlanmis-cagrilar': 'planned-calls',
            '/agent-durumlari': 'agent-status',
            '/musteri-listesi': 'customer-list',
            '/profilim': 'profile',
            // Add more mappings as needed
            '/admin/santral-izleme': 'admin-cc-dashboard',
            '/admin/dahili-listesi': 'admin-internal-list',
            '/admin/dahili-gruplari': 'admin-internal-groups',
            '/admin/grup-kullanicilari': 'admin-group-users',
            '/admin/kuyruk-listesi': 'admin-queue-list',
            '/admin/bekleme-muzikleri': 'admin-hold-music',
            '/admin/anons-ses-kayitlari': 'admin-announcement-records',
            // Add more mappings as needed
            '/admin/ivr-listesi': 'admin-ivr-list',
            '/admin/dinamik-ivr-listesi': 'admin-dynamic-ivr-list',
            '/admin/puanlama-listesi': 'admin-score-list',
            '/admin/ivr-ses-kayitlari': 'admin-ivr-recording',
            '/admin/dis-hat-islemleri': 'admin-external-lines',
            '/admin/mesai-sablonlari': 'admin-shift-templates',
            '/admin/arama-yetkilendirme': 'admin-call-authorization',
            '/admin/karaliste': 'admin-blacklist',
            '/admin/cagrilar': 'admin-calls',
            '/admin/cagri-durumu': 'admin-call-status',
            '/admin/dahili-durumu': 'admin-internal-status',
            '/admin/hat-durumu': 'admin-line-status',
        }

        return routeMap[url] || 'home';
    }

    getUrlFromRoute(route) {
        console.log('route: ' + route);
        const routeMap = {
            // User routes
            'home': '/',
            'calls': '/cagrilar',
            'planned-calls': '/planlanmis-cagrilar',
            'agent-status': '/agent-durumlari',
            'customer-list': '/musteri-listesi',
            'profile': '/profilim',

            // Admin routes
            'admin-home': '/',
            'admin-cc-dashboard': '/admin/santral-izleme',
            'admin-internal-list': '/admin/dahili-listesi',
            'admin-internal-groups': '/admin/dahili-gruplari',
            'admin-group-users': '/admin/grup-kullanicilari',
            'admin-queue-list': '/admin/kuyruk-listesi',
            'admin-hold-music': '/admin/bekleme-muzikleri',
            'admin-announcement-records': '/admin/anons-ses-kayitlari'
        };
        console.log('url: ' + routeMap[route]);
        return routeMap[route] || '/';
    }

    async fetchContent(url) {
        // jQuery kullanarak AJAX isteği yap
        try {
            const response = await $.ajax({
                url: url,
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                beforeSend: () => {
                    // Yükleniyor durumunu göster
                    this.showLoading();
                }
            });

            // Script ve stil dosyalarını kontrol et ve yükle
            if (response.scripts) {
                for (const script of response.scripts) {
                    if (!document.querySelector(`script[src="${script}"]`)) {
                        await this.loadScript(script);
                    }
                }
            }

            if (response.styles) {
                for (const style of response.styles) {
                    if (!document.querySelector(`link[href="${style}"]`)) {
                        this.loadStyle(style);
                    }
                }
            }

            return response.content || response;
        } catch (error) {
            console.error('AJAX Error:', error);
            throw error;
        }
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    loadStyle(href) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        document.head.appendChild(link);
    }

    updateContent(html, route) {
        if (!this.contentContainer) {
            console.error('Content container not found when updating content');
            return;
        }

        // Create a temporary container to parse the HTML
        const temp = document.createElement('div');
        temp.innerHTML = html;

        // Extract and apply styles
        const styles = temp.getElementsByTagName('style');
        const links = temp.getElementsByTagName('link');

        // Remove existing page-specific styles
        document.querySelectorAll('link[data-page-specific="true"]').forEach(el => el.remove());
        document.querySelectorAll('style[data-page-specific="true"]').forEach(el => el.remove());

        // Add new styles
        Array.from(links).forEach(link => {
            if (!document.querySelector(`link[href="${link.getAttribute('href')}"]`)) {
                link.setAttribute('data-page-specific', 'true');
                document.head.appendChild(link);
            }
        });

        Array.from(styles).forEach(style => {
            style.setAttribute('data-page-specific', 'true');
            document.head.appendChild(style);
        });

        // Clear the content container completely
        this.contentContainer.innerHTML = '';

        // Find the actual content to insert
        // Look for content marked with data-page-content attribute first
        let contentToInsert = temp.querySelector('[data-page-content]');

        // If not found, look for common content wrapper patterns
        if (!contentToInsert) {
            // Try to find section elements or main content divs
            contentToInsert = temp.querySelector('section') ||
                             temp.querySelector('.content') ||
                             temp.querySelector('[class*="section"]') ||
                             temp;
        }

        // Insert the content by moving all child nodes
        if (contentToInsert === temp) {
            // If we're using the temp container itself, move all its children
            while (temp.firstChild) {
                this.contentContainer.appendChild(temp.firstChild);
            }
        } else {
            // If we found a specific content element, clone and insert it
            this.contentContainer.appendChild(contentToInsert.cloneNode(true));
        }

        if (!window.LoadedScripts) window.LoadedScripts = {};

         if (!window.LoadedScripts[route]) {
            $.getScript('/js/pages/' + route + '.js', function () {
                window.LoadedScripts[route.replace(/-/g, '_')] = true;
                runPageInitFunction(route.replace(/-/g, '_'));
                // BURAYA EKLE
                this.reinitializeComponents && this.reinitializeComponents();
            }.bind(this));
        } else {
            runPageInitFunction(route.replace(/-/g, '_'));
            // BURAYA EKLE
            this.reinitializeComponents && this.reinitializeComponents();
        }

        // Re-initialize components after content update
        //this.reinitializeComponents();
    }
// Fonksiyonu çağıran yardımcı fonksiyon

    updateActiveState(activeLink) {
        // Remove active class from all nav links
        const allLinks = this.sidebar.querySelectorAll('.nav-link');
        allLinks.forEach(link => link.classList.remove('active'));

        // Add active class to clicked link
        activeLink.classList.add('active');

        // Handle dropdown states if needed
        const parentDropdown = activeLink.closest('.dropdown-menu');
        if (parentDropdown) {
            const dropdownToggle = parentDropdown.previousElementSibling;
            if (dropdownToggle) {
                dropdownToggle.classList.add('active');
            }
        }
    }

    updateUrl(url, title) {
        // Update browser history
        window.history.pushState(
            { url: url, title: title },
            title,
            url
        );

        // Update document title
        if (title) {
            document.title = `${title} - ${document.title.split(' - ').pop()}`;
        }
    }

    showLoading() {
        if (this.contentContainer) {
            this.contentContainer.classList.add(this.loadingClass);
        }
    }

    hideLoading() {
        if (this.contentContainer) {
            this.contentContainer.classList.remove(this.loadingClass);
        }
    }

    setupLoadingIndicator() {
        // Add CSS for loading state
        const style = document.createElement('style');
        style.textContent = `
            #content.loading {
                position: relative;
                pointer-events: none;
                opacity: 0.7;
                min-height: 200px;
            }

            #content.loading::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 40px;
                height: 40px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                z-index: 1000;
            }

            @keyframes spin {
                0% { transform: translate(-50%, -50%) rotate(0deg); }
                100% { transform: translate(-50%, -50%) rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    handleError(error, url) {
        this.hideLoading();

        console.error('AJAX Navigation Error:', error);

        // Show user-friendly error message
        const errorHtml = `
            <div class="alert alert-danger m-4" role="alert">
                <h4 class="alert-heading">Sayfa Yüklenemedi</h4>
                <p>Sayfa yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</p>
                <hr>
                <p class="mb-0">
                    <button class="btn btn-outline-danger" onclick="window.location.reload()">
                        Sayfayı Yenile
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="window.location.href='${url}'">
                        Doğrudan Git
                    </button>
                </p>
            </div>
        `;

        this.updateContent(errorHtml, 'error');
    }

    loadInitialContent() {
        // Load the current page content via AJAX if content area is empty
        const currentPath = window.location.pathname;
        if (currentPath !== '/') {
            this.showLoading();
            this.fetchContent(currentPath)
                .then(content => {
                    this.updateContent(content, currentPath);
                    this.hideLoading();

                    // Update active state for the current page
                    const activeLink = this.sidebar.querySelector(`a[href="${currentPath}"]`);
                    if (activeLink) {
                        this.updateActiveState(activeLink);
                    }
                })
                .catch(error => {
                    console.warn('Could not load initial content:', error);
                    this.hideLoading();
                });
        }
    }

    reinitializeComponents() {
        // Re-initialize DataTables if present
        // if (typeof $ !== 'undefined' && $.fn.DataTable) {
        //     $('.custom-data-table-wrapper table').each(function() {
        //         if ($.fn.DataTable.isDataTable(this)) {
        //             $(this).DataTable().destroy();
        //         }

        //         // Get the number of columns in this specific table
        //         const columnCount = $(this).find('thead tr:first th').length;

        //         // Create columnDefs based on actual column count
        //         let columnDefs = [];
        //         if (columnCount > 0) {
        //             // Make first column non-orderable (usually checkbox column)
        //             columnDefs.push({ targets: 0, orderable: false });

        //             // If there are more than 1 columns, make the last column non-orderable too (usually action column)
        //             if (columnCount > 1) {
        //                 columnDefs.push({ targets: columnCount - 1, orderable: false });
        //             }
        //         }
        //         console.log('Initializing DataTable with columnDefs:', columnDefs);

        //         $(this).DataTable({
        //             searching: false,
        //             pageLength: 10,
        //             columnDefs: columnDefs,
        //             language: {
        //                 lengthMenu: '_MENU_',
        //                 info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
        //             },
        //             drawCallback: function(settings) {
        //                 document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
        //                     btn.style.color = '#FFFFFF';
        //                     btn.querySelectorAll('*').forEach(function(child) {
        //                         child.style.color = '#FFFFFF';
        //                     });
        //                 });
        //             }
        //         });
        //     });

        //     // dt-length'i dt-paging'in soluna taşı
        //     $('.custom-data-table-wrapper').each(function() {
        //         var length = $(this).find('.dt-length');
        //         var paging = $(this).find('.dt-paging');
        //         if (length.length && paging.length) {
        //             paging.before(length);
        //         }
        //     });
        // }

        // Re-initialize Bootstrap components
        if (typeof bootstrap !== 'undefined') {
            // Reinitialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Reinitialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }

        // Re-initialize Iconify icons
        if (typeof Iconify !== 'undefined') {
            Iconify.scan();
        }

        // Trigger a custom event for other scripts to hook into
        document.dispatchEvent(new CustomEvent('ajaxContentLoaded'));

        document.querySelectorAll('.audio-play-btn').forEach(btn => {
        btn.onclick = function() {
            const audio = btn.closest('tr').querySelector('audio');
            if (!audio) return;
            if (audio.paused) {
                audio.play();
                btn.classList.add('playing');
                btn.classList.remove('paused');
            } else {
                audio.pause();
                btn.classList.remove('playing');
                btn.classList.add('paused');
            }
        };
        // İkonun otomatik güncellenmesi için audio eventleri:
        const audio = btn.closest('tr').querySelector('audio');
        if (audio) {
            audio.onplay = () => {
                btn.classList.add('playing');
                btn.classList.remove('paused');
            };
            audio.onpause = () => {
                btn.classList.remove('playing');
                btn.classList.add('paused');
            };
            audio.onended = () => {
                btn.classList.remove('playing');
                btn.classList.add('paused');
            };
        }
    });
    }
    // Handle browser back/forward buttons
    handlePopState(event) {
        if (event.state && event.state.url) {
            // Load content without updating history
            this.fetchContent(event.state.url)
                .then(content => {
                    this.updateContent(content, event.state.url);
                    // Update active state based on current URL
                    const activeLink = this.sidebar.querySelector(`a[href="${event.state.url}"]`);
                    if (activeLink) {
                        this.updateActiveState(activeLink);
                    }
                })
                .catch(error => this.handleError(error, event.state.url));
        }
    }
}
function  runPageInitFunction(route) {
    const fn = window['init_' + route.replace(/-/g, '_')];
    if (typeof fn === 'function') {
        fn();
    } else {
        console.warn('Fonksiyon bulunamadı: init_' + route);
    }
}

// Initialize AJAX navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const ajaxNav = new AjaxNavigation();

    window.addEventListener('popstate', (event) => {
        if (event.state && event.state.url) {
            // Klasik yönlendirme
            window.location.href = event.state.url;
        }
    });
});
